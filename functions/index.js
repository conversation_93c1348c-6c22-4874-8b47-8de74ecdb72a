// AIzaSyB3dt4CBwrgeyPjTKvhaVpXapq1IvmSqmM
// const { sendEmailToTeamMembers, sendEmailToUser } = require("./utils/methods");
const { onCall } = require("firebase-functions/v2/https");
const { onRequest } = require("firebase-functions/v2/https");
// const { HttpsError } = require("firebase-functions/v2/https");
const admin = require("firebase-admin");
const nodemailer = require('nodemailer');
const { FieldValue, Timestamp } = require("firebase-admin/firestore");
const {
    onDocumentWritten,
    onDocumentCreated,
    // onDocumentUpdated,
    // onDocumentDeleted,
    // Change,
    // FirestoreEvent
} = require("firebase-functions/v2/firestore");
const { event } = require("firebase-functions/v1/analytics");
const functions2 = require('firebase-functions/v2');
const functions = require('firebase-functions');

const { VertexAI } = require('@google-cloud/vertexai');


// const { google } = require('googleapis');
// const serviceAccount = require('./service-account-key.json');
// const dateFuncs = require('date-and-time');
admin.initializeApp();
const db = admin.firestore();
const auth = admin.auth();
const messaging = admin.messaging();


// const { PredictionServiceClient } = require('@google-cloud/aiplatform');
// const { helpers } = require('@google-cloud/aiplatform');

var transporter = nodemailer.createTransport({
    host: 'smtp.gmail.com',
    port: 465,
    secure: true,
    auth: {
        user: '<EMAIL>',
        pass: 'skfbnyjbyotmfvmw'
        // pass: 'zniorhjmhmatlzfd'
    }
});
const aiplatform = require('@google-cloud/aiplatform');

// Helper function to generate embeddings
async function generateEmbedding(text) {
    try {
        console.log('Generating embedding for:', text);

        // Use REST API for embeddings
        const { GoogleAuth } = require('google-auth-library');
        const auth = new GoogleAuth({
            scopes: ['https://www.googleapis.com/auth/cloud-platform']
        });

        const authClient = await auth.getClient();
        const accessToken = await authClient.getAccessToken();

        const project = 'wedding-ease-dc99a';
        const location = 'us-central1';
        const model = 'text-embedding-004';

        const url = `https://${location}-aiplatform.googleapis.com/v1/projects/${project}/locations/${location}/publishers/google/models/${model}:predict`;

        const requestBody = {
            instances: [
                {
                    content: text,
                    task_type: "RETRIEVAL_DOCUMENT"
                }
            ],
            parameters: {
                autoTruncate: true
            }
        };

        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${accessToken.token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error('API Error Response:', errorText);
            throw new Error(`API request failed: ${response.status} ${response.statusText} - ${errorText}`);
        }

        const data = await response.json();

        if (data.predictions && data.predictions.length > 0) {
            const prediction = data.predictions[0];
            const embedding = prediction.embeddings?.values;

            if (embedding && Array.isArray(embedding)) {
                console.log("Embedding generated successfully, length:", embedding.length);
                return embedding;
            } else {
                throw new Error("Embedding values not found in prediction");
            }
        } else {
            throw new Error("No predictions returned from the model");
        }

    } catch (error) {
        console.error("Embedding generation error:", error.message);
        throw error;
    }
}

exports.onSubCategoryCreated = onDocumentCreated({
    document: 'subcategories/{subCatId}',
    memory: '512MiB',
    timeoutSeconds: 60
}, async (event) => {
    try {
        const data = event.data.data();
        console.log('Sub-category created:', data.name);

        // Generate embedding for the subcategory name
        const embedding = await generateEmbedding(data.name);

        // Save embedding in separate collection
        await db.collection('subcategoriesembedding').add({
            name: data.name,
            image: data.image || null,
            embedding: embedding, // Store the full 768-dimensional array
            subcategoryId: event.data.id, // Reference to original document
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            embeddingGenerated: true,
            embeddingGeneratedAt: admin.firestore.FieldValue.serverTimestamp()
        });

        // Update original document to indicate embedding was generated
        await db.collection('subcategories').doc(event.data.id).update({
            'embeddingGenerated': true,
            'embeddingGeneratedAt': admin.firestore.FieldValue.serverTimestamp()
        });

        console.log(`Embedding saved for subcategory: ${data.name}`);

    } catch (error) {
        console.error('Error in onSubCategoryCreated:', error);

        // Update original document to indicate embedding generation failed
        try {
            await db.collection('subcategories').doc(event.data.id).update({
                'embeddingGenerated': false,
                'embeddingError': error.message,
                'embeddingErrorAt': admin.firestore.FieldValue.serverTimestamp()
            });
        } catch (updateError) {
            console.error('Failed to update error status:', updateError);
        }
    }
});

// Product embedding generation trigger
exports.onProductCreated = onDocumentCreated({
    document: 'products/{productId}',
    memory: '512MiB',
    timeoutSeconds: 60
}, async (event) => {
    try {
        const data = event.data.data();
        console.log('Product created:', data.name);

        // Create a combined text for embedding (name + description)
        const combinedText = `${data.name} ${data.description || ''}`.trim();

        // Generate embedding for the product
        const embedding = await generateEmbedding(combinedText);

        // Save embedding in separate collection
        await db.collection('productsembedding').add({
            name: data.name,
            image: data.image || null,
            embedding: embedding, // Store the full 768-dimensional array
            productId: event.data.id, // Reference to original document
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            embeddingGenerated: true,
            embeddingGeneratedAt: admin.firestore.FieldValue.serverTimestamp()
        });

        // Update original document to indicate embedding was generated
        await db.collection('products').doc(event.data.id).update({
            'embeddingGenerated': true,
            'embeddingGeneratedAt': admin.firestore.FieldValue.serverTimestamp()
        });

        console.log(`Embedding saved for product: ${data.name}`);

    } catch (error) {
        console.error('Error in onProductCreated:', error);

        // Update original document to indicate embedding generation failed
        try {
            await db.collection('products').doc(event.data.id).update({
                'embeddingGenerated': false,
                'embeddingError': error.message,
                'embeddingErrorAt': admin.firestore.FieldValue.serverTimestamp()
            });
        } catch (updateError) {
            console.error('Failed to update error status:', updateError);
        }
    }
});

// Variant embedding generation trigger
exports.onVariantCreated = onDocumentCreated({
    document: 'variants/{variantId}',
    memory: '512MiB',
    timeoutSeconds: 60
}, async (event) => {
    try {
        const data = event.data.data();
        console.log('Variant created:', data.name || data.title);

        // Create a combined text for embedding (name/title + description + attributes)
        const name = data.name || data.title || '';
        const description = data.description || '';
        const attributes = data.attributes ? Object.values(data.attributes).join(' ') : '';
        const combinedText = `${name} ${description} ${attributes}`.trim();

        // Generate embedding for the variant
        const embedding = await generateEmbedding(combinedText);

        // Save embedding in separate collection
        await db.collection('variantsembedding').add({
            name: name,
            image: data.image || null,
            embedding: embedding, // Store the full 768-dimensional array
            variantId: event.data.id, // Reference to original document
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            embeddingGenerated: true,
            embeddingGeneratedAt: admin.firestore.FieldValue.serverTimestamp()
        });

        // Update original document to indicate embedding was generated
        await db.collection('variants').doc(event.data.id).update({
            'embeddingGenerated': true,
            'embeddingGeneratedAt': admin.firestore.FieldValue.serverTimestamp()
        });

        console.log(`Embedding saved for variant: ${name}`);

    } catch (error) {
        console.error('Error in onVariantCreated:', error);

        // Update original document to indicate embedding generation failed
        try {
            await db.collection('variants').doc(event.data.id).update({
                'embeddingGenerated': false,
                'embeddingError': error.message,
                'embeddingErrorAt': admin.firestore.FieldValue.serverTimestamp()
            });
        } catch (updateError) {
            console.error('Failed to update error status:', updateError);
        }
    }
});
// Function to find similar items across all three collections using embeddings
exports.findSimilarItems = onRequest({
    memory: '512MiB',
    timeoutSeconds: 60
}, async (req, res) => {
    try {
        const { searchQuery } = req.body;

        if (!searchQuery) {
            return res.status(400).json({ error: 'searchQuery is required' });
        }

        // Generate embedding for the input
        const inputEmbedding = await generateEmbedding(searchQuery);

        // Get all items from the three embedding collections
        const [subcategoriesSnapshot, productsSnapshot, variantsSnapshot] = await Promise.all([
            db.collection('subcategoriesembedding').get(),
            db.collection('productsembedding').get(),
            db.collection('variantsembedding').get()
        ]);

        const similarities = [];

        // Process subcategories
        subcategoriesSnapshot.forEach(doc => {
            const data = doc.data();
            if (data.embedding && Array.isArray(data.embedding)) {
                const similarity = cosineSimilarity(inputEmbedding, data.embedding);
                similarities.push({
                    id: data.subcategoryId, // Original document ID
                    embeddingId: doc.id, // Embedding document ID
                    name: data.name,
                    image: data.image,
                    similarity: similarity,
                    type: 'subcategory'
                });
            }
        });

        // Process products
        productsSnapshot.forEach(doc => {
            const data = doc.data();
            if (data.embedding && Array.isArray(data.embedding)) {
                const similarity = cosineSimilarity(inputEmbedding, data.embedding);
                similarities.push({
                    id: data.productId, // Original document ID
                    embeddingId: doc.id, // Embedding document ID
                    name: data.name,
                    image: data.image,
                    similarity: similarity,
                    type: 'product'
                });
            }
        });

        // Process variants
        variantsSnapshot.forEach(doc => {
            const data = doc.data();
            if (data.embedding && Array.isArray(data.embedding)) {
                const similarity = cosineSimilarity(inputEmbedding, data.embedding);
                similarities.push({
                    id: data.variantId, // Original document ID
                    embeddingId: doc.id, // Embedding document ID
                    name: data.name,
                    image: data.image,
                    similarity: similarity,
                    type: 'variant'
                });
            }
        });

        // Sort by similarity (highest first)
        similarities.sort((a, b) => b.similarity - a.similarity);

        // Group results by type for better organization
        const results = {
            query: searchQuery,
            totalResults: similarities.length,
            topResults: similarities.slice(0, 10), // Top 10 overall
            byType: {
                subcategories: similarities.filter(item => item.type === 'subcategory').slice(0, 5),
                products: similarities.filter(item => item.type === 'product').slice(0, 5),
                variants: similarities.filter(item => item.type === 'variant').slice(0, 5)
            }
        };

        res.json(results);

    } catch (error) {
        console.error('Error finding similar items:', error);
        res.status(500).json({ error: error.message });
    }
});

// Helper function to calculate cosine similarity
function cosineSimilarity(vecA, vecB) {
    if (vecA.length !== vecB.length) return 0;

    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    for (let i = 0; i < vecA.length; i++) {
        dotProduct += vecA[i] * vecB[i];
        normA += vecA[i] * vecA[i];
        normB += vecB[i] * vecB[i];
    }

    return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
}

exports.testEmbedding = onRequest({
    memory: '512MiB',
    timeoutSeconds: 60
}, async (req, res) => {
    const text = 'blazer';

    try {
        console.log('Starting embedding test...');

        // Use REST API instead of gRPC client
        const { GoogleAuth } = require('google-auth-library');
        const auth = new GoogleAuth({
            scopes: ['https://www.googleapis.com/auth/cloud-platform']
        });

        const authClient = await auth.getClient();
        const accessToken = await authClient.getAccessToken();

        const project = 'wedding-ease-dc99a';
        const location = 'us-central1';
        const model = 'text-embedding-004';

        const url = `https://${location}-aiplatform.googleapis.com/v1/projects/${project}/locations/${location}/publishers/google/models/${model}:predict`;

        const requestBody = {
            instances: [
                {
                    content: text,
                    task_type: "RETRIEVAL_DOCUMENT"
                }
            ],
            parameters: {
                autoTruncate: true
            }
        };

        console.log('Making REST request to:', url);
        console.log('Request body:', JSON.stringify(requestBody, null, 2));

        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${accessToken.token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error('API Error Response:', errorText);
            throw new Error(`API request failed: ${response.status} ${response.statusText} - ${errorText}`);
        }

        const data = await response.json();
        console.log('Response received:', JSON.stringify(data, null, 2));

        if (data.predictions && data.predictions.length > 0) {
            const prediction = data.predictions[0];
            const embedding = prediction.embeddings?.values;

            if (embedding && Array.isArray(embedding)) {
                console.log("Embedding created successfully, length:", embedding.length);
                res.status(200).json({
                    embedding: embedding.slice(0, 10), // Only return first 10 values for brevity
                    length: embedding.length,
                    success: true,
                    model: 'text-embedding-004',
                    text: text
                });

            } else {
                console.log("Prediction structure:", JSON.stringify(prediction, null, 2));
                throw new Error("Embedding values not found in prediction");
            }
        } else {
            console.log("Full response:", JSON.stringify(data, null, 2));
            throw new Error("No predictions returned from the model");
        }

    } catch (error) {
        console.error("Embedding error:", error.message);
        console.error("Full error:", error);

        // Check if it's a quota/limit error
        if (error.message.includes('quota') || error.message.includes('limit') || error.message.includes('exceeded')) {
            res.status(429).json({
                error: 'API quota exceeded',
                message: error.message,
                suggestion: 'Please check your Google Cloud quotas and billing'
            });
        } else {
            res.status(500).json({
                error: 'Internal server error',
                message: error.message
            });
        }
    }
});
// exports.testEmbedding = onRequest({
//     memory: '512MiB',
//     timeoutSeconds: 60
// }, async (req, res) => {
//     const text = 'blazer';

//     try {
//         console.log('Starting embedding test...');

//         // Use REST API instead of gRPC client
//         const { GoogleAuth } = require('google-auth-library');
//         const auth = new GoogleAuth({
//             scopes: ['https://www.googleapis.com/auth/cloud-platform']
//         });

//         const authClient = await auth.getClient();
//         const accessToken = await authClient.getAccessToken();

//         const project = 'wedding-ease-dc99a';
//         const location = 'us-central1';
//         const model = 'text-embedding-004';

//         const url = `https://${location}-aiplatform.googleapis.com/v1/projects/${project}/locations/${location}/publishers/google/models/${model}:predict`;

//         const requestBody = {
//             instances: [
//                 {
//                     content: text,
//                     task_type: "RETRIEVAL_DOCUMENT"
//                 }
//             ],
//             parameters: {
//                 autoTruncate: true
//             }
//         };

//         console.log('Making REST request to:', url);
//         console.log('Request body:', JSON.stringify(requestBody, null, 2));

//         const response = await fetch(url, {
//             method: 'POST',
//             headers: {
//                 'Authorization': `Bearer ${accessToken.token}`,
//                 'Content-Type': 'application/json'
//             },
//             body: JSON.stringify(requestBody)
//         });

//         if (!response.ok) {
//             const errorText = await response.text();
//             console.error('API Error Response:', errorText);
//             throw new Error(`API request failed: ${response.status} ${response.statusText} - ${errorText}`);
//         }

//         const data = await response.json();
//         console.log('Response received:', JSON.stringify(data, null, 2));

//         if (data.predictions && data.predictions.length > 0) {
//             const prediction = data.predictions[0];
//             const embedding = prediction.embeddings?.values;

//             if (embedding && Array.isArray(embedding)) {
//                 console.log("Embedding created successfully, length:", embedding.length);
//                 res.status(200).json({
//                     embedding: embedding.slice(0, 10), // Only return first 10 values for brevity
//                     length: embedding.length,
//                     success: true,
//                     model: 'text-embedding-004',
//                     text: text
//                 });

//             } else {
//                 console.log("Prediction structure:", JSON.stringify(prediction, null, 2));
//                 throw new Error("Embedding values not found in prediction");
//             }
//         } else {
//             console.log("Full response:", JSON.stringify(data, null, 2));
//             throw new Error("No predictions returned from the model");
//         }

//     } catch (error) {
//         console.error("Embedding error:", error.message);
//         console.error("Full error:", error);

//         // Check if it's a quota/limit error
//         if (error.message.includes('quota') || error.message.includes('limit') || error.message.includes('exceeded')) {
//             res.status(429).json({
//                 error: 'API quota exceeded',
//                 message: error.message,
//                 suggestion: 'Please check your Google Cloud quotas and billing'
//             });
//         } else {
//             res.status(500).json({
//                 error: 'Internal server error',
//                 message: error.message
//             });
//         }
//     }
// });

// exports.createTeamMember = onCall(async (request) => {
//     // let data = req.body;
//     try {
//         console.log("IN CREATE TEAMMENBER");
//         if (!request.auth) return { status: 'error', code: 401, message: 'Not signed in' };
//         let response1 = await auth.createUser({
//             email: request.data.email,
//             password: request.data.password,
//         });
//         console.log('Successfully created new user:', response1.uid);
//         try {
//             let response2 = await db.collection('teamMember').doc(response1.uid).create({
//                 ...request.data.data,
//                 "joiningDate": FieldValue.serverTimestamp(),
//                 // "joiningDate": Date.parse(request.data.joiningDate),
//                 "createdAt": FieldValue.serverTimestamp(),
//             });
//             console.log('User Data saved Successfully');
//             return { "success": true, "msg": response2 };
//         } catch (error) {
//             console.log("Failed to create user doc, need to delete this user again!");
//             await auth.deleteUser(response1.uid);
//             console.log("User deleted successfully!");
//             return { "success": false, "msg": error };
//         }
//     } catch (error) {
//         console.log('Error creating new user:', error);
//         return { "success": false, "msg": error['errorInfo']['message'], "code": error['errorInfo']['code'] };
//     }
// });




// const fields = [
//     { fieldPath: 'combi.color', mode: 'DESCENDING' },
//     { fieldPath: 'combi.size', mode: 'DESCENDING' },
// ];




exports.createTeamMember = onCall({
    memory: '512MiB',
    timeoutSeconds: 60
}, async (request) => {
    // let data = req.body;
    try {
        console.log("IN CREATE TEAM MEMBER");
        // if (!request.auth) return { status: 'error', code: 401, message: 'Not signed in' };
        let response1 = await auth.createUser({
            email: request.data.email,
            password: request.data.password,
        });
        console.log('Successfully created new user:', response1.uid);
        try {
            let response2 = await db.collection('teamMember').doc(response1.uid).create({
                'name': request.data.name,
                'email': request.data.email,
                'phone': request.data.phone,
                'createdAt': FieldValue.serverTimestamp(),
                'address': request.data.address,
                'subcatids': [],
                'userType': request.data.userType,
                'speciality': request.data.speciality,
                'password': request.data.password,
                'isActive': true,
            });
            console.log('User Data saved Successfully');
            sendEmailToTeamMembers(request.data.email, request.data.password);
            return { "success": true, "msg": response2 };
        } catch (error) {
            // console.log("Failed to create user doc, need to delete this user again!");
            // await auth.deleteUser(response1.uid);
            // console.log("User deleted successfully!");
            console.log(error);
            return { "success": false, "msg": error };
        }
    } catch (error) {
        console.log('Error creating new user:', error);
        return { "success": false, "msg": error['errorInfo']['message'], "code": error['errorInfo']['code'] };
    }
});
exports.createTeamMemberTest = onCall({
    memory: '512MiB',
    timeoutSeconds: 60
}, async (request) => {
    // let data = req.body;
    try {
        console.log("IN Test CREATE TEAM MEMBER");
        // if (!request.auth) return { status: 'error', code: 401, message: 'Not signed in' };
        let response1 = await auth.createUser({
            email: request.data.email,
            password: request.data.password,
        });
        console.log('Successfully created new user:', response1.uid);
        try {
            let response2 = await db.collection('teamMemberTest').doc(response1.uid).create({
                'name': request.data.name,
                'email': request.data.email,
                'phone': request.data.phone,
                'createdAt': FieldValue.serverTimestamp(),
                'address': request.data.address,
                'subcatids': [],
                'userType': request.data.userType,
                'speciality': request.data.speciality,
                'password': request.data.password,
                'isActive': true,
            });
            console.log('User Data saved Successfully');
            sendEmailToTeamMembers(request.data.email, request.data.password);
            return { "success": true, "msg": response2 };
        } catch (error) {
            // console.log("Failed to create user doc, need to delete this user again!");
            // await auth.deleteUser(response1.uid);
            // console.log("User deleted successfully!");
            console.log(error);
            return { "success": false, "msg": error };
        }
    } catch (error) {
        console.log('Error creating new user:', error);
        return { "success": false, "msg": error['errorInfo']['message'], "code": error['errorInfo']['code'] };
    }
});

exports.deleteTeamMember = onCall({
    memory: '512MiB',
    timeoutSeconds: 60
}, async (request) => {
    try {
        console.log(request.data);
        try {
            await admin.auth().deleteUser(request.data.uid);
        } catch (error) {
            console.log(`Error deleting ${request.data.uid}`);
        }
        await admin.firestore().collection('teamMember').doc(request.data.uid).delete().then((val) => {
            console.log(`Deleted ${request.data.uid}`);
            return { "success": true };
        });
    } catch (error) {
        console.log(error);
        return { "success": false };
    }
});
exports.deleteTeamMemberTest = onCall({
    memory: '512MiB',
    timeoutSeconds: 60
}, async (request) => {
    try {
        console.log(request.data);
        try {
            await admin.auth().deleteUser(request.data.uid);
        } catch (error) {
            console.log(`Error deleting ${request.data.uid}`);
        }
        await admin.firestore().collection('teamMemberTest').doc(request.data.uid).delete().then((val) => {
            console.log(`Deleted ${request.data.uid}`);
            return { "success": true };
        });
    } catch (error) {
        console.log(error);
        return { "success": false };
    }
});

exports.verifyPayment = onCall({
    memory: '512MiB',
    timeoutSeconds: 60
}, async (request) => {
    try {
        console.log('In Verify Payment');


        // console.log(request.data);
        // try {
        //     await admin.auth().deleteUser(request.data.uid);
        // } catch (error) {
        //     console.log(`Error deleting ${request.data.uid}`);
        // }
        // await admin.firestore().collection('teamMember').doc(request.data.uid).delete().then((val) => {
        //     console.log(`Deleted ${request.data.uid}`);
        //     return { "success": true };
        // });
    } catch (error) {
        console.log(error);
        return { "success": false };
    }
});


exports.createVendor = onCall({
    memory: '512MiB',
    timeoutSeconds: 60
}, async (request) => {
    // let data = req.body;
    try {
        console.log("IN CREATE Vendor");
        // if (!request.auth) return { status: 'error', code: 401, message: 'Not signed in' };
        let response1 = await auth.createUser({
            email: request.data.email,
            password: request.data.password,
        });
        console.log('Successfully created new user:', response1.uid);
        try {
            let response2 = await db.collection('vendors').doc(response1.uid).create({
                'name': request.data.name,
                'email': request.data.email,
                'phone': request.data.phone,
                'createdAt': FieldValue.serverTimestamp(),
                'address': request.data.address,
                'subcatids': [],
                'userType': request.data.userType,
                'password': request.data.password,
                'isActive': true,
                'city': request.data.city,
                'state': request.data.state,
                'lastUpdatedOn': FieldValue.serverTimestamp(),
                'level': request.data.level,
                'websiteLink': null,
                'serviceDescription': request.data.serviceDescription,
            });
            console.log('User Data saved Successfully');
            sendEmailToTeamMembers(request.data.email, request.data.password);
            return { "success": true, "msg": response2 };
        } catch (error) {
            // console.log("Failed to create user doc, need to delete this user again!");
            // await auth.deleteUser(response1.uid);
            // console.log("User deleted successfully!");
            console.log(error);
            return { "success": false, "msg": error };
        }
    } catch (error) {
        console.log('Error creating new user:', error);
        return { "success": false, "msg": error['errorInfo']['message'], "code": error['errorInfo']['code'] };
    }
}); exports.createVendorTest = onCall({
    memory: '512MiB',
    timeoutSeconds: 60
}, async (request) => {
    // let data = req.body;
    try {
        console.log("IN Test CREATE Vendor");
        // if (!request.auth) return { status: 'error', code: 401, message: 'Not signed in' };
        let response1 = await auth.createUser({
            email: request.data.email,
            password: request.data.password,
        });
        console.log('Successfully created new user:', response1.uid);
        try {
            let response2 = await db.collection('vendorsTest').doc(response1.uid).create({
                'name': request.data.name,
                'email': request.data.email,
                'phone': request.data.phone,
                'createdAt': FieldValue.serverTimestamp(),
                'address': request.data.address,
                'subcatids': [],
                'userType': request.data.userType,
                'password': request.data.password,
                'isActive': true,
                'city': request.data.city,
                'state': request.data.state,
                'lastUpdatedOn': FieldValue.serverTimestamp(),
                'level': request.data.level,
                'websiteLink': null,
                'serviceDescription': request.data.serviceDescription,
            });
            console.log('User Data saved Successfully');
            sendEmailToTeamMembers(request.data.email, request.data.password);
            return { "success": true, "msg": response2 };
        } catch (error) {
            // console.log("Failed to create user doc, need to delete this user again!");
            // await auth.deleteUser(response1.uid);
            // console.log("User deleted successfully!");
            console.log(error);
            return { "success": false, "msg": error };
        }
    } catch (error) {
        console.log('Error creating new user:', error);
        return { "success": false, "msg": error['errorInfo']['message'], "code": error['errorInfo']['code'] };
    }
});

exports.deleteVendor = onCall({
    memory: '512MiB',
    timeoutSeconds: 60
}, async (request) => {
    try {
        console.log(request.data);
        try {
            await admin.auth().deleteUser(request.data.uid);
        } catch (error) {
            console.log(`Error deleting vendor ${request.data.uid}`);
        }
        await admin.firestore().collection('vendors').doc(request.data.uid).delete().then((val) => {
            console.log(`Deleted ${request.data.uid}`);
            return { "success": true };
        });
    } catch (error) {
        console.log(error);
        return { "success": false };
    }
}); exports.deleteVendorTest = onCall({
    memory: '512MiB',
    timeoutSeconds: 60
}, async (request) => {
    try {
        console.log(request.data);
        try {
            await admin.auth().deleteUser(request.data.uid);
        } catch (error) {
            console.log(`Error deleting vendor ${request.data.uid}`);
        }
        await admin.firestore().collection('vendorsTest').doc(request.data.uid).delete().then((val) => {
            console.log(`Deleted ${request.data.uid}`);
            return { "success": true };
        });
    } catch (error) {
        console.log(error);
        return { "success": false };
    }
});

exports.notifies = onDocumentWritten(
    // {
    //     document: ,
    //     memory: '512MiB',
    //     timeoutSeconds: 60
    // },
    'notifies/{userId}',
    async (event) => {
        try {
            const data = event.data.after.data();
            console.log(data.test == true ? "Test Noti..." : "Global Noti...");
            console.log("data.topic");
            console.log(data.topic);
            const payload = {
                // topic: 'test',
                topic: data.test == true ? 'test' : data.topic,
                // topic: data.test !=null? 'test' : 'global',
                notification: {
                    title: data.title,
                    body: data.desc
                },
                android: {
                    priority: "high",
                    notification: {
                        channel_id: "kadjicare"
                    }
                },
                apns: {
                    headers: {
                        'apns-priority': "10",
                    },
                    payload: {
                        aps: {
                            alert: {
                                title: data.title,
                                body: data.desc,
                            },
                            sound: "default",
                        }
                    }
                },
                data: {
                    title: data.title,
                    body: data.desc
                }
            };
            messaging.send(payload).then((response) => {
                // Response is a message ID string.
                console.log('Successfully sent message:', response);
                return { success: true };
            }).catch((error) => {
                console.log('Error:', error.code);
                return { error: error.code };
            });

        } catch (error) {
            console.log(error);
        }
    });


exports.sendBookingDetails = onCall({
    memory: '512MiB',
    timeoutSeconds: 60
}, async (request) => {
    try {
        console.log('inside booking details');
        const emailText = `<html>
       <body>
       <p>Dear ${request.data.fullnameAtBooking},</p>
       <p>Thank you for choosing <strong>WEDDING-EASE</strong>. Below are your meeting details.</p>
       <br>
       <p><strong>Date: </strong>${request.data.bookingDate}</p>
        <p><strong>Time: </strong>${request.data.bookingstartTime} - ${request.data.bookingendTime}</p>
       <p><strong>Venue: </strong>Online (Microsoft Teams)</p>
       </body>
       </html>`;
        console.log('Email Written');

        sendEmailToUser(request.data.emailAtBooking, "WEDDING-EASE - Booking Confirmation", emailText);
        console.log('Email Sent');
    } catch (error) {
        console.log('Error===========')
        console.log(error);

    }
});

exports.sendOtpEmail = onCall({
    memory: '512MiB',
    timeoutSeconds: 60
}, async (request) => {
    const emailText = `<html>
    <body>
    <p>${request.data.otp} is your OTP. Valid for 5 minutes.</p>
    </body>
    </html>`;
    sendEmailToUser(request.data.email, "WEDDING-EASE", emailText);
});





// exports.tester = onRequest(async (request, response) => {
//     try {
//         console.log(request.body);
//         const myHeaders = new Headers();
//         myHeaders.append("Content", "<>");
//         myHeaders.append("X-Shopify-Access-Token", "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");
//         myHeaders.append("Authorization", "Bearer 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");

//         const requestOptions = {
//             method: "GET",
//             headers: myHeaders,
//             redirect: "follow"
//         };

//         fetch("https://c031a7-d8.myshopify.com/admin/products/count.json", requestOptions)
//             .then((response) => response.text())
//             .then((result) => console.log(result))
//             .catch((error) => console.error(error));
//     } catch (error) {
//         console.log('Error===========')
//         console.log(error);

//     }
// });


/* OLD EVENTS DATA

const SCOPES = ['https://www.googleapis.com/auth/calendar'];
const jwtClient = new google.auth.JWT(
    serviceAccount.client_email,
    null,
    serviceAccount.private_key,
    SCOPES
);

const calendar = google.calendar({ version: 'v3', auth: jwtClient });


exports.createMeetEvent = functions.https.onRequest(async (req, res) => {
    try {
        const { summary, description, startDateTime, endDateTime, attendees } = req.body;

        const event = {
            summary,
            description,
            start: {
                dateTime: startDateTime,
                timeZone: 'America/Los_Angeles',
            },
            end: {
                dateTime: endDateTime,
                timeZone: 'America/Los_Angeles',
            },
            attendees: attendees.map(email => ({ email })),
            conferenceData: {
                createRequest: {
                    requestId: Math.random().toString(36).substring(2),
                    conferenceSolutionKey: { type: 'hangoutsMeet' },
                },
            },
        };

        const response = await calendar.events.insert({
            calendarId: 'primary',
            resource: event,
            conferenceDataVersion: 1,
        });

        const meetLink = response.data.conferenceData.entryPoints
            ? response.data.conferenceData.entryPoints.find(entry => entry.entryPointType === 'video').uri
            : null;
        await admin.firestore.collection('meetings').doc().create({
            'eventId': response.data.id,
            'meetLink': meetLink,
            'htmlLink': response.data.htmlLink,
        });
        res.status(200).json({
            eventId: response.data.id,
            meetLink,
            htmlLink: response.data.htmlLink,
        });
    } catch (error) {
        console.error('Error creating event:', error);
        res.status(500).send('Failed to create event');
    }
});


exports.editMeetEvent = functions.https.onRequest(async (req, res) => {
    try {
        const { eventId, summary, description, startDateTime, endDateTime, attendees } = req.body;

        const eventUpdates = {
            summary,
            description,
            start: {
                dateTime: startDateTime,
                timeZone: 'America/Los_Angeles',
            },
            end: {
                dateTime: endDateTime,
                timeZone: 'America/Los_Angeles',
            },
            attendees: attendees.map(email => ({ email })),
        };

        const response = await calendar.events.patch({
            calendarId: 'primary',
            eventId,
            resource: eventUpdates,
            conferenceDataVersion: 1,
        });

        const meetLink = response.data.conferenceData && response.data.conferenceData.entryPoints
            ? response.data.conferenceData.entryPoints.find(entry => entry.entryPointType === 'video').uri
            : null;

        res.status(200).json({
            eventId: response.data.id,
            meetLink,
            htmlLink: response.data.htmlLink,
        });
    } catch (error) {
        console.error('Error updating event:', error);
        res.status(500).send('Failed to update event');
    }
});


exports.deleteMeetEvent = functions.https.onRequest(async (req, res) => {
    try {
        const { eventId } = req.body;

        await calendar.events.delete({
            calendarId: 'primary',
            eventId,
        });

        res.status(200).json({ message: 'Event deleted successfully', eventId });
    } catch (error) {
        console.error('Error deleting event:', error);
        res.status(500).send('Failed to delete event');
    }
}); */

const path = require('path');
const { google } = require('googleapis');
const { JWT } = require('google-auth-library');
const SCOPES = ['https://www.googleapis.com/auth/calendar'];
const serviceAccount = require('./service-account-key.json');

const jwtClient = new JWT({
    email: serviceAccount.client_email,
    key: serviceAccount.private_key,
    scopes: SCOPES
});



exports.createGoogleMeet = onCall({
    memory: '512MiB',
    timeoutSeconds: 60
}, async (data) => {

    // return { message: "It deployed!" };

    // Optional: Enforce authentication
    console.log('object1=====');
    // if (!context.auth) {
    //     console.log('No Auth=====');

    //     throw new functions2.https.HttpsError(
    //         'unauthenticated',
    //         'User must be authenticated to schedule a meeting.'
    //     );

    // }
    console.log('auth succesfull=====');

    try {
        console.log('object1.1=====');
        // console.log(typeof serviceAccount.private_key, serviceAccount.private_key.length);
        // console.log(serviceAccount.client_email);
        // console.log(serviceAccount.private_key);
        await jwtClient.authorize(); // <--- Important!
        console.log('let');
        const calendar = google.calendar({ version: 'v3', auth: jwtClient });
        const calendarList = await calendar.calendarList.list();
        console.log(calendarList.data.items);
        console.log('object2=====');
        console.log('Incoming data:', data.data);
        const { summary, startDateTime, endDateTime, attendees } = data.data;
        console.log('object3=====');
        // attendees = Array.isArray(data.attendees) ? data.attendees : []
        if (!Array.isArray(attendees)) {
            throw new functions2.https.HttpsError('invalid-argument', 'Attendees must be an array');
        }
        console.log(attendees.length);
        const event = {
            summary,
            start: { dateTime: startDateTime, timeZone: 'UTC' },
            end: { dateTime: endDateTime, timeZone: 'UTC' },
            attendees: attendees.map(email => ({ email })),
            conferenceData: {
                createRequest: {
                    requestId: `meet-${Date.now()}`,
                    conferenceSolutionKey: { type: 'hangoutsMeet' }
                }
            }
        }; const list = await calendar.calendarList.list();
        console.log("Service Account Calendars:");
        list.data.items.forEach(cal => {
            console.log(`- ${cal.summary} [${cal.id}]`);
        });
        console.log('object4=====');

        const response = await calendar.events.insert({
            calendarId: '<EMAIL>', //'primary',
            resource: event,
            conferenceDataVersion: 1,
        });
        console.log('object5=====');

        const meetLink = response.data.conferenceData?.entryPoints?.find(
            entry => entry.entryPointType === 'video'
        )?.uri;
        console.log('object6=====');
        console.log(meetLink);

        return {
            meetLink,
            eventId: response.data.id,
            htmlLink: response.data.htmlLink
        };
    } catch (error) {
        console.log('Error=====');

        console.error(error);
        throw new functions2.https.HttpsError(
            'internal',
            'Error creating Google Meet event'
        );
    }
});

exports.editGoogleMeet = onCall({
    memory: '512MiB',
    timeoutSeconds: 60
}, async (data, context) => {
    // Optional: Enforce authentication
    if (!context.auth) {
        throw new functions2.https.HttpsError(
            'unauthenticated',
            'User must be authenticated to edit a meeting.'
        );
    }
    try {
        const { eventId, summary, startDateTime, endDateTime, attendees } = data;

        const eventUpdates = {
            summary,
            start: { dateTime: startDateTime, timeZone: 'UTC' },
            end: { dateTime: endDateTime, timeZone: 'UTC' },
            attendees: attendees.map(email => ({ email })),
        };

        const response = await calendar.events.patch({
            calendarId: '<EMAIL>', //'primary',
            eventId,
            resource: eventUpdates,
            conferenceDataVersion: 1,
        });

        // Extract updated Meet link if present
        const meetLink = response.data.conferenceData?.entryPoints?.find(
            (entry) => entry.entryPointType === 'video'
        )?.uri;

        return {
            meetLink,
            eventId: response.data.id,
            htmlLink: response.data.htmlLink,
        };
    } catch (error) {
        console.error(error);
        throw new functions2.https.HttpsError(
            'internal',
            'Error editing Google Meet event'
        );
    }
});

exports.deleteGoogleMeet = onCall({
    memory: '512MiB',
    timeoutSeconds: 60
}, async (data, context) => {
    // Optional: Enforce authentication
    if (!context.auth) {
        throw new functions2.https.HttpsError(
            'unauthenticated',
            'User must be authenticated to delete a meeting.'
        );
    }
    try {
        const { eventId } = data;

        await calendar.events.delete({
            calendarId: 'primary',
            eventId,
        });

        return { message: 'Event deleted successfully', eventId };
    } catch (error) {
        console.error(error);
        throw new functions2.https.HttpsError(
            'internal',
            'Error deleting Google Meet event'
        );
    }
});




async function sendEmailToTeamMembers(to, pass) {
    try {
        sendEmailToUser(to, "Wedding-Ease", `<html>
        <body>
        <p>Welcome to Wedding-Ease!</p>
        <p>Your login credentials are:</p>
        <p>Email: ${to}</p> 
        <p>Password: ${pass}</p> 
        </body>
        </html>`);
    } catch (error) {
        console.log(error);
    }
}
async function sendEmailToUser(to, subject, html) {
    try {
        const mailOptions = {
            from: {
                name: 'Wedding-Ease',
                address: '<EMAIL>'
            },
            to: to,
            subject: subject,
            html: html
        };
        return transporter.sendMail(mailOptions, (error, data) => {
            if (error) {
                console.log(error)
                return
            }
            console.log("Sent!")
        });
    } catch (error) {
        console.log(error);
    }
}
