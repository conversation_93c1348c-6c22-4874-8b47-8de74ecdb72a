async function createFirestoreIndex(projectId, databaseId, collectionGroup, fields) {
    // const firestore = new db({
    //     projectId: projectId,
    // });
    console.log("Admin object:", admin);
    console.log("DB object:", db);
    const parent = db.databasePath(projectId, databaseId);

    const index = {
        fields: fields,
        collectionGroup: collectionGroup,
    };

    try {
        const [operation] = await db.createIndex({
            parent: parent,
            index: index,
        });

        console.log(`Index creation started: ${operation.name}`);

        // To wait for the operation to complete:
        const [result] = await operation.promise();
        console.log('Index creation complete');
        console.log(result);

    } catch (error) {
        console.error('Error creating index:', error);
    }
}

