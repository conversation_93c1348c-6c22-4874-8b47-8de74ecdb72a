import 'package:cloud_firestore/cloud_firestore.dart';

class BookingsModel {
  final String docID;
  final String uId;
  final DateTime createdAt;
  final String createdBy;
  final String createdByDocId;
  final String? teamMemberId;
  final String? stylerId;
  final String? sessionId;
  final String? messageId;
  final DateTime bookingDate;
  final DateTime bookingstartTime;
  final DateTime bookingendTime;
  final int slotgap;
  final String teamsLink;
  final List<String> productIds;
  final String? teamMemberComment;
  final String? emailAtBooking;
  final String? fullnameAtBooking;
  final String? mobileAtBooking;
  final String bookingComment;
  final bool isCompleted;
  final DateTime? completedAt;
  final DateTime? cancelledAt;
  final bool isCancelled;
  final String? cancelledReason;

  BookingsModel({
    this.emailAtBooking,
    this.fullnameAtBooking,
    this.mobileAtBooking,
    required this.docID,
    required this.uId,
    required this.createdAt,
    required this.createdBy,
    required this.createdByDocId,
    required this.teamMemberId,
    required this.stylerId,
    required this.sessionId,
    required this.messageId,
    required this.bookingDate,
    required this.bookingstartTime,
    required this.bookingendTime,
    required this.slotgap,
    required this.teamsLink,
    required this.productIds,
    required this.teamMemberComment,
    required this.bookingComment,
    required this.isCompleted,
    required this.completedAt,
    required this.cancelledAt,
    required this.isCancelled,
    required this.cancelledReason,
  });

  /// Convert a BookingsModel instance to JSON
  Map<String, dynamic> toJson() {
    return {
      'docID': docID,
      'uId': uId,
      'createdAt': createdAt,
      'createdByDocId': createdByDocId,
      'createdBy': createdBy,
      'teamMemberId': teamMemberId,
      'stylerId': stylerId,
      'sessionId': sessionId,
      'messageId': messageId,
      'bookingDate': bookingDate,
      'bookingstartTime': bookingstartTime,
      'bookingendTime': bookingendTime,
      'slotgap': slotgap,
      'teamsLink': teamsLink,
      'productIds': productIds,
      'teamMemberComment': teamMemberComment,
      'bookingComment': bookingComment,
      'isCompleted': isCompleted,
      'completedAt': completedAt,
      'cancelledAt': cancelledAt,
      'isCancelled': isCancelled,
      'cancelledReason': cancelledReason,
      'fullnameAtBooking': fullnameAtBooking,
      'emailAtBooking': emailAtBooking,
      'mobileAtBooking': mobileAtBooking,
    };
  }

  /// Create a BookingsModel instance from JSON
  factory BookingsModel.fromJson(Map<String, dynamic> json) {
    try {
      return BookingsModel(
        docID: json['docID'],
        uId: json['uId'],
        createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt']),
        createdBy: json['createdBy'],
        createdByDocId: json['createdByDocId'],
        teamMemberId: json['teamMemberId'],
        stylerId: json['stylerId'],
        sessionId: json['sessionId'],
        messageId: json['messageId'],
        bookingDate: DateTime.fromMillisecondsSinceEpoch(json['bookingDate']),
        bookingstartTime:
            DateTime.fromMillisecondsSinceEpoch(json['bookingstartTime']),
        bookingendTime:
            DateTime.fromMillisecondsSinceEpoch(json['bookingendTime']),
        slotgap: json['slotgap'],
        teamsLink: json['teamsLink'],
        productIds: List<String>.from(json['productIds']),
        teamMemberComment: json['teamMemberComment'],
        bookingComment: json['bookingComment'],
        isCompleted: json['isCompleted'],
        completedAt: DateTime.fromMillisecondsSinceEpoch(json['completedAt']),
        cancelledAt: DateTime.fromMillisecondsSinceEpoch(json['cancelledAt']),
        isCancelled: json['isCancelled'],
        cancelledReason: json['cancelledReason'],
      );
    } catch (e) {
      print(
          'Error parsing BookingsModel.fromJson for docID: ${json['docID']}, error: $e');
      rethrow; // Skip this entry by rethrowing the error
    }
  }

  /// Create a BookingsModel instance from Firestore QueryDocumentSnapshot
  factory BookingsModel.fromSnap(
      QueryDocumentSnapshot<Map<String, dynamic>> json) {
    try {
      return BookingsModel(
        docID: json.id,
        uId: json['uId'],
        createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt']),
        createdBy: json['createdBy'],
        createdByDocId: json['createdByDocId'],
        teamMemberId: json['teamMemberId'],
        stylerId: json['stylerId'],
        sessionId: json['sessionId'],
        messageId: json['messageId'],
        bookingDate: DateTime.fromMillisecondsSinceEpoch(json['bookingDate']),
        bookingstartTime:
            DateTime.fromMillisecondsSinceEpoch(json['bookingstartTime']),
        bookingendTime:
            DateTime.fromMillisecondsSinceEpoch(json['bookingendTime']),
        slotgap: json['slotgap'],
        teamsLink: json['teamsLink'],
        productIds: List<String>.from(json['productIds']),
        teamMemberComment: json['teamMemberComment'],
        bookingComment: json['bookingComment'],
        isCompleted: json['isCompleted'],
        completedAt: json['completedAt'] != null
            ? DateTime.fromMillisecondsSinceEpoch(json['completedAt'])
            : null,
        cancelledAt: json['cancelledAt'] != null
            ? DateTime.fromMillisecondsSinceEpoch(json['cancelledAt'])
            : null,
        isCancelled: json['isCancelled'],
        cancelledReason: json['cancelledReason'],
        emailAtBooking: json['emailAtBooking'],
        fullnameAtBooking: json['fullnameAtBooking'],
        mobileAtBooking: json['mobileAtBooking'],
      );
    } catch (e) {
      print(
          'Error parsing BookingsModel.fromSnap for docID: ${json.id}, error: $e');
      rethrow; // Skip this entry by rethrowing the error
    }
  }

  /// Create a BookingsModel instance from Firestore DocumentSnapshot
  factory BookingsModel.fromDocSnap(
      DocumentSnapshot<Map<String, dynamic>> json) {
    try {
      return BookingsModel(
        docID: json.id,
        uId: json['uId'],
        createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt']),
        createdBy: json['createdBy'],
        teamMemberId: json['teamMemberId'],
        stylerId: json['stylerId'],
        createdByDocId: json['createdByDocId'],
        sessionId: json['sessionId'],
        messageId: json['messageId'],
        bookingDate: DateTime.fromMillisecondsSinceEpoch(json['bookingDate']),
        bookingstartTime:
            DateTime.fromMillisecondsSinceEpoch(json['bookingstartTime']),
        bookingendTime:
            DateTime.fromMillisecondsSinceEpoch(json['bookingendTime']),
        slotgap: json['slotgap'],
        teamsLink: json['teamsLink'],
        productIds: List<String>.from(json['productIds']),
        teamMemberComment: json['teamMemberComment'],
        bookingComment: json['bookingComment'],
        isCompleted: json['isCompleted'],
        completedAt: json['completedAt'] == null
            ? null
            : DateTime.fromMillisecondsSinceEpoch(json['completedAt']),
        cancelledAt: json['cancelledAt'] == null
            ? null
            : DateTime.fromMillisecondsSinceEpoch(json['cancelledAt']),
        isCancelled: json['isCancelled'],
        cancelledReason: json['cancelledReason'],
        emailAtBooking: json['emailAtBooking'],
        fullnameAtBooking: json['fullnameAtBooking'],
        mobileAtBooking: json['mobileAtBooking'],
      );
    } catch (e) {
      print(
          'Error parsing BookingsModel.fromDocSnap for docID: ${json.id}, error: $e');
      rethrow; // Skip this entry by rethrowing the error
    }
  }
}

//  'emailAtBooking': '',
//                               'fullnameAtBooking': '',
//                               'mobileAtBooking': '',
class BlockedSlotModel {
  final String docId;
  final bool dateBlock;
  final DateTime blockedDateTime;
  final DateTime createdAt;
  final int slotGap;

  BlockedSlotModel({
    required this.docId,
    required this.blockedDateTime,
    required this.dateBlock,
    required this.createdAt,
    required this.slotGap,
  });
  factory BlockedSlotModel.fromSnap(
      QueryDocumentSnapshot<Map<String, dynamic>> json) {
    return BlockedSlotModel(
      docId: json.id,
      blockedDateTime:
          DateTime.fromMillisecondsSinceEpoch(json['blockedDateTime']),
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt']),
      dateBlock: json['dateBlock'],
      slotGap: json['slotGap'],
    );
  }
}
