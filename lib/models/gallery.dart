import 'package:cloud_firestore/cloud_firestore.dart';

class GalleryModel {
  final String docId;
  final String image;
  final int priorityNo;

  GalleryModel({
    required this.docId,
    required this.image,
    required this.priorityNo,
  });

  // Converts a TestimonialsModel instance to a JSON map for Firestore
  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'image': image,
      'priorityNo': priorityNo,
    };
  }

  // Creates a TestimonialsModel instance from a JSON map (useful when fetching data)
  factory GalleryModel.fromJson(Map<String, dynamic> json) {
    try {
      return GalleryModel(
        docId: json['docId'],
        image: json['image'],
        priorityNo: json['priorityNo'],
      );
    } catch (e) {
      print(
          'Error parsing GalleryModel.fromJson for docId: ${json['docId']}, error: $e');
      rethrow; // Skip this entry by rethrowing the error
    }
  }

  // Creates a TestimonialsModel instance from a Firestore document snapshot
  factory GalleryModel.fromSnap(
      QueryDocumentSnapshot<Map<String, dynamic>> data) {
    try {
      return GalleryModel(
        docId: data.id,
        image: data['image'],
        priorityNo: data['priorityNo'],
      );
    } catch (e) {
      print(
          'Error parsing GalleryModel.fromSnap for docId: ${data.id}, error: $e');
      rethrow; // Skip this entry by rethrowing the error
    }
  }
}
