class ApiResponse {
  int? statusCode;
  bool? success;
  String? message;
  dynamic data;
  ApiResponse({this.statusCode, this.success, this.message, this.data});

  factory ApiResponse.fromJson(Map<String, dynamic> json) {
    try {
      return ApiResponse(
        statusCode: json["statusCode"],
        success: json["isSuccess"],
        message: json["message"],
        data: json["data"],
      );
    } catch (e) {
      print('Error parsing ApiResponse.fromJson, error: $e');
      rethrow; // Skip this entry by rethrowing the error
    }
  }

  Map<String, dynamic> toJson() => {
        "statusCode": statusCode,
        "message": message,
        "data": data,
      };
}
