import 'package:cloud_firestore/cloud_firestore.dart';

class OfferingsModel {
  final String docId;
  final String name;
  final String desc;
  final String image;
  final Timestamp createdAt;
  final int priorityNo;
  final bool isActive;
  final List<String> combinationNames;
  final List<String> tags;

  OfferingsModel({
    required this.docId,
    required this.name,
    required this.desc,
    required this.image,
    required this.createdAt,
    required this.priorityNo,
    required this.isActive,
    required this.combinationNames,
    required this.tags,
  });

  // Converts an OfferingsModel instance to a JSON map for Firestore
  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'name': name,
      'desc': desc,
      'image': image,
      'createdAt': createdAt,
      'priorityNo': priorityNo,
      'isActive': isActive,
      'combinationNames': combinationNames,
      'tags': tags,
    };
  }

  // Creates an OfferingsModel instance from a JSON map (useful when fetching data)
  factory OfferingsModel.fromJson(Map<String, dynamic> json) {
    try {
      return OfferingsModel(
        docId: json['docId'],
        name: json['name'],
        desc: json['desc'],
        image: json['image'],
        createdAt: json['createdAt'] as Timestamp,
        priorityNo: int.tryParse(json['priorityNo']) ?? 0,
        combinationNames: List<String>.from(json['combinationNames']),
        isActive: json['isActive'],
        tags: json['tags'],
      );
    } catch (e) {
      print(
          'Error parsing OfferingsModel.fromJson for docId: ${json['docId']}, error: $e');
      rethrow; // Skip this entry by rethrowing the error
    }
  }

  // Creates an OfferingsModel instance from a Firestore document snapshot
  factory OfferingsModel.fromSnap(
      QueryDocumentSnapshot<Map<String, dynamic>> data) {
    try {
      return OfferingsModel(
        docId: data.id,
        name: data['name'],
        desc: data['desc'],
        image: data['image'],
        createdAt: data['createdAt'] as Timestamp,
        priorityNo: int.tryParse(data['priorityNo']) ?? 0,
        combinationNames: List<String>.from(data['combinationNames']),
        isActive: data['isActive'],
        tags: data.data().containsKey('tags')
            ? List<String>.from(data['tags'])
            : [],
      );
    } catch (e) {
      print(
          'Error parsing OfferingsModel.fromSnap for docId: ${data.id}, error: $e');
      rethrow; // Skip this entry by rethrowing the error
    }
  }
}
