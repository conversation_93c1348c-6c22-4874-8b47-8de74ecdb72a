import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:wedding_super_admin/models/address_model.dart';

class OrderModel {
  final String docId;
  final String orderId;
  final DateTime createdAt;
  final String createdByDocId;
  final String uid;
  final String sessionId;
  final bool? userConfirmed;
  final bool isPaid;
  final DateTime? paidOn;
  final DateTime? statusUpdatedAt;
  final String? statusUpdatedBy;
  final DateTime? userConfirmedOn;
  final List<ChargeModel> charges;
  final List<OrderProductData> orderProductData;
  // final DateTime? updatedAt;
  // final String? updatedBy;
  // final String? updatedByDocId;
  final bool isDirectOrder;
  final num totalAmount;
  final num paidAmount;
  final String? address;
  final String orderStatus;
  final List<String> previousDocIds;
  final String? rejectionReason;

  OrderModel({
    required this.docId,
    required this.orderId,
    required this.createdAt,
    required this.createdByDocId,
    required this.uid,
    required this.userConfirmed,
    required this.isPaid,
    required this.sessionId,
    required this.isDirectOrder,
    this.paidOn,
    this.statusUpdatedAt,
    this.statusUpdatedBy,
    this.userConfirmedOn,
    required this.charges,
    required this.orderProductData,
    // this.updatedAt,
    // this.updatedBy,
    // this.updatedByDocId,
    required this.paidAmount,
    required this.totalAmount,
    this.address,
    required this.orderStatus,
    required this.previousDocIds,
    required this.rejectionReason,
  });
  // Convert JSON to OrderModel
  static OrderModel? fromJson(Map<String, dynamic> json) {
    try {
      return OrderModel(
        docId: json['docId'] ?? '',
        orderId: json['orderId'] ?? '',
        createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt']),
        createdByDocId: json['createdByDocId'] ?? '',
        uid: json['uid'] ?? '',
        sessionId: json['sessionId'] ?? '',
        userConfirmed: json['userConfirmed'],
        isDirectOrder: json['isDirectOrder'],
        isPaid: json['isPaid'] ?? false,
        paidOn: json['paidOn'] != null
            ? DateTime.fromMillisecondsSinceEpoch(json['paidOn'])
            : null,
        statusUpdatedAt: json['statusUpdatedAt'] != null
            ? DateTime.fromMillisecondsSinceEpoch(json['statusUpdatedAt'])
            : null,
        statusUpdatedBy: json['statusUpdatedBy'],
        userConfirmedOn: json['userConfirmedOn'] != null
            ? DateTime.fromMillisecondsSinceEpoch(json['userConfirmedOn'])
            : null,
        charges: (json['charges'] as Map<String, dynamic>?)
                ?.entries
                .map((e) =>
                    ChargeModel(chargeName: e.key, price: e.value.toString()))
                .toList() ??
            [],
        orderProductData: (json['orderProductData'] as List<dynamic>)
            .map((e) => OrderProductData.fromJson(e))
            .toList(),
        // updatedAt: json['updatedAt'] != null
        //     ? DateTime.fromMillisecondsSinceEpoch(json['updatedAt'])
        //     : null,
        // updatedBy: json['updatedBy'],
        // updatedByDocId: json['updatedByDocId'],
        paidAmount: json['paidAmount'] ?? 0,
        totalAmount: json['totalAmount'] ?? 0,
        address: json['address'],
        orderStatus: json['orderStatus'],
        rejectionReason: json['rejectionReason'],
        previousDocIds: List<String>.from(json['previousDocIds']),
      );
    } catch (e) {
      return null;
    }
  }

  // Convert OrderModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'orderId': orderId,
      'createdAt': Timestamp.fromDate(createdAt),
      'createdByDocId': createdByDocId,
      'uid': uid,
      'userConfirmed': userConfirmed,
      'isPaid': isPaid,
      'isDirectOrder': isDirectOrder,
      'paidOn': paidOn,
      'sessionId': sessionId,
      'charges': charges,
      'orderProductData': orderProductData.map((e) => e.toJson()).toList(),
      // 'updatedAt': updatedAt,
      // 'updatedBy': updatedBy,
      // 'updatedByDocId': updatedByDocId,
      'paidAmount': paidAmount,
      'totalAmount': totalAmount,
      'address': address,
      'orderStatus': orderStatus,
      'rejectionReason': rejectionReason,
    };
  }

  // Convert Firestore DocumentSnapshot to OrderModel
  factory OrderModel.fromDocSnap(DocumentSnapshot<Map<String, dynamic>> data) {
    try {
      return OrderModel(
        docId: data.id,
        orderId: data['orderId'],
        createdByDocId: data['createdByDocId'],
        uid: data['uid'],
        isPaid: data['isPaid'],
        isDirectOrder: data['isDirectOrder'],
        sessionId: data['sessionId'] ?? '',
        statusUpdatedAt: data['statusUpdatedAt'] != null
            ? DateTime.fromMillisecondsSinceEpoch(data['statusUpdatedAt'])
            : null,
        statusUpdatedBy: data['statusUpdatedBy'],
        charges: (data['charges'] as Map<String, dynamic>?)
                ?.entries
                .map((e) =>
                    ChargeModel(chargeName: e.key, price: e.value.toString()))
                .toList() ??
            [],
        orderProductData: (data['orderProductData'] as List<dynamic>)
            .map((e) => OrderProductData.fromJson(e))
            .toList(),
        // updatedBy: data['updatedBy'],
        // updatedByDocId: data['updatedByDocId'],
        // updatedAt: data['updatedAt'] != null
        //     ? DateTime.fromMillisecondsSinceEpoch(data['updatedAt'])
        //     : null,
        paidAmount: data['paidAmount'] ?? 0,
        totalAmount: data.data()?.containsKey('totalAmount') ?? false
            ? data['totalAmount'] ?? 0
            : 0,
        address: data['address'],
        orderStatus: data['orderStatus'],
        createdAt: DateTime.fromMillisecondsSinceEpoch(data['createdAt']),
        userConfirmed: data['userConfirmed'],
        rejectionReason: data.data()?.containsKey('rejectionReason') ?? false
            ? data['rejectionReason']
            : null,
        paidOn: data['paidOn'] != null
            ? DateTime.fromMillisecondsSinceEpoch(data['paidOn'])
            : null,
        userConfirmedOn: data['userConfirmedOn'] != null
            ? DateTime.fromMillisecondsSinceEpoch(data['userConfirmedOn'])
            : null,
        previousDocIds: List<String>.from(data['previousDocIds']),
      );
    } catch (e) {
      print(
          'Error parsing OrderModel.fromDocSnap for docId: ${data.id}, error: $e');
      rethrow; // Skip this entry by rethrowing the error
    }
  }
  factory OrderModel.fromSnap(
      QueryDocumentSnapshot<Map<String, dynamic>> data) {
    try {
      return OrderModel(
        docId: data.id,
        orderId: data['orderId'],
        createdByDocId: data['createdByDocId'],
        uid: data['uid'],
        sessionId: data['sessionId'] ?? '',
        isPaid: data['isPaid'],
        isDirectOrder: data['isDirectOrder'],
        rejectionReason: data.data().containsKey('rejectionReason')
            ? data['rejectionReason']
            : null,

        charges: (data['charges'] as Map<String, dynamic>?)
                ?.entries
                .map((e) =>
                    ChargeModel(chargeName: e.key, price: e.value.toString()))
                .toList() ??
            [],
        statusUpdatedAt: data['statusUpdatedAt'] != null
            ? DateTime.fromMillisecondsSinceEpoch(data['statusUpdatedAt'])
            : null,
        statusUpdatedBy: data['statusUpdatedBy'],
        orderProductData: (data['orderProductData'] as List<dynamic>)
            .map((e) => OrderProductData.fromJson(e as Map<String, dynamic>))
            .toList(),
        // updatedBy: data['updatedBy'],
        // updatedByDocId: data['updatedByDocId'],
        // updatedAt: data['updatedAt'] != null
        //     ? DateTime.fromMillisecondsSinceEpoch(data['updatedAt'])
        //     : null,
        orderStatus: data['orderStatus'],
        paidAmount: data['paidAmount'] ?? 0,
        totalAmount: data.data().containsKey('totalAmount')
            ? data['totalAmount'] ?? 0
            : 0,
        address: data['address'],
        createdAt: DateTime.fromMillisecondsSinceEpoch(data['createdAt']),
        userConfirmed: data['userConfirmed'],
        paidOn: data['paidOn'] != null
            ? DateTime.fromMillisecondsSinceEpoch(data['paidOn'])
            : null,
        userConfirmedOn: data['userConfirmedOn'] != null
            ? DateTime.fromMillisecondsSinceEpoch(data['userConfirmedOn'])
            : null,
        previousDocIds: List<String>.from(data['previousDocIds']),
      );
    } catch (e) {
      print(
          'Error parsing OrderModel.fromSnap for docId: ${data.id}, error: $e');
      rethrow; // Skip this entry by rethrowing the error
    }
  }
}

class OrderProductData {
  // final String vendorId;
  final String id;
  final String productId;
  final String variantId;
  final String vendorId;
  int qty;
  final String? currentStatus;
  final num purchasePrice;
  num sellingPrice;
  String userNote;
  String adminNote;
  final String productSKu;
  DateTime? deliveryDate;
  final OrderProductExtraData? orderProductExtraData;

  OrderProductData({
    required this.id,

    // required this.vendorId,
    required this.productId,
    required this.variantId,
    required this.vendorId,
    required this.qty,
    required this.currentStatus,
    required this.purchasePrice,
    required this.sellingPrice,
    required this.userNote,
    required this.adminNote,
    required this.productSKu,
    required this.deliveryDate,
    required this.orderProductExtraData,
  });

  // Convert JSON to OrderProductData
  factory OrderProductData.fromJson(Map<String, dynamic> json) {
    return OrderProductData(
      // vendorId: json['vendorId'] ?? '',
      id: json['id'] ?? '',
      productId: json['productId'] ?? '',
      variantId: json['variantId'] ?? '',
      vendorId: json['vendorId'] ?? '',
      qty: json['qty'] ?? 0,
      currentStatus: json['currentStatus'],
      purchasePrice: json['purchasePrice'],
      sellingPrice: json['sellingPrice'],
      userNote: json['userNote'],
      adminNote: json['adminNote'],
      productSKu: json['productSKu'],
      deliveryDate: json['deliveryDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['deliveryDate'])
          : null,
      orderProductExtraData: (json.containsKey('orderProductExtraData') &&
              json['orderProductExtraData'] != null)
          ? OrderProductExtraData.fromJson(json['orderProductExtraData'])
          : null,
    );
  }

  // Convert OrderProductData to JSON
  Map<String, dynamic> toJson() {
    return {
      // 'vendorId': vendorId,
      'id': id,
      'productId': productId,
      'variantId': variantId,
      'vendorId': vendorId,
      'qty': qty,
      'currentStatus': currentStatus,
      'purchasePrice': purchasePrice,
      'sellingPrice': sellingPrice,
      'userNote': userNote,
      'adminNote': adminNote,
      'productSKu': productSKu,
      'deliveryDate': deliveryDate?.millisecondsSinceEpoch,
      'orderProductExtraData': orderProductExtraData?.toJson(),
    };
  }

  // Map<String, dynamic> toOutForDeliveryJson() {
  //   return {
  //     // 'vendorId': vendorId,
  //     'id': id,
  //     'productId': productId,
  //     'variantId': variantId,
  //     'qty': qty,
  //     'currentStatus': OrderProductStatus.outfordeliver,
  //     'purchasePrice': purchasePrice,
  //     'sellingPrice': sellingPrice,
  //     'userNote': userNote,
  //     'adminNote': adminNote,
  //     'productSKu': productSKu,
  //     'deliveryDate': deliveryDate?.millisecondsSinceEpoch,
  //   };
  // }

  // Map<String, dynamic> toDeliveredJson() {
  //   return {
  //     // 'vendorId': vendorId,
  //     'id': id,
  //     'productId': productId,
  //     'variantId': variantId,
  //     'qty': qty,
  //     'currentStatus': OrderProductStatus.delivered,
  //     'purchasePrice': purchasePrice,
  //     'sellingPrice': sellingPrice,
  //     'userNote': userNote,
  //     'adminNote': adminNote,
  //     'productSKu': productSKu,
  //     'deliveryDate': deliveryDate?.millisecondsSinceEpoch,
  //   };
  // }

  Map<String, dynamic> toPartialDeliveredJson(
      String currentStatus, int deliveredQty) {
    return {
      // 'vendorId': vendorId,
      'id': id,
      'productId': productId,
      'variantId': variantId,
      'qty': deliveredQty,
      'currentStatus': currentStatus,
      'purchasePrice': purchasePrice,
      'sellingPrice': sellingPrice,
      'userNote': userNote,
      'adminNote': adminNote,
      'productSKu': productSKu,
      'deliveryDate': deliveryDate?.millisecondsSinceEpoch,
      'orderProductExtraData': orderProductExtraData?.toJson(),
    };
  }
}

class OrderProductExtraData {
  final String vendorDocId;
  final String vendorName;
  final String vendorEmail;
  final String vendorPhone;
  final String productDocId;
  final String productSku;
  final String productName;
  final String productDesc;
  final String variantDocId;
  final String variantImage;
  final num? variantPurchasePrice;
  final String variantDescription;
  final Map<String, dynamic> variantDetailTypes;

  OrderProductExtraData({
    required this.vendorDocId,
    required this.vendorName,
    required this.vendorEmail,
    required this.vendorPhone,
    required this.productDocId,
    required this.productSku,
    required this.productName,
    required this.productDesc,
    required this.variantDocId,
    required this.variantImage,
    required this.variantPurchasePrice,
    required this.variantDescription,
    required this.variantDetailTypes,
  });

  factory OrderProductExtraData.fromJson(Map<String, dynamic> json) {
    return OrderProductExtraData(
      vendorDocId: json['vendorDocId'] ?? '',
      vendorName: json['vendorName'] ?? '',
      vendorEmail: json['vendorEmail'] ?? '',
      vendorPhone: json['vendorPhone'] ?? '',
      productDocId: json['productDocId'] ?? '',
      productSku: json['productSku'] ?? '',
      productName: json['productName'] ?? '',
      productDesc: json['productDesc'] ?? '',
      variantDocId: json['variantDocId'] ?? '',
      variantImage: json['variantImage'] ?? '',
      variantPurchasePrice: json['variantPurchasePrice'],
      variantDescription: json['variantDescription'] ?? '',
      variantDetailTypes:
          Map<String, dynamic>.from(json['variantDetailTypes'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'vendorDocId': vendorDocId,
      'vendorName': vendorName,
      'vendorEmail': vendorEmail,
      'vendorPhone': vendorPhone,
      'productDocId': productDocId,
      'productSku': productSku,
      'productName': productName,
      'productDesc': productDesc,
      'variantDocId': variantDocId,
      'variantImage': variantImage,
      'variantPurchasePrice': variantPurchasePrice,
      'variantDescription': variantDescription,
      'variantDetailTypes': variantDetailTypes,
    };
  }
}

class TransactionModel {
  final String docId;
  final num amount;
  final String orderId;
  final bool isDebit;
  final DateTime? paymentTime;
  final String note;
  final String uId;
  final bool isPaid;
  final String paymentLink;
  final String? transactionId;
  final String? method;
  final DateTime createdAt;
  final String inquiryId;
  final String messageId;

  TransactionModel({
    required this.docId,
    required this.amount,
    required this.orderId,
    required this.isDebit,
    required this.paymentTime,
    required this.note,
    required this.uId,
    required this.isPaid,
    required this.paymentLink,
    required this.transactionId,
    required this.method,
    required this.createdAt,
    required this.inquiryId,
    required this.messageId,
  });

  /// From JSON
  factory TransactionModel.fromJson(Map<String, dynamic> json) {
    return TransactionModel(
      docId: json['docId'],
      amount: json['amount'],
      orderId: json['orderId'],
      isDebit: json['isDebit'],
      paymentTime: json['paymentTime'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['paymentTime'])
          : null,
      note: json['note'],
      uId: json['uId'],
      isPaid: json['isPaid'],
      paymentLink: json['paymentLink'],
      transactionId: json['transactionId'],
      method: json['method'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt']),
      inquiryId: json['inquiryId'],
      messageId: json['messageId'],
    );
  }

  /// To JSON
  Map<String, dynamic> toJson() {
    return {
      'amount': amount,
      'orderId': orderId,
      'isDebit': isDebit,
      'paymentTime': paymentTime,
      'note': note,
      'uId': uId,
      'isPaid': isPaid,
      'paymentLink': paymentLink,
      'transactionId': transactionId,
      'method': method,
      'createdAt': createdAt,
      'inquiryId': inquiryId,
      'messageId': messageId,
    };
  }

  /// From Firestore DocumentSnapshot
  factory TransactionModel.fromDocSnap(
      DocumentSnapshot<Map<String, dynamic>> json) {
    return TransactionModel(
      docId: json.id,
      amount: json['amount'],
      orderId: json['orderId'],
      isDebit: json['isDebit'],
      paymentTime: json['paymentTime'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['paymentTime'])
          : null,
      note: json['note'],
      uId: json['uId'],
      isPaid: json['isPaid'],
      paymentLink: json['paymentLink'],
      transactionId: json['transactionId'],
      method: json['method'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt']),
      inquiryId: json['inquiryId'],
      messageId: json['messageId'],
    );
  }
  factory TransactionModel.fromSnap(
      QueryDocumentSnapshot<Map<String, dynamic>> json) {
    return TransactionModel(
      docId: json.id,
      amount: json['amount'],
      orderId: json['orderId'],
      isDebit: json['isDebit'],
      paymentTime: json['paymentTime'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['paymentTime'])
          : null,
      note: json['note'],
      uId: json['uId'],
      isPaid: json['isPaid'],
      paymentLink: json['paymentLink'],
      transactionId: json['transactionId'],
      method: json['method'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt']),
      inquiryId: json['inquiryId'],
      messageId: json['messageId'],
    );
  }
}

class DeliveryDetailsModel {
  final String docId;
  final String deliveryPartner;
  final String trackingId;
  final String trackingLink;
  final DateTime? dispatchedOn;
  final List<DeliveryProductData> dispatchedProduct;
  final String teamMemberDocId;
  final String location;
  final AddressModel? userAddress;
  final String orderId;
  final String inquiryId;
  final String messageId;
  final String uId;
  final num charges;
  final DateTime createdAt;
  final DateTime? deliveredOn;
  final bool isDelivered;

  DeliveryDetailsModel({
    required this.docId,
    required this.deliveryPartner,
    required this.trackingId,
    required this.trackingLink,
    required this.dispatchedOn,
    required this.dispatchedProduct,
    required this.teamMemberDocId,
    required this.location,
    required this.orderId,
    required this.userAddress,
    required this.inquiryId,
    required this.messageId,
    required this.uId,
    required this.charges,
    required this.createdAt,
    required this.isDelivered,
    required this.deliveredOn,
  });

  factory DeliveryDetailsModel.fromJson(Map<String, dynamic> json) {
    return DeliveryDetailsModel(
      docId: json['docId'] ?? '',
      deliveryPartner: json['deliveryPartner'] ?? '',
      trackingId: json['trackingId'] ?? '',
      trackingLink: json['trackingLink'] ?? '',
      dispatchedOn: DateTime.fromMillisecondsSinceEpoch(json['dispatchedOn']),
      dispatchedProduct: (json['dispatchedProduct'] as List<dynamic>)
          .map((e) => DeliveryProductData.fromJson(e))
          .toList(),
      teamMemberDocId: json['teamMemberDocId'] ?? '',
      location: json['location'] ?? '',
      userAddress:
          json.containsKey('userAddress') && json['userAddress'] != null
              ? AddressModel.fromJson(
                  Map.castFrom(json['userAddress']).entries.first.key,
                  Map.castFrom(json['userAddress']).entries.first.value,
                )
              : null,
      orderId: json['orderId'] ?? '',
      inquiryId: json['inquiryId'] ?? '',
      messageId: json['messageId'] ?? '',
      uId: json['uId'] ?? '',
      charges: (json['charges']),
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt']),
      deliveredOn: json['deliveredOn'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['deliveredOn'])
          : null,
      isDelivered: json['isDelivered'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'deliveryPartner': deliveryPartner,
      'trackingId': trackingId,
      'trackingLink': trackingLink,
      'dispatchedOn': dispatchedOn?.millisecondsSinceEpoch,
      'dispatchedProduct': dispatchedProduct.map((e) => e.toJson()).toList(),
      'teamMemberDocId': teamMemberDocId,
      'location': location,
      'orderId': orderId,
      'inquiryId': inquiryId,
      'userAddress': userAddress?.toJson2(),
      'messageId': messageId,
      'uId': uId,
      'charges': charges,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'deliveredOn': deliveredOn,
      'isDelivered': isDelivered,
    };
  }

  factory DeliveryDetailsModel.fromDocSnap(
      DocumentSnapshot<Map<String, dynamic>> doc) {
    final data = doc.data()!;
    return DeliveryDetailsModel(
      docId: doc.id,
      deliveryPartner: data['deliveryPartner'] ?? '',
      trackingId: data['trackingId'] ?? '',
      trackingLink: data['trackingLink'] ?? '',
      dispatchedOn: data['dispatchedOn'] != null
          ? DateTime.fromMillisecondsSinceEpoch(data['dispatchedOn'])
          : null,
      dispatchedProduct: (data['dispatchedProduct'] as List<dynamic>)
          .map((e) => DeliveryProductData.fromJson(e))
          .toList(),
      teamMemberDocId: data['teamMemberDocId'] ?? '',
      location: data['location'] ?? '',
      userAddress:
          data.containsKey('userAddress') && data['userAddress'] != null
              ? AddressModel.fromJson(
                  Map.castFrom(data['userAddress']).entries.first.key,
                  Map.castFrom(data['userAddress']).entries.first.value,
                )
              : null,
      orderId: data['orderId'] ?? '',
      inquiryId: data['inquiryId'] ?? '',
      messageId: data['messageId'] ?? '',
      uId: data['uId'] ?? '',
      charges: data['charges'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(data['createdAt']),
      isDelivered: data['isDelivered'],
      deliveredOn: data['deliveredOn'] != null
          ? DateTime.fromMillisecondsSinceEpoch(data['deliveredOn'])
          : null,
    );
  }

  factory DeliveryDetailsModel.fromSnap(
      QueryDocumentSnapshot<Map<String, dynamic>> doc) {
    final data = doc.data();
    return DeliveryDetailsModel(
      docId: doc.id,
      deliveryPartner: data['deliveryPartner'] ?? '',
      trackingId: data['trackingId'] ?? '',
      trackingLink: data['trackingLink'] ?? '',
      dispatchedOn: data['dispatchedOn'] != null
          ? DateTime.fromMillisecondsSinceEpoch(data['dispatchedOn'])
          : null,
      dispatchedProduct: (data['dispatchedProduct'] as List<dynamic>)
          .map((e) => DeliveryProductData.fromJson(e))
          .toList(),
      teamMemberDocId: data['teamMemberDocId'] ?? '',
      location: data['location'] ?? '',
      userAddress:
          data.containsKey('userAddress') && data['userAddress'] != null
              ? AddressModel.fromJson(
                  Map.castFrom(data['userAddress']).entries.first.key,
                  Map.castFrom(data['userAddress']).entries.first.value,
                )
              : null,
      orderId: data['orderId'] ?? '',
      inquiryId: data['inquiryId'] ?? '',
      messageId: data['messageId'] ?? '',
      uId: data['uId'] ?? '',
      charges: data['charges'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(data['createdAt']),
      isDelivered: data['isDelivered'],
      deliveredOn: data['deliveredOn'] != null
          ? DateTime.fromMillisecondsSinceEpoch(data['deliveredOn'])
          : null,
    );
  }
}

class DeliveryProductData {
  // final String vendorId;
  final String id;
  final String productId;
  final String variantId;
  int qty;
  final String? currentStatus;
  final num purchasePrice;
  num sellingPrice;
  final String userNote;
  String adminNote;
  final String productSKu;
  DateTime? deliveryDate;

  DeliveryProductData({
    required this.id,

    // required this.vendorId,
    required this.productId,
    required this.variantId,
    required this.qty,
    required this.currentStatus,
    required this.purchasePrice,
    required this.sellingPrice,
    required this.userNote,
    required this.adminNote,
    required this.productSKu,
    required this.deliveryDate,
  });

  // Convert JSON to OrderProductData
  factory DeliveryProductData.fromJson(Map<String, dynamic> json) {
    return DeliveryProductData(
      // vendorId: json['vendorId'] ?? '',
      id: json['id'] ?? '',
      productId: json['productId'] ?? '',
      variantId: json['variantId'] ?? '',
      qty: json['qty'] ?? 0,
      currentStatus: json['currentStatus'],
      purchasePrice: json['purchasePrice'],
      sellingPrice: json['sellingPrice'],
      userNote: json['userNote'],
      adminNote: json['adminNote'],
      productSKu: json['productSKu'],
      deliveryDate: json['deliveryDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['deliveryDate'])
          : null,
    );
  }

  // Convert OrderProductData to JSON
  Map<String, dynamic> toJson() {
    return {
      // 'vendorId': vendorId,
      'id': id,
      'productId': productId,
      'variantId': variantId,
      'qty': qty,
      'currentStatus': currentStatus,
      'purchasePrice': purchasePrice,
      'sellingPrice': sellingPrice,
      'userNote': userNote,
      'adminNote': adminNote,
      'productSKu': productSKu,
      'deliveryDate': deliveryDate?.millisecondsSinceEpoch,
    };
  }

  // Map<String, dynamic> toOutForDeliveryJson() {
  //   return {
  //     // 'vendorId': vendorId,
  //     'id': id,
  //     'productId': productId,
  //     'variantId': variantId,
  //     'qty': qty,
  //     'currentStatus': OrderProductStatus.outfordeliver,
  //     'purchasePrice': purchasePrice,
  //     'sellingPrice': sellingPrice,
  //     'userNote': userNote,
  //     'adminNote': adminNote,
  //     'productSKu': productSKu,
  //     'deliveryDate': deliveryDate?.millisecondsSinceEpoch,
  //   };
  // }

  // Map<String, dynamic> toDeliveredJson() {
  //   return {
  //     // 'vendorId': vendorId,
  //     'id': id,
  //     'productId': productId,
  //     'variantId': variantId,
  //     'qty': qty,
  //     'currentStatus': OrderProductStatus.delivered,
  //     'purchasePrice': purchasePrice,
  //     'sellingPrice': sellingPrice,
  //     'userNote': userNote,
  //     'adminNote': adminNote,
  //     'productSKu': productSKu,
  //     'deliveryDate': deliveryDate?.millisecondsSinceEpoch,
  //   };
  // }

  Map<String, dynamic> toPartialDeliveredJson(
      String currentStatus, int deliveredQty) {
    return {
      // 'vendorId': vendorId,
      'id': id,
      'productId': productId,
      'variantId': variantId,
      'qty': deliveredQty,
      'currentStatus': currentStatus,
      'purchasePrice': purchasePrice,
      'sellingPrice': sellingPrice,
      'userNote': userNote,
      'adminNote': adminNote,
      'productSKu': productSKu,
      'deliveryDate': deliveryDate?.millisecondsSinceEpoch,
    };
  }
}

class ChargeModel {
  String chargeName;
  String price;

  ChargeModel({required this.chargeName, required this.price});

  // Convert JSON to ChargeModel
  factory ChargeModel.fromJson(String key, String value) {
    return ChargeModel(
      chargeName: key,
      price: value,
    );
  }

  // Convert ChargeModel to JSON
  Map<String, dynamic> toJson() {
    return {
      chargeName: price,
    };
  }
}
