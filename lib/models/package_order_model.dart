import 'package:cloud_firestore/cloud_firestore.dart';

import 'package_model.dart';

class AddnData {
  String serviceId;
  String serviceName;
  num qty;
  num additionalPrice;

  AddnData(
      {required this.serviceId,
      required this.serviceName,
      required this.qty,
      required this.additionalPrice});
  factory AddnData.fromJson(Map<String, dynamic> json) {
    return AddnData(
      serviceId: json['serviceId'] ?? '',
      serviceName: json['serviceName'] ?? '',
      qty: json['qty'] ?? 0,
      additionalPrice: json['additionalPrice'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'serviceId': serviceId,
      'serviceName': serviceName,
      'qty': qty,
      'additionalPrice': additionalPrice,
    };
  }
}

/* class PackageOrderModel {
  final String docId;
  final PackageModel packageModel;
  final num packageAmount;
  final List<AddnData> extraServices;
  final num extraServicesAmount;
  final String uid;
  final String selectedAddressId;
  final String username;
  final DateTime createdAt;
  final bool isPaid;
  final DateTime? paidon;
  final num totalAmount;
  final num paidAmount;

  PackageOrderModel({
    required this.docId,
    required this.packageModel,
    required this.extraServices,
    required this.packageAmount,
    required this.extraServicesAmount,
    required this.uid,
    required this.selectedAddressId,
    required this.username,
    required this.createdAt,
    required this.isPaid,
    required this.paidon,
    required this.totalAmount,
    required this.paidAmount,
  });
  factory PackageOrderModel.fromJson(Map<String, dynamic> json) {
    return PackageOrderModel(
      docId: json['docId'] ?? '',
      packageModel:
          PackageModel.fromJson(json['packageModel'] ?? <String, dynamic>{}),
      extraServices: (json['extraServices'] as List<dynamic>? ?? [])
          .map((e) => AddnData.fromJson(e as Map<String, dynamic>))
          .toList(),
      packageAmount: json['packageAmount'] ?? 0,
      extraServicesAmount: json['extraServicesAmount'] ?? 0,
      uid: json['uid'] ?? '',
      selectedAddressId: json['selectedAddressId'] ?? '',
      username: json['username'] ?? '',
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt'] ?? 0),
      isPaid: json['isPaid'] ?? false,
      paidon: DateTime.fromMillisecondsSinceEpoch(json['paidon'] ?? 0),
      totalAmount: json['totalAmount'] ?? 0,
      paidAmount: json['paidAmount'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'packageModel': packageModel.toJson(),
      'extraServices': extraServices.map((e) => e.toJson()).toList(),
      'packageAmount': packageAmount,
      'extraServicesAmount': extraServicesAmount,
      'uid': uid,
      'selectedAddressId': selectedAddressId,
      'username': username,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'isPaid': isPaid,
      'paidon': paidon?.millisecondsSinceEpoch,
      'paidAmount': paidAmount,
      'totalAmount': totalAmount,
    };
  }

  factory PackageOrderModel.fromSnap(
      DocumentSnapshot<Map<String, dynamic>> snap) {
    final data = snap.data()!;

    return PackageOrderModel(
      docId: snap.id,
      packageModel:
          PackageModel.fromJson(data['packageModel'] ?? <String, dynamic>{}),
      extraServices: (data['extraServices'] as List<dynamic>? ?? [])
          .map((e) => AddnData.fromJson(e as Map<String, dynamic>))
          .toList(),
      packageAmount: data['packageAmount'] ?? 0,
      extraServicesAmount: data['extraServicesAmount'] ?? 0,
      uid: data['uid'] ?? '',
      selectedAddressId: data['selectedAddressId'] ?? '',
      username: data['username'] ?? '',
      createdAt: DateTime.fromMillisecondsSinceEpoch(data['createdAt'] ?? 0),
      isPaid: data['isPaid'] ?? false,
      paidon: DateTime.fromMillisecondsSinceEpoch(data['paidon'] ?? 0),
      paidAmount: data['paidAmount'] ?? 0,
      totalAmount: data['totalAmount'] ?? 0,
    );
  }

  factory PackageOrderModel.fromDocSnap(
      DocumentSnapshot<Map<String, dynamic>> docSnap) {
    final data = docSnap.data()!;
    return PackageOrderModel(
      docId: docSnap.id,
      packageModel:
          PackageModel.fromJson(data['packageModel'] ?? <String, dynamic>{}),
      extraServices: (data['extraServices'] as List<dynamic>? ?? [])
          .map((e) => AddnData.fromJson(e as Map<String, dynamic>))
          .toList(),
      packageAmount: data['packageAmount'] ?? 0,
      extraServicesAmount: data['extraServicesAmount'] ?? 0,
      uid: data['uid'] ?? '',
      selectedAddressId: data['selectedAddressId'] ?? '',
      username: data['username'] ?? '',
      createdAt: DateTime.fromMillisecondsSinceEpoch(data['createdAt'] ?? 0),
      isPaid: data['isPaid'] ?? false,
      paidon: DateTime.fromMillisecondsSinceEpoch(data['paidon'] ?? 0),
      totalAmount: data['totalAmount'] ?? 0,
      paidAmount: data['paidAmount'] ?? 0,
    );
  }
  factory PackageOrderModel.forNextSnap(DocumentSnapshot docSnap) {
    final data = docSnap.data()! as Map<String, dynamic>;
    return PackageOrderModel(
      docId: docSnap.id,
      packageModel:
          PackageModel.fromJson(data['packageModel'] ?? <String, dynamic>{}),
      extraServices: (data['extraServices'] as List<dynamic>? ?? [])
          .map((e) => AddnData.fromJson(e as Map<String, dynamic>))
          .toList(),
      packageAmount: data['packageAmount'] ?? 0,
      extraServicesAmount: data['extraServicesAmount'] ?? 0,
      uid: data['uid'] ?? '',
      selectedAddressId: data['selectedAddressId'] ?? '',
      username: data['username'] ?? '',
      createdAt: DateTime.fromMillisecondsSinceEpoch(data['createdAt'] ?? 0),
      isPaid: data['isPaid'] ?? false,
      paidon: DateTime.fromMillisecondsSinceEpoch(data['paidon'] ?? 0),
      totalAmount: data['totalAmount'] ?? 0,
      paidAmount: data['paidAmount'] ?? 0,
    );
  }
}
 */

class PackagePurchaseModel {
  final String docId;
  final String packageName;
  final num packagePrice;
  final String description;
  final int validity;
  final String packageColor;
  final List<PointsModel> points;
  final List<AddnData> extraServices;
  final num extraServicesAmount;
  final num discountedPrice;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime purchaseAt;
  final String uid;
  final String userName;
  final String shareCode;
  final String userEmail;
  final String userPhone;
  final List<String> sharedBy;
  final bool isPaid;
  final num totalAmount;
  final num paidAmount;

  // NEED TO DO SOMETHING OF USED POINTS
  final List<PointsModel> remainingPoints;

  PackagePurchaseModel({
    required this.docId,
    required this.packageName,
    required this.packagePrice,
    required this.description,
    required this.validity,
    required this.packageColor,
    required this.discountedPrice,
    required this.points,
    required this.isActive,
    required this.createdAt,
    required this.purchaseAt,
    required this.shareCode,
    required this.uid,
    required this.userName,
    required this.updatedAt,
    required this.userEmail,
    required this.userPhone,
    required this.remainingPoints,
    required this.extraServices,
    required this.extraServicesAmount,
    required this.sharedBy,
    required this.isPaid,
    required this.totalAmount,
    required this.paidAmount,
  });

  factory PackagePurchaseModel.fromJson(Map<String, dynamic> json) {
    return PackagePurchaseModel(
      docId: json['docId'],
      packageName: json['packageName'],
      packagePrice: json['packagePrice'],
      description: json['description'],
      discountedPrice: json['discountedPrice'],
      validity: json['validity'],
      updatedAt: DateTime.fromMillisecondsSinceEpoch(json['updatedAt']),
      packageColor: json['packageColor'],
      points: (json['points'] as List<dynamic>)
          .map((e) => PointsModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      shareCode: json.containsKey('shareCode') ? json['shareCode'] : '123456',
      isActive: json['isActive'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt']),
      purchaseAt: DateTime.fromMillisecondsSinceEpoch(json['purchaseAt']),
      uid: json['uid'],
      userName: json['userName'],
      userEmail: json['userEmail'],
      userPhone: json['userPhone'],
      remainingPoints: (json['remainingPoints'] as List<dynamic>? ?? [])
          .map((e) => PointsModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      extraServices: (json['extraServices'] as List<dynamic>? ?? [])
          .map((e) => AddnData.fromJson(e as Map<String, dynamic>))
          .toList(),
      extraServicesAmount: json['extraServicesAmount'] ?? 0,
      isPaid: json['isPaid'] ?? false,
      totalAmount: json['totalAmount'] ?? 0,
      paidAmount: json['paidAmount'] ?? 0,
      sharedBy: List<String>.from(json['sharedBy'] ?? <String>[]),
    );
  }
  factory PackagePurchaseModel.fromSnap(
      QueryDocumentSnapshot<Map<String, dynamic>> json) {
    return PackagePurchaseModel(
        docId: json.id,
        packageName: json['packageName'],
        packagePrice: json['packagePrice'],
        discountedPrice: json['discountedPrice'],
        description: json['description'],
        updatedAt: DateTime.fromMillisecondsSinceEpoch(json['updatedAt']),
        validity: json['validity'],
        packageColor: json['packageColor'],
        points: (json['points'] as List<dynamic>)
            .map((e) => PointsModel.fromJson(e as Map<String, dynamic>))
            .toList(),
        isActive: json['isActive'],
        createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt']),
        purchaseAt: DateTime.fromMillisecondsSinceEpoch(json['purchaseAt']),
        uid: json['uid'],
        userName: json['userName'],
        userEmail: json['userEmail'],
        userPhone: json['userPhone'],
        shareCode:
            json.data().containsKey('shareCode') ? json['shareCode'] : '123456',
        remainingPoints: (json['remainingPoints'] as List<dynamic>? ?? [])
            .map((e) => PointsModel.fromJson(e as Map<String, dynamic>))
            .toList(),
        extraServices: (json['extraServices'] as List<dynamic>? ?? [])
            .map((e) => AddnData.fromJson(e as Map<String, dynamic>))
            .toList(),
        extraServicesAmount: json['extraServicesAmount'] ?? 0,
        isPaid: json['isPaid'] ?? false,
        totalAmount: json['totalAmount'] ?? 0,
        paidAmount: json['paidAmount'] ?? 0,
        sharedBy: List<String>.from(json['sharedBy'] ?? <String>[]));
  }
  factory PackagePurchaseModel.fromDocSnap(
      DocumentSnapshot<Map<String, dynamic>> json) {
    return PackagePurchaseModel(
      docId: json.id,
      packageName: json['packageName'],
      packagePrice: json['packagePrice'],
      discountedPrice: json['discountedPrice'],
      updatedAt: DateTime.fromMillisecondsSinceEpoch(json['updatedAt']),
      description: json['description'],
      validity: json['validity'],
      packageColor: json['packageColor'],
      points: (json['points'] as List<dynamic>)
          .map((e) => PointsModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      isActive: json['isActive'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt']),
      purchaseAt: DateTime.fromMillisecondsSinceEpoch(json['purchaseAt']),
      uid: json['uid'],
      userName: json['userName'],
      userEmail: json['userEmail'],
      userPhone: json['userPhone'],
      remainingPoints: (json['remainingPoints'] as List<dynamic>? ?? [])
          .map((e) => PointsModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      shareCode:
          json.data()!.containsKey('shareCode') ? json['shareCode'] : '123456',
      extraServices: (json['extraServices'] as List<dynamic>? ?? [])
          .map((e) => AddnData.fromJson(e as Map<String, dynamic>))
          .toList(),
      extraServicesAmount: json['extraServicesAmount'] ?? 0,
      isPaid: json['isPaid'] ?? false,
      totalAmount: json['totalAmount'] ?? 0,
      paidAmount: json['paidAmount'] ?? 0,
      sharedBy: List<String>.from(json['sharedBy'] ?? <String>[]),
    );
  }
  Map<String, dynamic> toJson() => {
        'packageName': packageName,
        'packagePrice': packagePrice,
        'description': description,
        'discountedPrice': discountedPrice,
        'updatedAt': updatedAt.millisecondsSinceEpoch,
        'validity': validity,
        'packageColor': packageColor,
        'points': points.map((e) => e.toJson()).toList(),
        'isActive': isActive,
        'createdAt': createdAt.millisecondsSinceEpoch,
        'purchaseAt': purchaseAt.millisecondsSinceEpoch,
        'uid': uid,
        'userName': userName,
        'userEmail': userEmail,
        'userPhone': userPhone,
        'remainingPoints': remainingPoints.map((e) => e.toJson()).toList(),
        'extraServices': extraServices.map((e) => e.toJson()).toList(),
        'extraServicesAmount': extraServicesAmount,
        'isPaid': isPaid,
        'totalAmount': totalAmount,
        'paidAmount': paidAmount,
        'sharedBy': sharedBy,
        'shareCode': shareCode,
      };
  factory PackagePurchaseModel.forNextSnap(DocumentSnapshot docSnap) {
    final data = docSnap.data()! as Map<String, dynamic>;
    return PackagePurchaseModel(
      docId: docSnap.id,
      packageName: data['packageName'],
      packagePrice: data['packagePrice'],
      discountedPrice: data['discountedPrice'],
      description: data['description'],
      shareCode: data.containsKey('shareCode') ? data['shareCode'] : '123456',
      updatedAt: DateTime.fromMillisecondsSinceEpoch(data['updatedAt']),
      validity: data['validity'],
      packageColor: data['packageColor'],
      points: (data['points'] as List<dynamic>)
          .map((e) => PointsModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      isActive: data['isActive'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(data['createdAt']),
      purchaseAt: DateTime.fromMillisecondsSinceEpoch(data['purchaseAt']),
      uid: data['uid'],
      userName: data['userName'],
      userEmail: data['userEmail'],
      userPhone: data['userPhone'],
      remainingPoints: (data['remainingPoints'] as List<dynamic>? ?? [])
          .map((e) => PointsModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      extraServices: (data['extraServices'] as List<dynamic>? ?? [])
          .map((e) => AddnData.fromJson(e as Map<String, dynamic>))
          .toList(),
      extraServicesAmount: data['extraServicesAmount'] ?? 0,
      isPaid: data['isPaid'] ?? false,
      totalAmount: data['totalAmount'] ?? 0,
      paidAmount: data['paidAmount'] ?? 0,
      sharedBy: List<String>.from(data['sharedBy'] ?? <String>[]),
    );
  }
}
