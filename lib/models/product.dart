import 'package:cloud_firestore/cloud_firestore.dart';

class ProductModel {
  final String? docId;
  final String name;
  final String lowerName;
  final List<String> combinationNames;
  final String mainCatDocId;
  final String defaultSubCatDocId;
  final List<String> subcatIds;
  final String vendorDocId;
  final String defaultImage;
  bool show;
  final String description;
  final String sku;
  final String userType;
  final Timestamp createdAt;
  bool topSelling;
//   final List<VariantModel> variants;
  final num minPrice;
  final num maxPrice;
  final int quantitySold;
  final num totalSalesAmount;
  final List<String> tags;
  // final List<String> combinedDetailsList;
  final List<String> detailsList;
  final int priorityNo;
  final num rating;

  ProductModel({
    required this.priorityNo,
    required this.rating,
    required this.docId,
    required this.name,
    required this.lowerName,
    required this.combinationNames,
    required this.mainCatDocId,
    required this.defaultSubCatDocId,
    required this.subcatIds,
    required this.vendorDocId,
    required this.show,
    required this.sku,
    required this.description,
    required this.userType,
    required this.defaultImage,
    required this.createdAt,
    required this.topSelling,
    // required this.variants,
    required this.minPrice,
    required this.maxPrice,
    required this.quantitySold,
    required this.totalSalesAmount,
    required this.tags,
    // required this.combinedDetailsList,
    required this.detailsList,
  });

  /// Convert a ProductModel instance to JSON
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'lowerName': lowerName,
      'combinationNames': combinationNames,
      'mainCatDocId': mainCatDocId,
      'subCatDocId': defaultSubCatDocId,
      'subcatIds': subcatIds,
      'vendorDocId': vendorDocId,
      'show': show,
      'description': description,
      'userType': userType,
      'createdAt': createdAt,
      'sku': sku,
      'topSelling': topSelling,
      'defaultImage': defaultImage,
      //   'variants': variants.map((variant) => variant.toJson()).toList(),
      'minPrice': minPrice,
      'maxPrice': maxPrice,
      'quantitySold': quantitySold,
      'totalSalesAmount': totalSalesAmount,
      'tags': tags,
      // 'combinedDetailsList': combinedDetailsList,
      'detailsList': detailsList,
      'priorityNo': priorityNo,
      'rating': rating,
    };
  }

  /// Create a ProductModel instance from JSON
  factory ProductModel.fromJson(Map<String, dynamic> json) {
    return ProductModel(
      docId: json['docId'] ?? '',
      name: json['name'],
      lowerName: json['lowerName'],
      combinationNames: List<String>.from(json['combinationNames']),
      mainCatDocId: json['mainCatDocId'],
      sku: json['sku'],
      defaultImage: json['defaultImage'],
      defaultSubCatDocId: json['subCatDocId'],
      subcatIds: List<String>.from(json['subcatIds']),
      vendorDocId: json['vendorDocId'],
      show: json['show'],
      description: json['description'],
      userType: json['userType'],
      createdAt: json['createdAt'],
      topSelling: json['topSelling'],
      //   variants: (json['variants'] as List<dynamic>)
      //       .map((variant) =>
      //           VariantModel.fromJson('', variant as Map<String, dynamic>))
      //       .toList(),
      minPrice: json['minPrice'],
      maxPrice: json['maxPrice'],
      priorityNo: json['priorityNo'],
      rating: json['rating'],
      quantitySold: json['quantitySold'],
      totalSalesAmount: json['totalSalesAmount'],
      tags: List<String>.from(json['tags']),
      detailsList: List<String>.from(json['detailsList']),

      // combinedDetailsList: List<String>.from(json['combinedDetailsList']),
    );
  }

  /// Create a ProductModel instance from Firestore QueryDocumentSnapshot
  factory ProductModel.fromSnap(
      QueryDocumentSnapshot<Map<String, dynamic>> json) {
    return ProductModel(
      docId: json.id,
      name: json['name'],
      lowerName: json['lowerName'],
      combinationNames: List<String>.from(json['combinationNames']),
      mainCatDocId: json['mainCatDocId'],
      defaultImage: json['defaultImage'],
      defaultSubCatDocId: json['subCatDocId'],
      subcatIds: json.data().containsKey('subcatIds')
          ? List<String>.from(json['subcatIds'])
          : [],
      vendorDocId: json['vendorDocId'],
      sku: json.data().containsKey('sku') ? json['sku'] : '',
      priorityNo: json['priorityNo'],
      rating: json['rating'],
      show: json['show'],
      description: json['description'],
      userType: json['userType'],
      createdAt: json['createdAt'],
      topSelling: json['topSelling'],
      //   variants: Map.from(json['variants'])
      //       .entries
      //       .map((variantJson) =>
      //           VariantModel.fromJson(variantJson.key, variantJson.value))
      //       .toList(),
      minPrice: json.data().containsKey('minPrice') ? json['minPrice'] : 0,
      maxPrice: json.data().containsKey('maxPrice') ? json['maxPrice'] : 0,
      quantitySold:
          json.data().containsKey('quantitySold') ? json['quantitySold'] : 0,
      totalSalesAmount: json.data().containsKey('totalSalesAmount')
          ? json['totalSalesAmount']
          : 0,
      tags: json.data().containsKey('tags')
          ? List<String>.from(json['tags'])
          : [],
      // combinedDetailsList: json.data().containsKey('combinedDetailsList')
      // ? List<String>.from(json['combinedDetailsList'])
      // : [],
      detailsList: json.data().containsKey('detailsList')
          ? List<String>.from(json['detailsList'])
          : [],
    );
  }

  /// Create a ProductModel instance from Firestore DocumentSnapshot
  factory ProductModel.fromDocSnap(
      DocumentSnapshot<Map<String, dynamic>> json) {
    return ProductModel(
      docId: json.id,
      name: json['name'],
      lowerName: json['lowerName'],
      combinationNames: List<String>.from(json['combinationNames']),
      mainCatDocId: json['mainCatDocId'],
      defaultImage: json['defaultImage'],
      defaultSubCatDocId: json['subCatDocId'],
      sku: json.data()?.containsKey('sku') ?? false ? json['sku'] : '',
      subcatIds: json.data()?.containsKey('subcatIds') ?? false
          ? List<String>.from(json['subcatIds'])
          : [],
      vendorDocId: json['vendorDocId'],
      show: json['show'],
      priorityNo: json['priorityNo'],
      rating: json['rating'],
      description: json['description'],
      userType: json['userType'],
      createdAt: json['createdAt'],
      topSelling: json['topSelling'],
      //   variants: Map.from(json['variants'])
      //       .entries
      //       .map((variantJson) =>
      //           VariantModel.fromJson(variantJson.key, variantJson.value))
      //       .toList(),
      minPrice: (json.data()?.containsKey('minPrice') ?? false)
          ? json['minPrice']
          : 0,
      maxPrice:
          json.data()?.containsKey('maxPrice') ?? false ? json['maxPrice'] : 0,
      quantitySold: json.data()?.containsKey('quantitySold') ?? false
          ? json['quantitySold']
          : 0,
      totalSalesAmount: json.data()?.containsKey('totalSalesAmount') ?? false
          ? json['totalSalesAmount']
          : 0,
      tags: json.data()?.containsKey('tags') ?? false
          ? List<String>.from(json['tags'])
          : [],
      // combinedDetailsList:
      //     json.data()?.containsKey('combinedDetailsList') ?? false
      //         ? List<String>.from(json['combinedDetailsList'])
      //         : [],
      detailsList: json.data()?.containsKey('detailsList') ?? false
          ? List<String>.from(json['detailsList'])
          : [],
    );
  }
}

// import 'package:cloud_firestore/cloud_firestore.dart';

// import 'variants.dart';

// class ProductModel {
//   final String docId;
//   final String name;
//   final String lowerName;
//   final List<String> combinationNames;
//   // final List<String> colors;
//   // final List<String> materials;
//   // final List<String> sizes;
//   final String mainCatDocId;
//   final String defaultSubCatDocId;
//   final List<String> subcatIds;
//   final String vendorDocId;
//   bool show;
//   final String description;
//   final String userType;
//   final Timestamp createdAt;
//   bool topSelling;
//   final List<VariantModel> variants;
//   final num minPrice;
//   final num maxPrice;
//   final int quantitySold;
//   final num totalSalesAmount;
//   final List<String> tags;
//   final List<String> combinedDetailsList;

//   ProductModel({
//     required this.subcatIds,
//     required this.minPrice,
//     required this.maxPrice,
//     required this.quantitySold,
//     required this.totalSalesAmount,
//     required this.tags,
//     required this.combinedDetailsList,
//     required this.docId,
//     required this.name,
//     required this.lowerName,
//     // required this.colors,
//     // required this.materials,
//     // required this.sizes,
//     required this.combinationNames,
//     required this.mainCatDocId,
//     required this.defaultSubCatDocId,
//     required this.vendorDocId,
//     required this.show,
//     required this.createdAt,
//     required this.description,
//     required this.topSelling,
//     required this.userType,
//     required this.variants,
//   });

//   // Convert a ProductModel instance to JSON
//   Map<String, dynamic> toJson() {
//     return {
//       'docId': docId,
//       'name': name,
//       'lowerName': lowerName,
//       'combinationNames': combinationNames,
//       'mainCatDocId': mainCatDocId,
//       'subCatDocId': defaultSubCatDocId,
//       'vendorDocId': vendorDocId,
//       'show': show,
//       'userType': userType,
//       'crecreatedAt': createdAt,
//       'description': description,
//       'topSelling': topSelling,
//       'variants': variants.map((variant) => variant.toJson()).toList(),
//     };
//   }

//   // Create a ProductModel instance from JSON
//   factory ProductModel.fromJson(Map<String, dynamic> json) {
//     return ProductModel(
//       docId: json['docId'],
//       name: json['name'],
//       lowerName: json['lowerName'],
//       combinationNames: json['combinationNames'],
//       // colors: json['colors'],
//       // materials: json['materials'],
//       // sizes: json['sizes'],
//       mainCatDocId: json['mainCatDocId'],
//       defaultSubCatDocId: json['subCatDocId'],
//       vendorDocId: json['vendorDocId'],
//       show: json['show'],
//       userType: json['userType'],
//       description: json['description'],
//       createdAt: json['createdAt'],
//       topSelling: json['topSelling'],
//       variants: Map.from(json['variants'])
//           .entries
//           .map((variantJson) =>
//               VariantModel.fromJson(variantJson.key, variantJson.value))
//           .toList(),
//     );
//   }

//   factory ProductModel.fromSnap(
//       QueryDocumentSnapshot<Map<String, dynamic>> json) {
//     return ProductModel(
//       docId: json.id,
//       name: json['name'],
//       lowerName: json['lowerName'],
//       combinationNames: List<String>.from(json['combinationNames']),
//       // colors: List<String>.from(json['colors']),
//       // sizes: List<String>.from(json['sizes']),
//       // materials: List<String>.from(json['materials']),
//       mainCatDocId: json['mainCatDocId'],
//       defaultSubCatDocId: json['subCatDocId'],
//       vendorDocId: json['vendorDocId'],
//       show: json['show'],
//       userType: json['userType'],
//       description: json['description'],
//       createdAt: json['createdAt'],
//       topSelling: json['topSelling'],
//       variants: Map.from(json['variants'])
//           .entries
//           .map((variantJson) =>
//               VariantModel.fromJson(variantJson.key, variantJson.value))
//           .toList(),
//       // variants: (json['variants'] as Map<String, dynamic>)
//       //     .map((variant) =>
//       //         VariantModel.fromJson(variant, variant as Map<String, dynamic>))
//       //     .toList(),
//     );
//   }
//   factory ProductModel.fromDocSnap(
//       DocumentSnapshot<Map<String, dynamic>> json) {
//     return ProductModel(
//       docId: json.id,
//       name: json['name'],
//       userType: json['userType'],
//       lowerName: json['lowerName'],
//       // colors: List<String>.from(json['colors']),
//       // sizes: List<String>.from(json['sizes']),
//       // materials: List<String>.from(json['materials']),
//       combinationNames: List<String>.from(json['combinationNames']),
//       createdAt: json['createdAt'],
//       mainCatDocId: json['mainCatDocId'],
//       defaultSubCatDocId: json['subCatDocId'],
//       vendorDocId: json['vendorDocId'],
//       show: json['show'],
//       description: json['description'],
//       topSelling: json['topSelling'],
//       variants: Map.from(json['variants'])
//           .entries
//           .map((variantJson) =>
//               VariantModel.fromJson(variantJson.key, variantJson.value))
//           .toList(),
//     );
//   }
// }
