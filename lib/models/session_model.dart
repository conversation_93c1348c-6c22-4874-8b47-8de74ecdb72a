import 'package:cloud_firestore/cloud_firestore.dart';

class SessionModel {
  final String docId;
  final String uId;
  final DateTime createdAt;
  final List<String> variantIds;
  final List<ToOrderVariantData> finalVariantIds;
  final String inquiryId;
  final String consultantId;
  final String stylerId;
  final DateTime? endedOn;
  final String userName;

  final bool isActive;
  final Map<String, dynamic>? lastMessage;
  final num? budget;

  SessionModel({
    this.lastMessage,
    required this.docId,
    required this.consultantId,
    required this.finalVariantIds,
    required this.createdAt,
    required this.userName,
    required this.endedOn,
    required this.inquiryId,
    required this.isActive,
    required this.stylerId,
    required this.variantIds,
    required this.uId,
    required this.budget,
  });

  factory SessionModel.fromSnap(DocumentSnapshot snap) {
    var data = snap.data() as Map<String, dynamic>;
    return SessionModel(
      docId: snap.id,
      uId: data['uId'] as String,
      userName: data.containsKey('userName') ? data['userName'] : '',
      createdAt: DateTime.fromMillisecondsSinceEpoch(data['createdAt']),
      variantIds: List<String>.from(data['variantIds'] ?? []),
      finalVariantIds: (data['finalVariantIds2'] as Map<String, dynamic>)
          .entries
          .map((e) => ToOrderVariantData.fromJson(e.key, e.value))
          .toList(),
      inquiryId: data['inquiryId'] as String,
      consultantId: data['consultantId'] as String,
      budget: data.containsKey('budget') ? data['budget'] as num? : null,
      stylerId: data['stylerId'] as String,
      endedOn: data['endedOn'] != null
          ? DateTime.fromMillisecondsSinceEpoch(data['endedOn'])
          : null,
      isActive: data['isActive'] as bool,
      lastMessage: data['lastMessage'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'uId': uId,
      'createdAt': Timestamp.fromDate(createdAt),
      'variantIds': variantIds,
      'inquiryId': inquiryId,
      'consultantId': consultantId,
      'stylerId': stylerId,
      'endedOn': endedOn != null ? Timestamp.fromDate(endedOn!) : null,
      'isActive': isActive,
      'lastMessage': lastMessage,
      'finalVariantIds2': finalVariantIds,
    };
  }

  factory SessionModel.fromJson(Map<String, dynamic> json, String docId) {
    return SessionModel(
        docId: docId,
        uId: json['uId'] as String,
        userName: json['userName'],
        createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt']),
        variantIds: List<String>.from(json['variantIds'] ?? []),
        finalVariantIds: (json['finalVariantIds2'] as Map<String, dynamic>?)
                ?.entries
                .map((e) => ToOrderVariantData.fromJson(e.key, e.value))
                .toList() ??
            [],
        inquiryId: json['inquiryId'] as String,
        budget: json.containsKey('budget') ? json['budget'] as num? : null,
        consultantId: json['consultantId'] as String,
        stylerId: json['stylerId'] as String,
        endedOn: json['endedOn'] != null
            ? DateTime.fromMillisecondsSinceEpoch(json['endedOn'])
            : null,
        isActive: json['isActive'] as bool,
        lastMessage: json['lastMessage'] as Map<String, dynamic>?);
  }
}

class MessagesModel {
  final String docId;
  final String type; // MESSAGE TYPE
  final String sessionId;
  final String? loadingText; // TO SHOW DATA WHILE LOADING
  final Map<String, dynamic> data;
  final String senderId;
  final DateTime sendAt;
  // final bool isMember;

  MessagesModel({
    required this.docId,
    required this.type,
    required this.sessionId,
    required this.loadingText,
    required this.data,
    required this.senderId,
    required this.sendAt,
  });

  factory MessagesModel.fromJson(Map<String, dynamic> json) {
    return MessagesModel(
      docId: json['docId'],
      type: json['type'],
      sessionId: json['sessionId'],
      loadingText: json['loadingText'],
      data: json['data'] ?? {},
      senderId: json['senderId'],
      sendAt: DateTime.fromMillisecondsSinceEpoch(json['sendAt']),

      // isMember: json['isMember'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'sessionId': sessionId,
      'loadingText': loadingText,
      'data': data,
      'senderId': senderId,
      'sendAt': sendAt,
    };
  }

  factory MessagesModel.fromSnap(DocumentSnapshot json) {
    return MessagesModel(
      docId: json.id,
      type: json['type'],
      sessionId: json['sessionId'],
      loadingText: json['loadingText'],
      data: json['data'] ?? {},
      senderId: json['senderId'],
      sendAt: DateTime.fromMillisecondsSinceEpoch(json['sendAt']),

      // isMember: json['isMember'],
    );
  }
}

class MessageTypes {
  static const text = 'text';
  static const booking = 'booking';
  static const package = 'package';
  static const images = 'images';
  static const file = 'file';
  static const set = 'set';
  static const info = 'info';
  static const orderAccRej = 'orderAccRej';
  static const payment = 'payment';
  static const deliveryDetails = 'deliveryDetails';
  static const getPrice = 'getPrice';
}

class ToOrderVariantData {
  final String variantId;
  int qty;
  final DateTime? deliveryDate;
  String customNote;

  final String id;
  ToOrderVariantData({
    required this.id,
    required this.variantId,
    required this.qty,
    required this.deliveryDate,
    required this.customNote,
  });
  factory ToOrderVariantData.fromJson(String id, Map<String, dynamic> json) {
    return ToOrderVariantData(
      id: id,
      variantId: json['variantId'] ?? '',
      qty: json['qty'],
      deliveryDate: json['deliveryDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['deliveryDate'])
          : null,
      customNote: json['customNote'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      id: {
        'variantId': variantId,
        'qty': qty,
        'deliveryDate': deliveryDate?.millisecondsSinceEpoch,
        'customNote': customNote,
      }
    };
  }

  Map<String, dynamic> toJson2() {
    return {
      'variantId': variantId,
      'qty': qty,
      'deliveryDate': deliveryDate?.millisecondsSinceEpoch,
      'customNote': customNote,
    };
  }
}

class StylerProducts {
  final String docId;
  final String title;
  final List<SelectedProduct> selectedProducts;

  StylerProducts({
    required this.title,
    required this.selectedProducts,
    required this.docId,
  });

  factory StylerProducts.fromJson(Map<String, dynamic> json) {
    return StylerProducts(
      docId: json['docId'] ?? '',
      title: json['title'] ?? '',
      selectedProducts: (json['products'] as List<dynamic>)
          .map((e) => SelectedProduct.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
  factory StylerProducts.fromSnap(
      QueryDocumentSnapshot<Map<String, dynamic>> json) {
    return StylerProducts(
      docId: json.id,
      title: json['title'] ?? '',
      selectedProducts: (json['products'] as List<dynamic>)
          .map((e) => SelectedProduct.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'products': selectedProducts.map((e) => e.toJson()).toList(),
    };
  }
}

class SelectedProduct {
  final String variantId;
  final bool isSelected;

  SelectedProduct({
    required this.variantId,
    required this.isSelected,
  });

  factory SelectedProduct.fromJson(Map<String, dynamic> json) {
    return SelectedProduct(
      variantId: json['variantId'] ?? '',
      isSelected: json['isSelected'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'variantId': variantId,
      'isSelected': isSelected,
    };
  }
}
