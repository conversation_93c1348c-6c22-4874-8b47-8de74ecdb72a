import 'package:cloud_firestore/cloud_firestore.dart';

class TeamModel {
  final String docId;
  final String name;
  final String email;
  final String phone;
  final Timestamp createdAt;
  final String address;
  final List<String> subcatids;
  final String userType;
  final String? speciality;
  final String password;
  final bool isActive;
  final bool isAvailable;

  TeamModel({
    required this.docId,
    required this.name,
    required this.email,
    required this.phone,
    required this.createdAt,
    required this.address,
    required this.subcatids,
    required this.userType,
    required this.speciality,
    required this.password,
    required this.isActive,
    required this.isAvailable,
  });

  // Converts a VendorModel instance to a JSON map for Firestore
  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'name': name,
      'email': email,
      'phone': phone,
      'createdAt': createdAt,
      'address': address,
      'subcatids': subcatids,
      'userType': userType,
      'speciality': speciality,
      'isActive': isActive,
      'isAvailable': isAvailable,
    };
  }

  // Creates a VendorModel instance from a JSON map (useful when fetching data)
  factory TeamModel.fromJson(Map<String, dynamic> json) {
    try {
      return TeamModel(
        docId: json['docId'],
        name: json['name'],
        email: json['email'],
        phone: json['phone'],
        createdAt: json['createdAt'] as Timestamp,
        address: json['address'],
        userType: json['userType'],
        speciality: json['speciality'],
        password: json['password'],
        isActive: json['isActive'],
        isAvailable: json['isAvailable'],
        subcatids: List<String>.from(json['subcatids']),
      );
    } catch (e) {
      print(
          'Error parsing TeamModel.fromJson for docId: ${json['docId']}, error: $e');
      rethrow; // Skip this entry by rethrowing the error
    }
  }

  // Creates a VendorModel instance from a Firestore document snapshot
  factory TeamModel.fromSnap(QueryDocumentSnapshot<Map<String, dynamic>> data) {
    return TeamModel(
      docId: data.id,
      name: data['name'],
      email: data['email'],
      phone: data['phone'],
      createdAt: data['createdAt'] as Timestamp,
      address: data['address'],
      userType: data['userType'],
      isActive: data['isActive'],
      speciality: data['speciality'],
      isAvailable:
          data.data().containsKey('isAvailable') ? data['isAvailable'] : false,
      password: data['password'],
      subcatids: List<String>.from(data['subcatids']),
    );
  }

  factory TeamModel.fromDocSnap(DocumentSnapshot<Map<String, dynamic>> data) {
    return TeamModel(
      docId: data.id,
      name: data['name'],
      email: data['email'],
      phone: data['phone'],
      createdAt: data['createdAt'] as Timestamp,
      address: data['address'],
      userType: data['userType'],
      speciality: data['speciality'],
      isActive: data['isActive'],
      password: data['password'],
      isAvailable: data.data()?.containsKey('isAvailable') ?? false
          ? data['isAvailable']
          : false,
      subcatids: List<String>.from(data['subcatids']),
    );
  }
}
