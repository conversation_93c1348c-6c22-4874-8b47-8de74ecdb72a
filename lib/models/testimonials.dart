import 'package:cloud_firestore/cloud_firestore.dart';

class TestimonialsModel {
  final String docId;
  final String title;
  final String videoLink;
  final DateTime createdAt;
  final bool isActive;

  TestimonialsModel({
    required this.docId,
    required this.title,
    required this.videoLink,
    required this.createdAt,
    required this.isActive,
  });

  // Converts a TestimonialsModel instance to a JSON map for Firestore
  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'title': title,
      'videoLink': videoLink,
      'isActive': isActive,
      'createdAt': createdAt, // Convert DateTime to Timestamp
    };
  }

  // Creates a TestimonialsModel instance from a JSON map (useful when fetching data)
  factory TestimonialsModel.fromJson(Map<String, dynamic> json) {
    try {
      return TestimonialsModel(
        docId: json['docId'],
        title: json['title'],
        isActive: json['isActive'],
        videoLink: json['videoLink'],
        createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt']),
      );
    } catch (e) {
      print(
          'Error parsing TestimonialsModel.fromJson for docId: ${json['docId']}, error: $e');
      rethrow; // Skip this entry by rethrowing the error
    }
  }

  // Creates a TestimonialsModel instance from a Firestore document snapshot
  factory TestimonialsModel.fromSnap(
      QueryDocumentSnapshot<Map<String, dynamic>> data) {
    try {
      return TestimonialsModel(
        docId: data.id,
        title: data['title'],
        isActive: data['isActive'],
        videoLink: data['videoLink'],
        createdAt: DateTime.fromMillisecondsSinceEpoch(data['createdAt']),
      );
    } catch (e) {
      print(
          'Error parsing TestimonialsModel.fromSnap for docId: ${data.id}, error: $e');
      rethrow; // Skip this entry by rethrowing the error
    }
  }
}
