import 'package:cloud_firestore/cloud_firestore.dart';

import 'address_model.dart';
import 'package_model.dart';

class UserModel {
  final String? docId;
  final String? name;
  final String? phone;
  final String email;
  final String password;
  final String? defaultAddressId;
  final List<String>? favourites;
  final List<AddressModel> addresses;
  final List<PointsModel> services;

  UserModel({
    required this.docId,
    required this.name,
    required this.phone,
    required this.email,
    // required this.authEmail,
    required this.password,
    required this.defaultAddressId,
    required this.favourites,
    required this.addresses,
    required this.services,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    try {
      return UserModel(
        docId: json['docId'],
        name: json['name'],
        phone: json['phone'],
        // authEmail: json['authEmail'],
        defaultAddressId: json['defaultAddressId'],
        password: json['password'] as String,
        email: json['email'],
        services: (json['services'] as List<dynamic>? ?? [])
            .map((e) => PointsModel.fromJson(e as Map<String, dynamic>))
            .toList(),
        favourites: json.containsKey('favourites') && json['favourites'] is List
            ? List<String>.from(json['favourites'])
            : [],
        addresses: Map.castFrom(json['addresses'])
            .entries
            .map((e) => AddressModel.fromJson(e.key, e.value))
            .toList(),
      );
    } catch (e) {
      print(
          'Error parsing UserModel.fromJson for docId: ${json['docId']}, error: $e');
      rethrow; // Skip this entry by rethrowing the error
    }
  }

  factory UserModel.fromDocSnap(DocumentSnapshot<Map<String, dynamic>> json) {
    try {
      print('user');
      print('json.id========');
      print(json.id);
      return UserModel(
        docId: json.id, // Handle missing docId
        name: json['name'] ?? '', // If name is null, assign empty string
        phone: json['phone'] ?? '', // Assign an empty string if phone is null
        // authEmail: json['authEmail'] ?? '',
        defaultAddressId:
            json['defaultAddressId'], // Leave as null if it's null
        password: json['password'] ?? '',
        email: json['email'] ?? '',
        services: (json.data()?.containsKey('services') ?? false)
            ? (json['services'] as List<dynamic>? ?? [])
                .map((e) => PointsModel.fromJson(e as Map<String, dynamic>))
                .toList()
            : [],
        // If 'favourites' exists and is a list, convert it; otherwise, return an empty list
        favourites: (json.data()?.containsKey('favourites') ?? false) &&
                json['favourites'] is List
            ? List<String>.from(json['favourites'])
            : [],

        // Handling addresses similarly, check if it's a map or a list
        addresses: Map.castFrom(json['addresses'])
            .entries
            .map((e) => AddressModel.fromJson(e.key, e.value))
            .toList(),
      );
    } catch (e) {
      print(
          'Error parsing UserModel.fromDocSnap for docId: ${json.id}, error: $e');
      rethrow; // Skip this entry by rethrowing the error
    }
  }
  factory UserModel.fromSnap(QueryDocumentSnapshot json) {
    try {
      Map<String, dynamic> data = json.data() as Map<String, dynamic>;
      return UserModel(
        docId: json.id, // Handle missing docId
        name: data['name'] ?? '', // If name is null, assign empty string
        phone: data['phone'] ?? '', // Assign an empty string if phone is null
        // authEmail: json['authEmail'] ?? '',
        defaultAddressId:
            data['defaultAddressId'], // Leave as null if it's null
        password: data['password'] ?? '',
        email: data['email'] ?? '',
        services: (data.containsKey('services'))
            ? (data['services'] as List<dynamic>? ?? [])
                .map((e) => PointsModel.fromJson(e as Map<String, dynamic>))
                .toList()
            : [],
        // If 'favourites' exists and is a list, convert it; otherwise, return an empty list
        favourites:
            (data.containsKey('favourites')) && data['favourites'] is List
                ? List<String>.from(data['favourites'])
                : [],

        // Handling addresses similarly, check if it's a map or a list
        addresses: Map.castFrom(data['addresses'])
            .entries
            .map((e) => AddressModel.fromJson(e.key, e.value))
            .toList(),
      );
    } catch (e) {
      print(
          'Error parsing UserModel.fromSnap for docId: ${json.id}, error: $e');
      rethrow; // Skip this entry by rethrowing the error
    }
  }

  Map<String, dynamic> toJson() => {
        'docId': docId,
        'name': name,
        'phone': phone,
        'email': email,
        'favourites': favourites,
        'addresses': addresses.map((e) => e.toJson2()).toList(),
        // 'services':
      };
}
