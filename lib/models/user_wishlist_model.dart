import 'package:cloud_firestore/cloud_firestore.dart';

class UserWishlistModel {
  final String docId;
  final String uId;
  final String title;
  final String desc;
  final DateTime createdAt;
  final String createdBy;
  final String createdByType;
  final List<VariantDetails> variantIds;

  UserWishlistModel({
    required this.docId,
    required this.uId,
    required this.variantIds,
    required this.createdAt,
    required this.title,
    required this.desc,
    required this.createdBy,
    required this.createdByType,
  });
  factory UserWishlistModel.fromSnap(
      QueryDocumentSnapshot<Map<String, dynamic>> json) {
    return UserWishlistModel(
      docId: json.id,
      uId: json['uId'],
      variantIds: (json['variantIds'] as List<dynamic>)
          .map((e) => VariantDetails.fromJson(e as Map<String, dynamic>))
          .toList(),
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt']),
      title: json['title'],
      desc: json['desc'],
      createdBy: json['createdBy'],
      createdByType: json['createdByType'],
    );
  }

  Map<String, dynamic> toJson2() {
    return {
      'uId': uId,
      'variantIds': variantIds,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'title': title,
      'desc': desc,
      'createdBy': createdBy,
      'createdByType': createdByType,
    };
  }

  factory UserWishlistModel.fromJson(Map<String, dynamic> json) {
    return UserWishlistModel(
      docId: json['docId'],
      uId: json['uId'],
      variantIds: (json['variantIds'] as List<dynamic>)
          .map((e) => VariantDetails.fromJson(e as Map<String, dynamic>))
          .toList(),
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt']),
      title: json['title'],
      desc: json['desc'],
      createdBy: json['createdBy'],
      createdByType: json['createdByType'],
    );
  }
}

class VariantDetails {
  final String id;
  final String name;
  final String description;
  final String image;
  final String variantId;
  final String productId;
  final int addedOn;
  final String addedBy;
  final String addedByType;
  final num price;

  VariantDetails({
    required this.id,
    required this.name,
    required this.description,
    required this.image,
    required this.variantId,
    required this.productId,
    required this.addedOn,
    required this.addedBy,
    required this.addedByType,
    required this.price,
  });

  /// Create object from JSON
  factory VariantDetails.fromJson(Map<String, dynamic> json) {
    return VariantDetails(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      image: json['image'] as String,
      variantId: json['variantId'] as String,
      productId: json['productId'] as String,
      addedOn: (json['addedOn'] as num).toInt(),
      addedBy: json['addedBy'] as String,
      addedByType: json['addedByType'] as String,
      price: json['price'] as num,
    );
  }

  /// Convert object to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'image': image,
      'variantId': variantId,
      'productId': productId,
      'addedOn': addedOn,
      'addedBy': addedBy,
      'addedByType': addedByType,
      'price': price,
    };
  }
}
