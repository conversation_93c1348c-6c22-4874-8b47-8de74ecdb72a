import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import '../services/image_picker.dart';

class PriceRangeModel {
  String startQty;
  String endQty;
  num price;

  PriceRangeModel({
    required this.startQty,
    required this.endQty,
    required this.price,
  });

  factory PriceRangeModel.fromJson(Map<String, dynamic> json) =>
      PriceRangeModel(
        startQty: json['startQty'] as String,
        endQty: json['endQty'] as String,
        price: json['price'],
      );

  Map<String, dynamic> toJson() => {
        'startQty': startQty,
        'endQty': endQty,
        'price': price,
      };

  PriceRangeInputModel toInputPriceRange() {
    return PriceRangeInputModel(
        startQty: TextEditingController(text: startQty),
        endQty: TextEditingController(text: endQty),
        price: TextEditingController(text: price.toString()));
  }
}

class PriceRangeInputModel {
  final TextEditingController startQty;
  final TextEditingController endQty;
  final TextEditingController price;

  PriceRangeInputModel({
    required this.startQty,
    required this.endQty,
    required this.price,
  });

  static newRange() {
    return PriceRangeInputModel(
      startQty: TextEditingController(),
      endQty: TextEditingController(),
      price: TextEditingController(),
    );
  }
}

class DummyModel {
  final Map<String, dynamic> newData;

  DummyModel({
    required this.newData,
  });
}

class NewVariantModel {
  final String? docId;
  final String? id;
  final String? aqpiId;
  final String productId;
  final String subCatId;
  final String mainCatId;
  final String lowerName;
  final List<String> images;
  List<SelectedImage> newImages;
  String description;
  bool defaultt;
  bool show;
  num fixedprice;
  String priceType; // priceType = (range, fixed price, inquiry)
  List<PriceRangeModel> priceRange;
  final Map<String, dynamic> detailTypes;
  num originalPrice;
  num sellingPrice;

  NewVariantModel({
    this.docId,
    required this.id,
    required this.aqpiId,
    required this.newImages,
    required this.images,
    required this.description,
    required this.defaultt,
    required this.show,
    required this.lowerName,
    required this.fixedprice,
    required this.priceType,
    required this.priceRange,
    required this.detailTypes,
    required this.productId,
    required this.subCatId,
    required this.mainCatId,
    required this.originalPrice,
    required this.sellingPrice,
  });

  NewVariantModel updateValue(String key, dynamic value) {
    switch (key) {
      case 'description':
        description = value;
        break;
      case 'defaultt':
        defaultt = value;
        break;
      case 'show':
        show = value == 'Available';
        break;
      // case 'lowerName':
      //   lowerName = value;
      //   break;
      case 'fixedprice':
        fixedprice = value;
        break;
      case 'originalprice':
        originalPrice = value;
        break;

      case 'sellprice':
        sellingPrice = value;
        break;
      case 'priceType':
        priceType = value;
        break;
      case 'images':
        images.clear();
        images.addAll(List<String>.from(value));
        break;
      case 'newImages':
        newImages.clear();
        newImages.addAll(List<SelectedImage>.from(value));
        break;
      case 'priceRange':
        priceRange.clear();
        priceRange.addAll(
          (value as List<dynamic>)
              .map(
                  (e) => e is PriceRangeModel ? e : PriceRangeModel.fromJson(e))
              .toList(),
        );
        break;

      default:
        detailTypes[key] = value;
        return this;
    }
    return this;
  }
/* 
  NewVariantModel updateValue(String ky, dynamic value) {
    switch (ky) {
      case 'fixedPrice':
        fixedprice = value;
        return this;

      default:
        return this;
    }
  } */

  factory NewVariantModel.fromJson(Map<String, dynamic> json) {
    return NewVariantModel(
      id: json['id'],
      aqpiId: json['aqpiId'],
      docId: json["docId"] ?? "",
      productId: json["productId"] ?? "",
      subCatId: json["subCatId"] ?? "",
      mainCatId: json["mainCatId"] ?? "",
      images: List<String>.from(json["images"] ?? []),
      newImages: [],
      lowerName: json.containsKey('lowerName') ? json['lowerName'] : '',
      description: json["description"] ?? "",
      defaultt: json["defaultt"] ?? false,
      show: json["show"] ?? false,
      fixedprice: json["fixedprice"] ?? 0,
      priceType: json["priceType"] ?? "fixed price",
      priceRange: (json["priceRange"] as List<dynamic>?)
              ?.map((e) => PriceRangeModel.fromJson(e))
              .toList() ??
          [],
      detailTypes: json["detailTypes"] ?? {},
      originalPrice: json.containsKey('originalprice') ?? false
          ? json['originalprice']
          : 0,
      sellingPrice:
          json.containsKey('sellingprice') ?? false ? json['sellingprice'] : 0,
    );
  }
  factory NewVariantModel.fromDocSnap(
      DocumentSnapshot<Map<String, dynamic>> json) {
    return NewVariantModel(
      docId: json.id,
      id: json["id"],
      aqpiId:
          (json.data()?.containsKey('aqpiId') ?? false) ? json["aqpiId"] : '',
      productId: json["productId"] ?? "",
      subCatId: json["subCatId"] ?? "",
      mainCatId: json["mainCatId"] ?? "",
      lowerName: json.data()?.containsKey('lowerName') ?? false
          ? json["lowerName"]
          : "-",
      images: List<String>.from(json["images"] ?? []),
      newImages: [],
      description: json["description"] ?? "",
      defaultt: json["defaultt"] ?? false,
      show: json["show"] ?? false,
      fixedprice: json["fixedprice"] ?? 0,
      priceType: json["priceType"] ?? "fixed price",
      priceRange: (json["priceRange"] as List<dynamic>?)
              ?.map((e) => PriceRangeModel.fromJson(e))
              .toList() ??
          [],
      detailTypes: json.data()?.containsKey('detailTypes') ?? false
          ? (json['detailTypes'] as Map<String, dynamic>?)
                  ?.map((key, value) => MapEntry(key, value)) ??
              {}
          : {},
      originalPrice: json.data()?.containsKey('originalprice') ?? false
          ? json['originalprice']
          : 0,
      sellingPrice: json.data()?.containsKey('sellingprice') ?? false
          ? json['sellingprice']
          : 0,
    );
  }
  factory NewVariantModel.fromSnap(
      QueryDocumentSnapshot<Map<String, dynamic>> json) {
    return NewVariantModel(
      docId: json.id,
      id: json.data().containsKey('id') ? json["id"] : '',
      aqpiId: json.data().containsKey('aqpiId') ? json["aqpiId"] : '',
      productId: json["productId"] ?? "",
      subCatId: json["subCatId"] ?? "",
      mainCatId: json["mainCatId"] ?? "",
      images: List<String>.from(json["images"] ?? []),
      newImages: [],
      lowerName: json.data().containsKey('lowerName') ? json["lowerName"] : "-",
      description: json["description"] ?? "",
      defaultt: json["defaultt"] ?? false,
      show: json["show"] ?? false,
      fixedprice: json["fixedprice"] ?? 0,
      priceType: json["priceType"] ?? "fixed price",
      priceRange: (json["priceRange"] as List<dynamic>?)
              ?.map((e) => PriceRangeModel.fromJson(e))
              .toList() ??
          [],
      detailTypes: json.data().containsKey('detailTypes')
          ? (json['detailTypes'] as Map<String, dynamic>).map(
              (key, value) => MapEntry(key, value),
            )
          : {},
      originalPrice: json.data()?.containsKey('originalprice') ?? false
          ? json['originalprice']
          : 0,
      sellingPrice: json.data()?.containsKey('sellingprice') ?? false
          ? json['sellingprice']
          : 0,
    );
  }
}

/* 
class VariantModel {
  final String id;
  final List<String> images;
  List<SelectedImage> newImages;
  String description;
  bool defaultt;
  bool show;
  num fixedprice;
  String priceType; // priceType = (range, fixed price, inquiry)
  List<PriceRangeModel> priceRange;
  final Map<String, dynamic> detailTypes;

  VariantModel({
    required this.id,
    required this.newImages,
    required this.images,
    required this.description,
    required this.defaultt,
    required this.show,
    required this.fixedprice,
    required this.priceType,
    required this.priceRange,
    required this.detailTypes,
  });

  /// Convert a VariantModel instance to JSON
  Map<String, dynamic> toJson() => {
        id: {
          'images': images,
          'description': description,
          'default': defaultt,
          'show': show,
          'fixedprice': fixedprice,
          'priceType': priceType,
          'priceRange': priceRange.map((range) => range.toJson()).toList(),
          'detailTypes': detailTypes,
        }
      };

  /// Create a VariantModel instance from JSON
  factory VariantModel.fromJson(String id, Map<String, dynamic> json) {
    return VariantModel(
      id: id,
      images: List<String>.from(json['images']),
      newImages: [],
      description: json['description'],
      defaultt: json['default'],
      show: json['show'],
      fixedprice: json.containsKey('fixedprice') ? json['fixedprice'] : 0,
      priceType: json.containsKey('priceType') ? json['priceType'] : '',
      priceRange: json.containsKey('priceRange')
          ? (json['priceRange'] as List<dynamic>)
              .map((range) =>
                  PriceRangeModel.fromJson(range as Map<String, dynamic>))
              .toList()
          : [],
      detailTypes: json.containsKey('detailTypes')
          ? Map<String, dynamic>.from(json['detailTypes'])
          : {},
    );
  }
}

class VariantModel2 {
  final String id;
  final List<String> images;
  final String? color;
  final String? material;
  final String description;
  final bool defaultt;
  final bool show;
  final String? size;
  final num fixedprice;
  final String priceType; // priceType= (range, fixed price, inquiry)
  // final num? fixedPrice;
  final List<PriceRangeModel> priceRange;
  final Map<String, dynamic> detailTypes;

  VariantModel2({
    required this.id,
    required this.images,
    required this.color,
    required this.material,
    required this.priceRange,
    required this.description,
    required this.defaultt,
    required this.show,
    required this.fixedprice,
    required this.priceType,
    // required this.fixedPrice,
    required this.size,
    required this.detailTypes,
  });

  factory VariantModel2.fromJson(String keyId, Map<String, dynamic> json) {
    return VariantModel2(
        id: keyId,
        images: List<String>.from(json['images']),
        color: json['color'],
        material: json['material'],
        description: json['description'],
        defaultt: json['default'],
        show: json['show'],
        size: json['size'],
        fixedprice: json['price'],
        priceRange: json.containsKey('priceRange')
            ? (json['priceRange'] as List<dynamic>)
                .map((e) => PriceRangeModel.fromJson(e as Map<String, dynamic>))
                .toList()
            : [],
        priceType: json.containsKey('priceType') ? json['priceType'] : '',
        detailTypes:
            json.containsKey('detailTypes') ? json['detailTypes'] : {});
  }

  Map<String, dynamic> toJson() => {
        id: {
          'images': images,
          'color': color,
          'material': material,
          'description': description,
          'default': defaultt,
          'show': show,
          'size': size,
          'price': fixedprice,
          'priceRange': priceRange,
          'priceType': priceType,
          'detailTypes': detailTypes,
        },
      };

  toInputModel() {
    return VariantInputModel2(
      vId: id,
      sizeCtrl: TextEditingController(text: size.toString()),
      colorCtrl: TextEditingController(text: color),
      show: show,
      descriptionCtrl: TextEditingController(text: description),
      materialCtrl: TextEditingController(text: material),
      uploadedImages: images,
      newImages: [],
      priceCtrl: TextEditingController(text: fixedprice.toString()),
      defaultt: defaultt,
    );
    // return VariantInputModel(
    //   size: TextEditingController(text: size),
    //   color: TextEditingController(text: color),
    //   // mrp: TextEditingController(text: mrp.toString()),
    //   qty: TextEditingController(text: qty.toString()),
    //   salePrice: TextEditingController(text: salePrice.toString()),
    //   baseItem: baseItem,
    //   colored: color != null,
    //   uploadedImages: images,
    //   newImages: [],
    //   vId: vId,
    // );
  }
}

class VariantInputModel2 {
  final String vId;
  final TextEditingController sizeCtrl;
  final TextEditingController colorCtrl;
  final TextEditingController descriptionCtrl;
  final TextEditingController materialCtrl;
  final TextEditingController priceCtrl;
  bool defaultt;
  bool show;
  final List<String> uploadedImages;
  final List<SelectedImage> newImages;

  VariantInputModel2({
    required this.vId,
    required this.sizeCtrl,
    required this.colorCtrl,
    required this.show,
    required this.descriptionCtrl,
    required this.materialCtrl,
    required this.priceCtrl,
    this.defaultt = false,
    required this.uploadedImages,
    required this.newImages,
  });

  static newVariant() {
    return VariantInputModel2(
      vId: getRandomId(8),
      show: true,
      sizeCtrl: TextEditingController(),
      priceCtrl: TextEditingController(),
      colorCtrl: TextEditingController(),
      materialCtrl: TextEditingController(),
      descriptionCtrl: TextEditingController(),
      uploadedImages: [],
      newImages: [],
    );
  }
}
 */
