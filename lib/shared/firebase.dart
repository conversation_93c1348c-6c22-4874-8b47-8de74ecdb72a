import 'package:cloud_functions/cloud_functions.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';

const testMode = false;

class FBAuth {
  static final auth = FirebaseAuth.instance;
}

class FBFireStore {
  static final fb = FirebaseFirestore.instance;
  static final teamMember =
      testMode ? fb.collection('teamMemberTest') : fb.collection('teamMember');
  static final users =
      testMode ? fb.collection('usersTest') : fb.collection('users');
  static final blogs =
      testMode ? fb.collection('blogsTest') : fb.collection('blogs');
  static final offerings =
      testMode ? fb.collection('offeringsTest') : fb.collection('offerings');
  static final subCategories = testMode
      ? fb.collection('subcategoriesTest')
      : fb.collection('subcategories');
  static final tags =
      testMode ? fb.collection('tagsTest') : fb.collection('tags');
  static final orders =
      testMode ? fb.collection('ordersTest') : fb.collection('orders');
  static final packageOrders = testMode
      ? fb.collection('packageOrdersTest')
      : fb.collection('packageOrders');
  static final wishlist =
      testMode ? fb.collection('wishlistTest') : fb.collection('wishlist');

  static final products =
      testMode ? fb.collection('productsTest') : fb.collection('products');
  static final testimonials = testMode
      ? fb.collection('testimonialsTest')
      : fb.collection('testimonials');
  static final gallery =
      testMode ? fb.collection('galleryTest') : fb.collection('gallery');
  static final vendors =
      testMode ? fb.collection('vendorsTest') : fb.collection('vendors');
  static final questionaire = testMode
      ? fb.collection('questionaireTest')
      : fb.collection('questionaire');
  static final variants =
      testMode ? fb.collection('variantsTest') : fb.collection('variants');
  static final packages =
      testMode ? fb.collection('packagesTest') : fb.collection('packages');
  static final transaction = testMode
      ? fb.collection('transactionTest')
      : fb.collection('transaction');
  static final deliveryDetails = testMode
      ? fb.collection('deliveryDetailsTest')
      : fb.collection('deliveryDetails');

  static final bookings =
      testMode ? fb.collection('bookingsTest') : fb.collection('bookings');
  static final sessions =
      testMode ? fb.collection('sessionsTest') : fb.collection('sessions');
  static final vendorOrder = testMode
      ? fb.collection('vendorOrderTest')
      : fb.collection('vendorOrder');

  static final settings =
      fb.collection('settings').doc(testMode ? 'setsTest' : 'sets');
  static final blocked = settings.collection('blocked');

  // static final amenitiesMore = fb.collection('amenities&more');
  // static final cities = fb.collection('cities');
  // static final areas = fb.collection('areas');
  // static final settings = fb.collection('settings');
}

class FBStorage {
  static final fbstore = FirebaseStorage.instance;
  static final offering = testMode
      ? fbstore.ref().child('categoryTest')
      : fbstore.ref().child('category');
  static final gallery = testMode
      ? fbstore.ref().child('galleryTest')
      : fbstore.ref().child('gallery');
  static final subcategory = testMode
      ? fbstore.ref().child('subcategoryTest')
      : fbstore.ref().child('subcategory');
  static final products = testMode
      ? fbstore.ref().child('productsTest')
      : fbstore.ref().child('products');
  static final sessions = testMode
      ? fbstore.ref().child('sessionsTest')
      : fbstore.ref().child('sessions');
  static final blogs = testMode
      ? fbstore.ref().child('blogsTest')
      : fbstore.ref().child('blogs');
  static final misc =
      testMode ? fbstore.ref().child('miscTest') : fbstore.ref().child('misc');
  // static final banners = fbstore.ref().child('banner');
  // static final amenity = fbstore.ref().child('amenity');
  // static final food = fbstore.ref().child('food');
  // static final otherCertis = fb.ref().child('otherCertis');
}

class FBFunctions {
  static final ff = FirebaseFunctions.instance;
}
