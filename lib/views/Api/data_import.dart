// ignore_for_file: dead_code

import 'dart:async';
import 'dart:convert';
import 'package:dart_openai/dart_openai.dart';

// Future<List<Map<String, dynamic>>> convertProductsOnly(
//     List<Map<String, dynamic>> vendorData, String apiKey) async {
//   OpenAI.apiKey = true
//       ? apiKey
//       : "********************************************************************************************************************************************************************";
//   // : "********************************************************************************************************************************************************************";

//   final productTool = OpenAIToolModel(
//     type: "function",
//     function: OpenAIFunctionModel.withParameters(
//       name: "convert_vendor_to_products_main",
//       description:
//           "Converts vendor product (excluding variants) to product JSON. and make sure brand data like `The Hub, 'SHopify` names should be in the data like description and all ",
//       // List only main product fields, exclude variants
//       parameters: [
//         // ...same fields as before, except exclude "variants"
//       ],
//     ),
//   );

//   // messages: system, instruction, and original vendorData json

//   final messages = [
//     OpenAIChatCompletionChoiceMessageModel(
//       role: OpenAIChatMessageRole.system,
//       content: [
//         OpenAIChatCompletionChoiceMessageContentItemModel.text(
//           "You are a conversion assistant.",
//         ),
//       ],
//     ),
//     OpenAIChatCompletionChoiceMessageModel(
//       role: OpenAIChatMessageRole.user,
//       content: [
//         OpenAIChatCompletionChoiceMessageContentItemModel.text(
//           "Convert the provided vendor JSON into product JSON format with all vendor data, EXCLUDING variants.",
//         ),
//       ],
//     ),
//     OpenAIChatCompletionChoiceMessageModel(
//       role: OpenAIChatMessageRole.user,
//       content: [
//         OpenAIChatCompletionChoiceMessageContentItemModel.text(
//           jsonEncode(vendorData),
//         ),
//       ],
//     ),
//   ];
//   int maxRetries = 8;
//   int attempt = 0;

//   while (attempt < maxRetries) {
//     try {
//       attempt++;
//       final chat = await OpenAI.instance.chat.create(
//         model: "gpt-4o",
//         messages: messages,
//         tools: [productTool],
//       ).timeout(Duration(minutes: 2));

//       final toolCalls = chat.choices.first.message.toolCalls;

//       if (toolCalls == null || toolCalls.isEmpty) {
//         throw Exception("OpenAI did not return any function calls.");
//       }
//       final argsParsed = jsonDecode(toolCalls.first.function.arguments);

//       return [argsParsed];
//     } on TimeoutException catch (e) {
//       print("⏱ Timeout on attempt $attempt: $e");
//     } catch (e) {
//       print("❌ Error on attempt $attempt: $e");
//     }

//     print("Retrying... (attempt $attempt)");
//     await Future.delayed(Duration(seconds: 2)); // Optional delay before retry
//   }
//   try {
//     final chat = await OpenAI.instance.chat.create(
//       model: "gpt-4o",
//       messages: messages,
//       tools: [productTool],
//     ).timeout(Duration(minutes: 2));

//     final toolCalls = chat.choices.first.message.toolCalls;

//     if (toolCalls == null || toolCalls.isEmpty) {
//       throw Exception("OpenAI did not return any function calls.");
//     }
//     final argsParsed = jsonDecode(toolCalls.first.function.arguments);

//     return [argsParsed];
//     // process chat
//   } on TimeoutException catch (e) {
//     print("TimeoutException caught: $e");
//     rethrow;
//   } catch (e) {
//     print("Other exception: $e");
//     return [];
//   }
// }

// Future<List<Map<String, dynamic>>> convertProductVariants(
//     List<Map<String, dynamic>> vendorData, String apiKey) async {
//   OpenAI.apiKey = true
//       ? apiKey
//       : "********************************************************************************************************************************************************************"; // : "********************************************************************************************************************************************************************";

//   final variantTool = OpenAIToolModel(
//     type: "function",
//     function: OpenAIFunctionModel.withParameters(
//       name: "convert_vendor_variants",
//       description:
//           "Converts vendor product variants to variant JSON format. and make sure brand data like `The Hub, 'SHopify` names should be in the data like description and all ",
//       parameters: [
//         // Only the "variants" field schema
//       ],
//     ),
//   );

//   final messages = [
//     OpenAIChatCompletionChoiceMessageModel(
//       role: OpenAIChatMessageRole.system,
//       content: [
//         OpenAIChatCompletionChoiceMessageContentItemModel.text(
//           "You are a conversion assistant.",
//         ),
//       ],
//     ),
//     OpenAIChatCompletionChoiceMessageModel(
//       role: OpenAIChatMessageRole.user,
//       content: [
//         OpenAIChatCompletionChoiceMessageContentItemModel.text(
//           "Convert the provided vendor JSON variants into the variants JSON format according to the schema.",
//         ),
//       ],
//     ),
//     OpenAIChatCompletionChoiceMessageModel(
//       role: OpenAIChatMessageRole.user,
//       content: [
//         OpenAIChatCompletionChoiceMessageContentItemModel.text(
//           jsonEncode(vendorData),
//         ),
//       ],
//     ),
//   ];
//   int maxRetries = 8;
//   int attempt = 0;

//   while (attempt < maxRetries) {
//     try {
//       attempt++;
//       final chat = await OpenAI.instance.chat.create(
//         model: "gpt-4o",
//         messages: messages,
//         tools: [variantTool],
//       ).timeout(Duration(minutes: 2));

//       final toolCalls = chat.choices.first.message.toolCalls;

//       if (toolCalls == null || toolCalls.isEmpty) {
//         throw Exception("OpenAI did not return any function calls.");
//       }
//       final argsParsed = jsonDecode(toolCalls.first.function.arguments);

//       return [argsParsed];
//     } on TimeoutException catch (e) {
//       print("⏱ Timeout on attempt $attempt: $e");
//     } catch (e) {
//       print("❌ Error on attempt $attempt: $e");
//     }

//     print("Retrying... (attempt $attempt)");
//     await Future.delayed(Duration(seconds: 2)); // Optional delay before retry
//   }
//   try {
//     final chat = await OpenAI.instance.chat.create(
//       model: "gpt-4o",
//       messages: messages,
//       tools: [variantTool],
//     ).timeout(Duration(minutes: 2));

//     final toolCalls = chat.choices.first.message.toolCalls;

//     if (toolCalls == null || toolCalls.isEmpty) {
//       throw Exception("OpenAI did not return any function calls.");
//     }
//     final argsParsed = jsonDecode(toolCalls.first.function.arguments);

//     return [argsParsed];
//   } on TimeoutException catch (e) {
//     print("TimeoutException caught: $e");
//     rethrow;
//   } catch (e) {
//     print("Other exception: $e");
//     return [];
//   }
// }

// Future<List<Map<String, dynamic>>> combineProductAndVariants(
//     List<Map<String, dynamic>> products,
//     List<Map<String, dynamic>> variants) async {
//   for (int i = 0; i < products.length; i++) {
//     products[i]['variants'] = variants[i];
//   }
//   return products;
// }

Future<List<Map<String, dynamic>>> convertVendorToProducts(
    List<Map<String, dynamic>> vendorData, String apiKey) async {
  OpenAI.apiKey = true
      ? apiKey
      : "********************************************************************************************************************************************************************";

  final convertVendorToProductsTool = OpenAIToolModel(
    type: "function",
    function: OpenAIFunctionModel.withParameters(
      name: "convert_vendor_to_products",
      description:
          "Converts vendor product data into the products JSON format for an e-commerce wedding shopping system. All fields from the products schema must be present. and dont return null data in any fileds simplt return the default value. like false, "
          ", 0 etc",
      parameters: [
        OpenAIFunctionProperty.boolean(
          name: "show",
          description:
              "Indicates whether the product is visible in the storefront if not given by default set to true.",
        ),
        OpenAIFunctionProperty.integer(
          name: "priorityNo",
          description:
              "Priority number for sorting product listings (higher values indicate higher priority) if not given by default set to 0.",
        ),
        OpenAIFunctionProperty.boolean(
          name: "topSelling",
          description:
              "Indicates if the product is a top-selling item. if not found set to false",
        ),
        OpenAIFunctionProperty.string(
          name: "name",
          description: "The name of the product.",
        ),
        OpenAIFunctionProperty.string(
          name: "subCatDocId",
          description: "Document ID of the product's subcategory.",
        ),
        OpenAIFunctionProperty.string(
          name: "description",
          description:
              "A brief description of the product. remove html format just return the String of descripton. if data not not found auto generate with refresnce to these data.if not found auto generate with refresnce to these data. all.",
        ),
        OpenAIFunctionProperty.array(
          name: "detailsList",
          description:
              "List of detail categories (e.g., 'colour', 'material', 'size'). these data is founD in the varaint. if not found auto generate with refresnce to the variant and make sure it shoulld be be like color,size,fabric etc not like set,partyear type. AND MAINLY IT SHOULD LINK TO VARAINT IT GENERATE COLOUR-red then a variant red color mus be there.  data should be in lower case and for color or colour use color",
          items: OpenAIFunctionProperty.string(name: "detail"),
        ),
        OpenAIFunctionProperty.array(
          name: "subcatIds",
          description:
              "List of categories document IDs associated with the product, and add the subCatDocId fromabove data",
          items: OpenAIFunctionProperty.string(name: "id"),
        ),
        OpenAIFunctionProperty.number(
          name: "minPrice",
          description: "Minimum price of the product across all variants.",
        ),
        OpenAIFunctionProperty.array(
          name: "combinationNames",
          description:
              "List of alternate names or search keywords derived from the product name. if not found auto generate with refresnce to these data",
          items: OpenAIFunctionProperty.string(name: "name"),
        ),
        OpenAIFunctionProperty.string(
          name: "sku",
          description:
              "Stock Keeping Unit identifier for inventory tracking. if data not not found auto generate with refresnce to these data",
        ),
        OpenAIFunctionProperty.number(
          name: "rating",
          description:
              "Average rating of the product (e.g., 0 to 5).if not given set to 0",
        ),
        OpenAIFunctionProperty.array(
          name: "tags",
          description:
              "Keywords or labels associated with the product for search and filtering from the product data, if not found auto generate with refresnce to these data",
          items: OpenAIFunctionProperty.string(name: "tag"),
        ),
        OpenAIFunctionProperty.number(
          name: "totalSalesAmount",
          description:
              "Total monetary amount from sales of the product. if not found set to 0",
        ),
        OpenAIFunctionProperty.string(
          name: "createdAt",
          description: "Timestamp of product creation.",
        ),
        OpenAIFunctionProperty.string(
          name: "vendorDocId",
          description: "Document ID of the vendor supplying the product.",
        ),
        OpenAIFunctionProperty.string(
          name: "defaultImage",
          description:
              "URL of the default image for the product. if not found set to any image url given",
        ),
        OpenAIFunctionProperty.string(
          name: "lowerName",
          description:
              "Lowercase version of the product name for case-insensitive searches.",
        ),
        OpenAIFunctionProperty.number(
          name: "quantitySold",
          description: "Total number of units sold.if not found set to 0",
        ),
        OpenAIFunctionProperty.number(
          name: "maxPrice",
          description:
              "Maximum price of the product across all variants. if not found set to any price given",
        ),
        OpenAIFunctionProperty.array(
          name: "variants",
          description:
              "List of product variants with specific attributes and pricing.",
          items: OpenAIFunctionProperty.object(
            name: "variant",
            description:
                "Single variant object if there are none variant then use  prodcut add as a default varaint ",
            properties: [
              OpenAIFunctionProperty.array(
                name: "images",
                description:
                    "List of url of related images of the varaint ,varaint can have multiple images so look carefully and if one variant in the product is found then use all images of the product for the varaint",
                items: OpenAIFunctionProperty.string(name: "detail"),
              ),
              OpenAIFunctionProperty.string(
                name: "id",
                description:
                    "Unique identifier for the variant. if no varaint in the data use id of  the product. if no id in the data then generate a unique id",
              ),
              OpenAIFunctionProperty.boolean(
                name: "defaultt",
                description:
                    "Indicates if this variant is the default option. if not found anset to true",
              ),
              OpenAIFunctionProperty.boolean(
                name: "show",
                description:
                    "Indicates if this variant is available or no, if not related data then set to true",
              ),
              OpenAIFunctionProperty.object(
                name: "detailTypes",
                description:
                    "Key-value pairs of variant details like color, size etc.{'Size':'Xl','Color':'Red'} You should only include main variant details like Size, Color, and Fabric if they are clearly available in the data. For example, if the Color is 'red, green,' list it like that. If Size is missing or has unclear values like 'Make to order,' then don’t include. data should be in lower case and for color or colour use color When there are multiple options in one attribute, like Size being 'XL, L, S,red,green,cotton,silk' treat each size as a separate variant. Also, keep the values short and simple, no longer than 3 words. Avoid adding unrelated details like product type unless none of the main variant details are present. This way, the variant information stays clean and accurate.if not found then generate if ts matches decription otherwise dont add. value should not be large than 3 words",
                properties: {},
              ),
              OpenAIFunctionProperty.string(
                name: "description",
                description:
                    "Description specific to this variant in normal string format not in html. if not found use product description but not in html format.",
              ),
              OpenAIFunctionProperty.string(
                name: "priceType",
                description:
                    "Pricing model for the variant ('Fixed' or 'Price Range').if not found set to Fixed",
              ),
              OpenAIFunctionProperty.number(
                name: "fixedprice",
                description:
                    "Fixed price when priceType is 'Fixed'; set to 0 if using priceRange.",
              ),
              OpenAIFunctionProperty.array(
                name: "priceRange",
                description:
                    "List of price tiers based on quantity when priceType is 'Price Range'.e.g [{'endQty':'15','price':10000,'startQty':'10'}]",
                items: OpenAIFunctionProperty.object(
                  name: "priceTier",
                  description: "A single price tier,",
                  properties: [
                    OpenAIFunctionProperty.number(
                      name: "price",
                      description: "Price for this quantity range.",
                    ),
                    OpenAIFunctionProperty.integer(
                      name: "startQty",
                      description: "Starting quantity for this price tier.",
                    ),
                    OpenAIFunctionProperty.integer(
                      name: "endQty",
                      description: "Ending quantity for this price tier.",
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    ),
  );

  final systemMessage = OpenAIChatCompletionChoiceMessageModel(
    role: OpenAIChatMessageRole.system,
    content: [
      OpenAIChatCompletionChoiceMessageContentItemModel.text(
        "You are a conversion assistant.",
      ),
    ],
  );

  final instructionMessage = OpenAIChatCompletionChoiceMessageModel(
    role: OpenAIChatMessageRole.user,
    content: [
      OpenAIChatCompletionChoiceMessageContentItemModel.text(
        "Convert the provided vendor JSON into the products JSON format preserving all vendor data.",
      ),
    ],
  );

  final originalJsonMessage = OpenAIChatCompletionChoiceMessageModel(
    role: OpenAIChatMessageRole.user,
    content: [
      OpenAIChatCompletionChoiceMessageContentItemModel.text(
        jsonEncode(
            vendorData), // Make sure vendorData is your `Map` or `List<Map>` variable
      ),
    ],
  );

  final messages = [
    systemMessage,
    instructionMessage,
    originalJsonMessage,
  ];
  int maxRetries = 8;
  int attempt = 0;

  while (attempt < maxRetries) {
    try {
      attempt++;
      final chat = await OpenAI.instance.chat.create(
        model: "gpt-4o",
        messages: messages,
        tools: [convertVendorToProductsTool],
      ).timeout(Duration(minutes: 5));
      final toolCalls = chat.choices.first.message.toolCalls;
      if (toolCalls == null || toolCalls.isEmpty) {
        throw Exception("OpenAI did not return any function calls.");
      }
      final argsRaw = toolCalls.first.function.arguments;
      final Map<String, dynamic> argsParsed = jsonDecode(argsRaw);

      return [argsParsed]; //p
    } on TimeoutException catch (e) {
      print("⏱ Timeout on attempt $attempt: $e");
    } catch (e) {
      print("❌ Error on attempt $attempt: $e");
    }

    print("Retrying... (attempt $attempt)");
    await Future.delayed(Duration(seconds: 2)); // Optional delay before retry
  }

  try {
    final chat = await OpenAI.instance.chat.create(
      model: "gpt-4o",
      messages: messages,
      tools: [convertVendorToProductsTool],
    ).timeout(Duration(minutes: 5));
    final toolCalls = chat.choices.first.message.toolCalls;
    if (toolCalls == null || toolCalls.isEmpty) {
      throw Exception("OpenAI did not return any function calls.");
    }

    final argsRaw = toolCalls.first.function.arguments;
    final Map<String, dynamic> argsParsed = jsonDecode(argsRaw);

    return [argsParsed]; //
    // process chat
  } on TimeoutException catch (e) {
    print("TimeoutException caught: $e");
    rethrow;
  } catch (e) {
    print("Other exception: $e");
    return [];
  }
  // final chat = await OpenAI.instance.chat.create(
  //   model: "gpt-4o",

  //   // model: "o3-mini",
  //   messages: messages,
  //   tools: [convertVendorToProductsTool],
  // ).timeout(Duration(minutes: 5));

// Or adjust if multiple products expected
}

Future<List<Map<String, dynamic>>> convertVendorCategoryToCategory(
  List<Map<String, dynamic>> vendorData,
  String apiKey,
) async {
  OpenAI.apiKey = true
      ? apiKey
      : "********************************************************************************************************************************************************************";
  // : "********************************************************************************************************************************************************************";

  final convertVendorToProductsTool = OpenAIToolModel(
      type: "function",
      function: OpenAIFunctionModel.withParameters(
          name: "convert_vendor_to_category",
          description:
              "Converts vendor category data into the categories JSON format for an e-commerce wedding shopping system. All fields from the category schema must be present.",
          parameters: [
            OpenAIFunctionProperty.string(
                name: "name",
                description:
                    "Name of the category (e.g., 'Lehengas, Blazers,')."),
            OpenAIFunctionProperty.string(
                name: "image",
                description:
                    "URL or reference to the image representing the category if not found save empty string."),
            OpenAIFunctionProperty.boolean(
                name: "isActive",
                description:
                    "Whether this category is currently active and visible."),
            OpenAIFunctionProperty.array(
                name: "combinationNames",
                description:
                    "Alternate names or searchable keywords for the category.",
                items: OpenAIFunctionProperty.string(name: "name")),
            // OpenAIFunctionProperty.string(
            //     name: "offeringId",
            //     description:
            //         "ID of the offering under which this category falls."),
            OpenAIFunctionProperty.number(
                name: "minPrice",
                description: "Minimum price of items in this category."),
            OpenAIFunctionProperty.number(
                name: "maxPrice",
                description: "Maximum price of items in this category."),
            OpenAIFunctionProperty.array(
                name: "tags",
                description:
                    "Tags associated with this category for filtering/search and values from detailtype, could be anthing related to category tyle List of Strings",
                items: OpenAIFunctionProperty.string(name: "tag")),
            // OpenAIFunctionProperty.array(
            //     name: "detailTypes",
            //     description:
            //         "List of attribute maps (e.g., color, material, size) relevant to this category.data example [{'detailType':'Size','priorityNo':'1'},{'detailType':'Color','priorityNo':'1'}]",
            //     items: OpenAIFunctionProperty.object(
            //         name: "detailType",
            //         description:
            //             "A detail map like .data example [{'detailType':'Size','priorityNo':'1'},{'detailType':'Color','priorityNo':'1'}]}",
            //         properties: {})),
            OpenAIFunctionProperty.number(
                name: "marginPercentage",
                description:
                    "The profit margin percentage applied to items in this category."),
            // OpenAIFunctionProperty.object(
            //     name: "allData",
            //     description: "stored as emptt list",
            //     properties: {}),

            // OpenAIFunctionProperty.array(
            //   name: "colors",
            //   description: "List of colors applicable to this category.",
            //   items: OpenAIFunctionProperty.string(name: "color")
            // ),
            // OpenAIFunctionProperty.array(
            //   name: "sizes",
            //   description: "List of sizes applicable to this category.",
            //   items: OpenAIFunctionProperty.string(name: "size")
            // ),
            // OpenAIFunctionProperty.array(
            //   name: "materials",
            //   description: "List of materials used in this subcategory.",
            //   items: OpenAIFunctionProperty.string(name: "material")
            // ),
          ]));

  final systemMessage = OpenAIChatCompletionChoiceMessageModel(
    role: OpenAIChatMessageRole.system,
    content: [
      OpenAIChatCompletionChoiceMessageContentItemModel.text(
        "You are a conversion assistant.",
      ),
    ],
  );

  final instructionMessage = OpenAIChatCompletionChoiceMessageModel(
    role: OpenAIChatMessageRole.user,
    content: [
      OpenAIChatCompletionChoiceMessageContentItemModel.text(
          "Convert the provided vendor JSON into the category JSON format preserving all vendor data." // And for offeringiD try to match with closest category provided categoriesJsonMessage in  and save the offeringId of the matched category. If category name provided if matches exactly the same then skip the whole category conversion as it already exists",
          ),
      // OpenAIChatCompletionChoiceMessageContentItemModel.text(
      //   jsonEncode(Get.find<HomeCtrl>()
      //       .subcategories
      //       .map((e) => {'offeringId': e.offeringId, 'name': e.name})
      //       .toList()), // Make sure vendorData is your `Map` or `List<Map>` variable
      // ),
    ],
  );

  final originalJsonDataMessage = OpenAIChatCompletionChoiceMessageModel(
    role: OpenAIChatMessageRole.user,
    content: [
      OpenAIChatCompletionChoiceMessageContentItemModel.text(
        jsonEncode(
            vendorData), // Make sure vendorData is your `Map` or `List<Map>` variable
      ),
    ],
  );

  // final categoriesJsonMessage = OpenAIChatCompletionChoiceMessageModel(
  //   role: OpenAIChatMessageRole.user,
  //   content: [
  //     OpenAIChatCompletionChoiceMessageContentItemModel.text(
  //       jsonEncode(Get.find<HomeCtrl>()
  //           .subcategories
  //           .map((e) => {'offeringId': e.offeringId, 'name': e.name})
  //           .toList()), // Make sure vendorData is your `Map` or `List<Map>` variable
  //     ),
  //   ],
  // );

  final messages = [
    systemMessage,
    instructionMessage,
    originalJsonDataMessage,
    // categoriesJsonMessage
  ];

  int maxRetries = 8;
  int attempt = 0;

  while (attempt < maxRetries) {
    try {
      attempt++;
      final chat = await OpenAI.instance.chat.create(
        // model: "o3-mini",
        model: "gpt-4o",
        messages: messages,
        tools: [convertVendorToProductsTool],
      ).timeout(Duration(minutes: 5));

      final toolCalls = chat.choices.first.message.toolCalls;
      if (toolCalls == null || toolCalls.isEmpty) {
        throw Exception("OpenAI did not return any function calls.");
      }

      final argsRaw = toolCalls.first.function.arguments;
      final Map<String, dynamic> argsParsed = jsonDecode(argsRaw);

      return [argsParsed]; // Or adjust if multiple products expected
    } on TimeoutException catch (e) {
      print("⏱ Timeout on attempt $attempt: $e");
    } catch (e) {
      print("❌ Error on attempt $attempt: $e");
    }

    print("Retrying... (attempt $attempt)");
    await Future.delayed(Duration(seconds: 2)); // Optional delay before retry
  }
  try {
    final chat = await OpenAI.instance.chat.create(
      // model: "o3-mini",
      model: "gpt-4o",
      messages: messages,
      tools: [convertVendorToProductsTool],
    ).timeout(Duration(minutes: 5));

    final toolCalls = chat.choices.first.message.toolCalls;
    if (toolCalls == null || toolCalls.isEmpty) {
      throw Exception("OpenAI did not return any function calls.");
    }

    final argsRaw = toolCalls.first.function.arguments;
    final Map<String, dynamic> argsParsed = jsonDecode(argsRaw);

    return [argsParsed]; // Or adjust if multiple products expected
  } on TimeoutException catch (e) {
    print("TimeoutException caught: $e");
    rethrow;
  } catch (e) {
    print("Other exception: $e");
    return [];
  }
}

Future<List<Map<String, dynamic>>> matchedDatawithOffering(
    List<Map<String, dynamic>> categoriesData,
    List<Map<String, dynamic>> offeringData,
    Map<String, dynamic> data,
    // String? prodName,
    // String? prodDesc,
    // String category,
    String apiKey) async {
  OpenAI.apiKey = true
      ? apiKey
      : "********************************************************************************************************************************************************************";

  final convertVendorToProductsTool = OpenAIToolModel(
    type: "function",
    function: OpenAIFunctionModel.withParameters(
      name: "match-category",
      description:
          "try to matched with the closest category data if not matched, matched it with the closest offering and save its offeringId to Given new Category.",
      parameters: [
        OpenAIFunctionProperty.string(
            name: "offeringId",
            description:
                "offeringID of the closest category matched from the given Data"),
      ],
    ),
  );

  final systemMessage = OpenAIChatCompletionChoiceMessageModel(
    role: OpenAIChatMessageRole.system,
    content: [
      OpenAIChatCompletionChoiceMessageContentItemModel.text(
        "You are a conversion assistant.",
      ),
    ],
  );

  final instructionMessage = OpenAIChatCompletionChoiceMessageModel(
    role: OpenAIChatMessageRole.user,
    content: [
      OpenAIChatCompletionChoiceMessageContentItemModel.text(
        "provided data like categoryName, productName and productDescription data, try to match all the three parameters with the  category data and save its offeringId to Given new Category.if not found with categories then match with the closest offering and save its offering id",
      ),
      OpenAIChatCompletionChoiceMessageContentItemModel.text(jsonEncode(data)),
    ],
  );
  // : OpenAIChatCompletionChoiceMessageModel(
  //     role: OpenAIChatMessageRole.user,
  //     content: [
  //       OpenAIChatCompletionChoiceMessageContentItemModel.text(
  //         " provided cat name match with the closest category data and save its offeringId to Given new Category.",
  //       ),
  //       OpenAIChatCompletionChoiceMessageContentItemModel.text(category),
  //     ],
  //   );

  final originalJsonMessage = OpenAIChatCompletionChoiceMessageModel(
    role: OpenAIChatMessageRole.user,
    content: [
      OpenAIChatCompletionChoiceMessageContentItemModel.text(
        "categories - ${jsonEncode(categoriesData)}", // Make sure vendorData is your `Map` or `List<Map>` variable
      ),
      OpenAIChatCompletionChoiceMessageContentItemModel.text(
        "offerings - ${jsonEncode(offeringData)}", // Make sure vendorData is your `Map` or `List<Map>` variable
      ),
    ],
  );

  final messages = [
    systemMessage,
    instructionMessage,
    originalJsonMessage,
  ];

  final chat = await OpenAI.instance.chat.create(
    // model: "o3-mini",
    model: "gpt-4o",

    messages: messages,
    tools: [convertVendorToProductsTool],
  ).timeout(Duration(minutes: 5));

  final toolCalls = chat.choices.first.message.toolCalls;
  if (toolCalls == null || toolCalls.isEmpty) {
    throw Exception("OpenAI did not return any function calls.");
  }

  final argsRaw = toolCalls.first.function.arguments;
  final Map<String, dynamic> argsParsed = jsonDecode(argsRaw);

  return [argsParsed]; // Or adjust if multiple products expected
}
