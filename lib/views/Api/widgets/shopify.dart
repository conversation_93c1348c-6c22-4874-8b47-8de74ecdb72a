// ignore_for_file: avoid_print, dead_code

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shopify_flutter/shopify_flutter.dart';
import 'package:wedding_super_admin/controller/home_ctrl.dart';
import 'package:wedding_super_admin/models/product.dart';
import 'package:wedding_super_admin/models/subcategories.dart';
import 'package:wedding_super_admin/models/variants.dart';
import 'package:wedding_super_admin/models/vendor.dart';
import 'package:wedding_super_admin/shared/const.dart';
import 'package:wedding_super_admin/shared/firebase.dart';
import 'package:wedding_super_admin/shared/methods.dart';
import 'package:wedding_super_admin/shared/theme.dart';
import 'package:wedding_super_admin/views/Api/data_import.dart';

class ShopifyProdCatData {
  final Collection subcatData;
  final List<Product> prodData;
  ShopifyProdCatData({required this.subcatData, required this.prodData});
}

class Shopify extends StatefulWidget {
  const Shopify({super.key, required this.vendor});
  final VendorModel vendor;

  @override
  State<Shopify> createState() => _ShopifyState();
}

class _ShopifyState extends State<Shopify> {
  TextEditingController storeFrontTokenctrl = TextEditingController();
  bool allCatFetched = false;
  List<ShopifyProdCatData> apiFetchedProductData = [];
  List<ShopifyProdCatData> apiFetchedProductDatatemp = [];
  // TextEditingController prodApiKeyctrl = TextEditingController();
  SubCategory? selectedSubCat;
  // TextEditingController perPageCountctrlCat = TextEditingController();
  TextEditingController perPageCountctrlProd =
      TextEditingController(text: perPageProduct);
  TextEditingController storeNamectrl = TextEditingController();
  bool fetchingDataCat = false;
  // TextEditingController apiKeyctrl = TextEditingController();
  bool dataSet = false;
  bool loader = false;
  int currentCatIndex = 0;
  String? currentCursor;
  bool fetchingDataProd = false;
  bool convertingCat = false;
  bool convertingProd = false;
  bool allCatProdFetched = false;
  num prodPageCount = 1;
  bool allProductsFetched = false;
  String? currentStatus;

  bool createNew = true;
  bool catConverted = false;
  bool prodConverted = false;
  num categoryCompletionCount = 0;
  num productCompletionCount = 0;
  bool inProcess = false;
  bool breakFunct = false;
  int i = 0;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    if (widget.vendor.apiRequiredData.isNotEmpty) {
      if (widget.vendor.apiRequiredData.containsKey('storeToken')) {
        storeFrontTokenctrl.text = widget.vendor.apiRequiredData['storeToken'];
      }
      if (widget.vendor.apiRequiredData.containsKey('storeName')) {
        storeNamectrl.text = widget.vendor.apiRequiredData['storeName'];
      }

      if (widget.vendor.apiRequiredData.containsKey('selectedCat')) {
        selectedSubCat = Get.find<HomeCtrl>().subcategories.firstWhereOrNull(
            (element) =>
                element.docId == widget.vendor.apiRequiredData['selectedCat']);
      }
      if (widget.vendor.apiRequiredData.containsKey('newSubCat')) {
        createNew = widget.vendor.apiRequiredData['newSubCat'];
      }
    }
    if (mounted) {
      setState(() {});
    }
  }

  fetchCategories() async {
    currentStatus = "Fetching Categories";

    ShopifyStore shopifyStore = ShopifyStore.instance;

    final resp = await shopifyStore.getAllCollections();
    print(resp.length);
    for (Collection element in resp) {
      if (breakFunct) {
        break;
      }
      // if (apiFetchedProductData.length < 1) {
      apiFetchedProductDatatemp
          .add(ShopifyProdCatData(subcatData: element, prodData: []));
      // }
    }

    bool check = false;
    for (var element in apiFetchedProductDatatemp) {
      if (element.subcatData.id == "gid://shopify/Collection/289433682029") {
        check = true;
      }
      if (check) {
        apiFetchedProductData.add(element);
      }
    }

    print(apiFetchedProductData.length);

    setState(() {});
  }

  convertCategories() async {
    currentStatus = "Converting Categories";

    await convertWoocommerceCat().timeout(Duration(minutes: 5));
  }

  fetchProducts() async {
    currentStatus = "Fetching Products";

    if (perPageCountctrlProd.text.isNotEmpty) {
      // for (var i = 0; i < apiFetchedProductData.length; i++) {
      while (!allProductsFetched) {
        ShopifyStore shopifyStore = ShopifyStore.instance;

        final products =
            await shopifyStore.getXProductsAfterCursorWithinCollection(
                apiFetchedProductData[currentCatIndex].subcatData.id,
                int.tryParse(perPageCountctrlProd.text) ?? 0,
                startCursor: currentCursor);
        if (products?.isNotEmpty ?? false) {
          currentCursor = products?.last.cursor;

          apiFetchedProductData
              .firstWhereOrNull((element) =>
                  element.subcatData.id ==
                  apiFetchedProductData[currentCatIndex].subcatData.id)
              ?.prodData
              .addAll(products ?? []);
          // print(products?.length);
          // for (Product element in products ?? []) {
          //   if (breakFunct) {
          //     break;
          //   }

          // }
          // print(apiData);
          // for (var element in apiFetchedProductData) {
          //   print(element.prodData.length);
          // }
          // print(apiFetchedProductData);
          // pageNum = ((int.tryParse(pageNum) ?? 0) + 1).toString();
        } else {
          // break;
        }

        // print("${data.length}---${(int.tryParse(perPageCount) ?? 0)}");
        if (products?.length !=
            (int.tryParse(perPageCountctrlProd.text) ?? 0)) {
          allCatProdFetched = true;
          if (currentCatIndex + 1 == apiFetchedProductData.length) {
            allProductsFetched = true;
          } else {
            print(
                "-------------------------${apiFetchedProductData[currentCatIndex].subcatData.id}");
            currentCatIndex++;
            currentCursor = null;
          }
        } else {
          allCatProdFetched = false;
        }
        prodPageCount++;

        setState(() {});
      }
    } else {
      showAppSnackBar(context, "Enter both keys");
    }
  }

  convertProducts() async {
    currentStatus = "Converting Products";

    await convertWoocommerceProducts().timeout(Duration(hours: 1));
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        SizedBox(
          height: 30,
        ),
        Divider(),
        SizedBox(
          height: 30,
        ),

        Row(
          children: [
            Text("Shopify API Configure",
                style: GoogleFonts.zcoolXiaoWei(
                  fontSize: 24,
                  color: themeColor,
                )),
            SizedBox(
              width: 10,
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(5))),
              onPressed: loader
                  ? () {}
                  : () async {
                      if (storeNamectrl.text.isNotEmpty &&
                          storeFrontTokenctrl.text.isNotEmpty) {
                        Map<String, dynamic> newData = ctrl.vendors
                                .firstWhereOrNull((element) =>
                                    element.docId == widget.vendor.docId)
                                ?.apiRequiredData ??
                            {};
                        newData['storeToken'] = storeFrontTokenctrl.text;
                        newData['storeName'] = storeNamectrl.text;

                        await FBFireStore.vendors
                            .doc(widget.vendor.docId)
                            .update({'apiRequiredData': newData});
                        loader = true;
                        setState(() {});
                        ShopifyConfig.setConfig(
                          storefrontAccessToken: storeFrontTokenctrl.text,
                          storeUrl: '${storeNamectrl.text}.myshopify.com',
                          storefrontApiVersion: '2025-04',
                          language: 'en',
                        );
                        await Future.delayed(Duration(seconds: 1));
                        loader = false;
                        dataSet = true;
                        setState(() {});

                        setState(() {});
                      } else {
                        showAppSnackBar(context, "Enter all the data");
                      }
                    },
              child: loader ? CircularProgressIndicator() : Text('Set Data'),
            ),
            Spacer(),
            Text(currentStatus == null ? "" : "$currentStatus..."),
            SizedBox(
              width: 10,
            ),
            if (dataSet)
              ElevatedButton(
                  style: ElevatedButton.styleFrom(
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(5))),
                  onPressed: inProcess
                      ? () {}
                      : () async {
                          if (storeFrontTokenctrl.text.isEmpty ||
                              storeNamectrl.text.isEmpty ||
                              perPageCountctrlProd.text.isEmpty) {
                            showAppSnackBar(
                                context, "Please enter all the details   !!");
                            return;
                          }
                          inProcess = true;
                          setState(() {});
                          try {
                            await fetchCategories();
                            // await convertCategories();
                            await fetchProducts();
                            await convertProducts();
                          } catch (e) {
                            // TODO
                            debugPrint(e.toString());
                            currentStatus = "Error ${e.toString()}!!";
                          }
                          inProcess = false;
                          setState(() {});
                        },
                  child: inProcess
                      ? CircularProgressIndicator()
                      : Text("Initiate Process")),
            if (inProcess)
              IconButton(
                  onPressed: () {
                    breakFunct = true;
                  },
                  icon: Icon(
                    Icons.block,
                    color: Colors.red,
                  ))
          ],
        ),
        SizedBox(
          height: 20,
        ),
        StaggeredGrid.extent(
          maxCrossAxisExtent: 600,
          crossAxisSpacing: 20,
          mainAxisSpacing: 20,
          children: [
            TextFormField(
              onChanged: (value) async {
                await FBFireStore.vendors
                    .doc(widget.vendor.docId)
                    .update({'apiRequiredData.storeName': value});
              },
              controller: storeNamectrl,
              decoration: inpDecor().copyWith(labelText: 'Enter Store name'),
            ),
            TextFormField(
              onChanged: (value) async {
                await FBFireStore.vendors
                    .doc(widget.vendor.docId)
                    .update({'apiRequiredData.storeToken': value});
              },
              controller: storeFrontTokenctrl,
              decoration:
                  inpDecor().copyWith(labelText: 'Enter Store Front Token'),
            ),
            // TextFormField(
            //   controller: apiKeyctrl,
            //   decoration: inpDecor().copyWith(labelText: 'Enter api key'),
            // ),
          ],
        ),

        SizedBox(
          height: 30,
        ),
        Divider(),
        SizedBox(
          height: 30,
        ),
        Text("Default Category",
            style: GoogleFonts.zcoolXiaoWei(
              fontSize: 24,
              color: themeColor,
            )),
        SizedBox(
          height: 20,
        ),
        Container(
            constraints: BoxConstraints(maxWidth: 500),
            child: Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField(
                    value: selectedSubCat,
                    hint: Text("Select Default Sub-Category"),
                    decoration: InputDecoration(border: OutlineInputBorder()),
                    items: List.generate(
                      ctrl.subcategories.length,
                      (index) {
                        return DropdownMenuItem(
                            value: ctrl.subcategories[index],
                            child: Text(ctrl.subcategories[index].name));
                      },
                    ),
                    onChanged: (value) async {
                      await FBFireStore.vendors
                          .doc(widget.vendor.docId)
                          .update({
                        'apiRequiredData.selectedCat': selectedSubCat?.docId
                      });
                      selectedSubCat = value;
                      setState(() {});
                    },
                  ),
                ),
                SizedBox(
                  width: 10,
                ),
                IconButton(
                    onPressed: () async {
                      await FBFireStore.vendors
                          .doc(widget.vendor.docId)
                          .update({
                        'apiRequiredData.selectedCat': null,
                        'apiRequiredData.newSubCat': true
                      });
                      setState(() {
                        selectedSubCat = null;
                        createNew = true;
                      });
                    },
                    icon: Icon(CupertinoIcons.xmark))
              ],
            )
            //  TextFormField(
            //   controller: perPageCountctrlProd,
            //   decoration: InputDecoration(
            //       labelText: "Select Default Sub-Category",
            //       border:
            //           OutlineInputBorder(borderRadius: BorderRadius.circular(5))),
            // ),
            ),
        if (selectedSubCat != null) ...[
          SizedBox(
            height: 20,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Checkbox(
                value: createNew,
                onChanged: (value) async {
                  await FBFireStore.vendors
                      .doc(widget.vendor.docId)
                      .update({'apiRequiredData.newSubCat': value});
                  createNew = value ?? false;
                  setState(() {});
                },
              ),
              SizedBox(
                width: 5,
              ),
              Text("Create New Sub Cat ")
            ],
          ),
        ],
        if (dataSet) ...[
          SizedBox(
            height: 30,
          ),
          Divider(),
          SizedBox(
            height: 30,
          ),
          Row(
            children: [
              Text("Category",
                  style: GoogleFonts.zcoolXiaoWei(
                    fontSize: 24,
                    color: themeColor,
                  )),
              SizedBox(
                width: 20,
              ),
              TextButton(
                style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(5))),
                onPressed: fetchingDataCat || allCatFetched
                    ? () {}
                    : true
                        ? () {}
                        : fetchCategories,
                child: fetchingDataCat
                    ? Padding(
                        padding: const EdgeInsets.all(5.0),
                        child: CircularProgressIndicator(),
                      )
                    : Text(allCatFetched
                        ? "Data Fetched"
                        // : catPageCount != 1
                        //     ? "Fetch next ${perPageCountctrlCat.text} Categories"
                        : ''),
              ),
            ],
          ),
          if (apiFetchedProductData.map((e) => e.subcatData).isNotEmpty) ...[
            SizedBox(
              height: 20,
            ),
            Text(
                "Result: ${apiFetchedProductData.map((e) => e.subcatData).length} Categories Found!!",
                style: GoogleFonts.zcoolXiaoWei(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: themeColor,
                )),
          ],
        ],

        if (apiFetchedProductData.map((e) => e.subcatData).isNotEmpty) ...[
          if (selectedSubCat == null || createNew) ...[
            if (apiFetchedProductData.map((e) => e.subcatData).isNotEmpty) ...[
              SizedBox(
                height: 30,
              ),
              Divider(),
              SizedBox(
                height: 30,
              ),
              Row(
                children: [
                  Text("Convert Category",
                      style: GoogleFonts.zcoolXiaoWei(
                        fontSize: 24,
                        color: themeColor,
                      )),
                  SizedBox(
                    width: 20,
                  ),
                  TextButton(
                    style: ElevatedButton.styleFrom(
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(5))),
                    onPressed: convertingCat ||
                            (categoryCompletionCount ==
                                (apiFetchedProductData
                                    .map((e) => e.subcatData)
                                    .length))
                        ? () {}
                        : true
                            ? () {}
                            : convertCategories,
                    child: (categoryCompletionCount ==
                            (apiFetchedProductData
                                .map((e) => e.subcatData)
                                .length))
                        ? Text(
                            "${apiFetchedProductData.map((e) => e.subcatData).length} - Converted")
                        : convertingCat
                            ? Padding(
                                padding: const EdgeInsets.all(5.0),
                                child: Row(
                                  children: [
                                    Text(
                                        "$categoryCompletionCount/${apiFetchedProductData.map((e) => e.subcatData).length}"),
                                    SizedBox(
                                      width: 10,
                                    ),
                                    CircularProgressIndicator(),
                                  ],
                                ),
                              )
                            : Text(catConverted ? "Converted" : ''),
                  ),
                ],
              ),
            ],

            // // Tex
          ],
          SizedBox(
            height: 30,
          ),
          Divider(),
          SizedBox(
            height: 30,
          ),
          Row(
            children: [
              Text("Products",
                  style: GoogleFonts.zcoolXiaoWei(
                    fontSize: 24,
                    color: themeColor,
                  )),
              SizedBox(
                width: 20,
              ),
              TextButton(
                style: ElevatedButton.styleFrom(
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(5))),
                onPressed: fetchingDataProd || allProductsFetched
                    ? () {}
                    : true
                        ? () {}
                        : fetchProducts,
                child: fetchingDataProd
                    ? Padding(
                        padding: const EdgeInsets.all(5.0),
                        child: CircularProgressIndicator(),
                      )
                    : Text(allProductsFetched
                        ? "Data Fetched"
                        : allCatProdFetched
                            ? "Fetch next category products"
                            : prodPageCount != 1
                                ? "Fetch next ${perPageCountctrlProd.text} Products"
                                : ''),
              ),
            ],
          ),
          SizedBox(
            height: 20,
          ),
          ConstrainedBox(
            constraints: BoxConstraints(maxWidth: 500),
            child: TextFormField(
              controller: perPageCountctrlProd,
              decoration: InputDecoration(
                  labelText: "Per Page Limit",
                  border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(5))),
            ),
          ),

          // Tex

          if (apiFetchedProductData
              .any((subList) => subList.prodData.isNotEmpty)) ...[
            SizedBox(
              height: 20,
            ),
            Text(
                "Results: ${apiFetchedProductData.map((e) => e.prodData).map((e) => e.length).reduce((value, element) => value + element)} Products Found!!",
                style: GoogleFonts.zcoolXiaoWei(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: themeColor,
                )),
            SizedBox(
              height: 30,
            ),
            Divider(),
            SizedBox(
              height: 30,
            ),
            Row(
              children: [
                Text("Products",
                    style: GoogleFonts.zcoolXiaoWei(
                      fontSize: 24,
                      color: themeColor,
                    )),
                SizedBox(
                  width: 20,
                ),
                TextButton(
                  style: ElevatedButton.styleFrom(
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(5))),
                  onPressed: convertingProd ||
                          (productCompletionCount ==
                              apiFetchedProductData
                                  .map((e) => e.prodData)
                                  .map((e) => e.length)
                                  .reduce((value, element) => value + element))
                      ? () {}
                      : true
                          ? () {}
                          : convertProducts,
                  child: (productCompletionCount ==
                          apiFetchedProductData
                              .map((e) => e.prodData)
                              .map((e) => e.length)
                              .reduce((value, element) => value + element))
                      ? Text(
                          "${apiFetchedProductData.map((e) => e.prodData).map((e) => e.length).reduce((value, element) => value + element)} - Converted")
                      : convertingProd
                          ? Padding(
                              padding: const EdgeInsets.all(5.0),
                              child: Row(
                                children: [
                                  Text(
                                      "$productCompletionCount/${apiFetchedProductData.map((e) => e.prodData).map((e) => e.length).reduce((value, element) => value + element)}"),
                                  SizedBox(
                                    width: 10,
                                  ),
                                  CircularProgressIndicator(),
                                ],
                              ),
                            )
                          : Text(prodConverted ? "Converted" : ''),
                ),
              ],
            ),
            SizedBox(
              height: 20,
            ),
            // Tex
          ]
        ],
        //   ),
        // ],
      ]);
    });
  }

  Future convertWoocommerceCat() async {
    convertingCat = true;
    setState(() {});

    try {
      for (var item in apiFetchedProductData) {
        if (breakFunct) {
          break;
        }
        final data = await convertVendorCategoryToCategory(
                [((item.subcatData.toJson()))],
                // apiFetchedProductData
                //     .map((e) => e.subcatData as Map<String, dynamic>)
                //     .toList(),
                gptApipiKey)
            .timeout(Duration(minutes: 5));

        // print("------------$data");
        // final data = await convertVendorToProducts(vendorTemp);
        // print(data);
        print("------------$data");
        for (var category in data) {
          final categoryData = {
            'name': category['name'],
            'image': category['image'],
            'isActive': category['isActive'],
            'combinationNames': category['combinationNames'],
            'offeringId': '',
            'minPrice': category['minPrice'],
            'maxPrice': category['maxPrice'],
            'tags': category['tags'],
            'detailTypes': [],
            'marginPercentage': category['marginPercentage'],
            'allData': {}
          };
          print(
              "Generating combinations for category name: ${categoryData['name']}");
          categoryData['offeringId'] =
              generateCombinations(categoryData['name']);
          print("Generated offeringId: ${categoryData['offeringId']}");
          print("Getting ShopifyStore instance");
          ShopifyStore shopifyStore = ShopifyStore.instance;
          print(
              "Fetching first product data for subcat id: ${item.subcatData.id}");
          final firstProductData = await shopifyStore
              .getXProductsAfterCursorWithinCollection(item.subcatData.id, 1);
          if (firstProductData?.isEmpty ?? true) {
            continue;
          }
          print("First product data: ");
          String? desc = firstProductData?.first.descriptionHtml;
          print("Product description: $desc");
          String? prodName = firstProductData?.first.title;
          print("Product name: $prodName");

          final offeringId = await matchedDatawithOffering(
                  Get.find<HomeCtrl>()
                      .subcategories
                      .map((e) => {'name': e.name, 'offeringId': e.offeringId})
                      .toList(),
                  Get.find<HomeCtrl>()
                      .offerings
                      .map((e) => {'name': e.name, 'offeringId': e.docId})
                      .toList(),
                  {
                    'categoryName': (category['name']),
                    'productName': prodName,
                    "ProductDescription": desc
                  },
                  gptApipiKey)
              .timeout(Duration(minutes: 5));
          if (offeringId.isEmpty) {
            categoryData['offeringId'] = '3WpWSn4CMj2BMH4V7nRn';
          } else {
            categoryData['offeringId'] = offeringId.first['offeringId'];
          }
          String? catId = Get.find<HomeCtrl>()
              .vendors
              .firstWhereOrNull((element) {
                print("${element.docId} == ${widget.vendor.docId}");

                return element.docId == widget.vendor.docId;
              })
              ?.apiSubCatIds
              .firstWhereOrNull((e) {
                print("${e.subCatApiId} == ${item.subcatData.id}");
                return e.subCatApiId == item.subcatData.id.split('/').last;
              })
              ?.subCatdocId;

          final catData =
              catId ?? (await FBFireStore.subCategories.add(categoryData)).id;
          print("catId---------$catId");
          if (catId == null) {
            List<SubCatMap>? subcatMapList = Get.find<HomeCtrl>()
                .vendors
                .firstWhereOrNull(
                    (element) => element.docId == widget.vendor.docId)
                ?.apiSubCatIds;
            List<String>? subcatids = Get.find<HomeCtrl>()
                .vendors
                .firstWhereOrNull(
                    (element) => element.docId == widget.vendor.docId)
                ?.subcatids;

            subcatMapList?.add(SubCatMap(
                subCatApiId: apiFetchedProductData
                        .firstWhereOrNull((element) =>
                            element.subcatData.title == category['name'])
                        ?.subcatData
                        .id
                        .split('/')
                        .last
                        .toString() ??
                    "",
                subCatdocId: catData));
            subcatids?.addIf(!(subcatids.contains(catData)), catData);
            print(subcatMapList?.map((e) => e.toJson()));
            await FBFireStore.vendors.doc(widget.vendor.docId).update({
              'apiSubCatIds': subcatMapList?.map((e) => e.toJson()).toList(),
              'subcatids': subcatids
              //  FieldValue.arrayUnion([
              //   '${apiFetchedProductData.firstWhereOrNull((element) => element.subcatData['name'] == category['name'])?.subcatData['id'] ?? ""}'
              // ])
            });
          }
        }
        categoryCompletionCount++;
        setState(() {});
      }
      catConverted = true;
    } catch (e) {
      showAppSnackBar(context, e.toString());
      debugPrint(e.toString());
    }

    convertingCat = false;
    setState(() {});
  }

  Future convertWoocommerceProducts() async {
    convertingProd = true;
    setState(() {});
    try {
      for (var subCatProd in apiFetchedProductData) {
        if (breakFunct) {
          break;
        }
        for (var prod in subCatProd.prodData) {
          if (breakFunct) {
            break;
          }
          final data =
              await convertVendorToProducts([prod.toJson()], gptApipiKey);
          // print('Calling convertProductsOnly with prod: ${prod.toJson()}');
          // final productApicallData =
          //     await convertProductsOnly([prod.toJson()], gptApipiKey)
          //         .timeout(Duration(hours: 1));
          // print('productApicallData: $productApicallData');
          // if (productApicallData.isEmpty) {
          //   print('productApicallData is empty, returning');
          //   return;
          // }
          // print('Calling convertProductVariants with prod: ${prod.toJson()}');
          // final variantsApicallData =
          //     await convertProductVariants([prod.toJson()], gptApipiKey)
          //         .timeout(Duration(hours: 1));
          // print('variantsApicallData: $variantsApicallData');
          // if (variantsApicallData.isEmpty) {
          //   print('variantsApicallData is empty, returning');
          //   return;
          // }
          // print('Combining product and variants data');
          // final data = await combineProductAndVariants(
          //     productApicallData, variantsApicallData);
          // print('Combined data: $data');
          List<String> combNames = [];
          print('Generating combination names');
          for (var element in (data.first['combinationNames'] as List)) {
            print('Generating combinations for: $element');
            combNames.addAll(generateCombinations(element.toString()));
          }
          print('combNames: $combNames');
          Map<String, dynamic> productData = {
            'priorityNo': data.first['priorityNo'],
            'rating': data.first['rating'],
            'name': data.first['name'],
            'lowerName': data.first['lowerName'],
            'combinationNames': combNames,
            'mainCatDocId': null,
            'subCatDocId': null,
            'subcatIds': <String>[],
            'vendorDocId': widget.vendor.docId,
            'show': data.first['show'],
            'sku': data.first['sku'],
            'description': data.first['description'],
            'userType': 'Vendor',
            'defaultImage': data.first['defaultImage'],
            'createdAt': Timestamp.now(),
            'topSelling': data.first['topSelling'],
            'minPrice': data.first['minPrice'],
            'maxPrice': data.first['maxPrice'],
            'quantitySold': data.first['quantitySold'],
            'totalSalesAmount': data.first['totalSalesAmount'],
            'tags': data.first['tags'],
            'detailsList': data.first['detailsList'] ?? <String>[],
            // 'docId': '',
          };
          print("------1");
          // SelectedImage image = SelectedImage(
          //     name: DateTime.now().millisecondsSinceEpoch.toString(),
          //     uInt8List:
          //         await getImageBytesFromUrl(productData['defaultImage']),
          //     extention: null);
          // final imageurl = await uploadProductImages(image);
          // productData['defaultImage'] = imageurl ?? "";
          if (selectedSubCat == null) {
            String? subCatDocId = Get.find<HomeCtrl>()
                .vendors
                .firstWhereOrNull(
                    (element) => element.docId == widget.vendor.docId)
                ?.apiSubCatIds
                .firstWhereOrNull((el) =>
                    el.subCatApiId ==
                    subCatProd.subcatData.id.split('/').last.toString())
                ?.subCatdocId;
            if (subCatDocId != null) {
              final subCat = Get.find<HomeCtrl>()
                  .subcategories
                  .firstWhereOrNull((element) => element.docId == subCatDocId);
              if (subCat != null) {
                (productData['subcatIds'] as List<String>).add(subCat.docId);
                if (productData['subCatDocId'] == null
                    // &&
                    //     subCat.name == subCatProd.subcatData.title
                    ) {
                  // catId = subCat.docId;
                  productData['subCatDocId'] = subCat.docId;
                  productData['mainCatDocId'] = subCat.offeringId;
                }
              }
            }
          } else {
            (productData['subcatIds'] as List<String>)
                .add(selectedSubCat?.docId ?? "");
            if (productData['subCatDocId'] == null
                // &&
                //     subCat.name == subCatProd.subcatData.title
                ) {
              // catId = subCat.docId;
              productData['subCatDocId'] = selectedSubCat?.docId ?? "";
              productData['mainCatDocId'] = selectedSubCat?.offeringId ?? "";
            }
          }
          if (selectedSubCat != null && createNew) {
            for (var apiId in data.first['subcatIds']) {
              String? subCatDocId = Get.find<HomeCtrl>()
                  .vendors
                  .firstWhereOrNull(
                      (element) => element.docId == widget.vendor.docId)
                  ?.apiSubCatIds
                  .firstWhereOrNull((el) => el.subCatApiId == apiId.toString())
                  ?.subCatdocId;
              if (subCatDocId != null) {
                final subCat = Get.find<HomeCtrl>()
                    .subcategories
                    .firstWhereOrNull(
                        (element) => element.docId == subCatDocId);
                if (subCat != null) {
                  (productData['subcatIds'] as List<String>).add(subCat.docId);
                }
              }
            }
          }
          print("------2");
          print("------$productData");
          // Set only the null values in productData to defaults according to ProductModel's data types
          productData.forEach((key, value) {
            if (value == null) {
              if ([
                'name',
                'lowerName',
                'mainCatDocId',
                'subCatDocId',
                'vendorDocId',
                'sku',
                'description',
                'userType',
                'defaultImage',
                'docId',
              ].contains(key)) {
                productData[key] = "";
              } else if ([
                'minPrice',
                'maxPrice',
                'priorityNo',
                'rating',
                'quantitySold',
                'totalSalesAmount',
                'createdAt',
              ].contains(key)) {
                productData[key] = 0;
              } else if ([
                'show',
                'topSelling',
              ].contains(key)) {
                productData[key] = false;
              } else if ([
                'combinationNames',
                'subcatIds',
                'tags',
                'detailsList',
              ].contains(key)) {
                productData[key] = <String>[];
              }
            }
          });
          // Ensure all variants in data.first['variants'] have their null fields set to defaults according to NewVariantModel's data types
          if (data.first['variants'] is List) {
            for (var variant in data.first['variants']) {
              if (variant is Map<String, dynamic>) {
                variant.forEach((key, value) {
                  if (value == null) {
                    if ([
                      'docId',
                      'id',
                      'aqpiId',
                      'productId',
                      'subCatId',
                      'mainCatId',
                      'lowerName',
                      'description',
                      'priceType',
                    ].contains(key)) {
                      variant[key] = "";
                    } else if ([
                      'fixedprice',
                    ].contains(key)) {
                      variant[key] = 0;
                    } else if ([
                      'defaultt',
                      'show',
                    ].contains(key)) {
                      variant[key] = false;
                    } else if ([
                      'images',
                      'newImages',
                      'priceRange',
                    ].contains(key)) {
                      variant[key] = <dynamic>[];
                    } else if ([
                      'detailTypes',
                    ].contains(key)) {
                      variant[key] = <String, dynamic>{};
                    }
                  }
                });
              }
            }
          }
          final product = ProductModel.fromJson(productData);
          final fbData = await FBFireStore.products
              .where('sku', isEqualTo: product.sku)
              .get();
          print("------3");
          String? existingId;
          if (fbData.docs.isNotEmpty) {
            existingId = fbData.docs.first.id;
          }
          if (existingId != null) {
            print("------4");
            // print("------${data.first}");
            await FBFireStore.products.doc(existingId).update(productData);
            print("------ss");
            List<NewVariantModel> existingVariants = [];
            final variantData = await FBFireStore.variants
                .where('productId', isEqualTo: existingId)
                .get();

            existingVariants = variantData.docs.isEmpty
                ? []
                : variantData.docs
                    .map((e) => NewVariantModel.fromDocSnap(e))
                    .toList();
            print("------ss3");

            Map<String, List<String>> allDataProduct = {};
            for (var element in (data.first['variants'] as List)) {
              // (element['detailTypes'] as Map).forEach((key, value) {
              //   if (allDataProduct.containsKey(key as String)) {
              //     List listData2 = allDataProduct[key] ?? [];
              //     final listData = listData2.map((e) => e.toString()).toList();
              //     listData.addAll(element['detailTypes']);
              //     listData.toSet();
              //     listData.toList();
              //     allDataProduct[key] = listData;
              //   } else {
              //     allDataProduct.addEntries({
              //       (key.toString()):
              //           (value as List).map((e) => e.toString()).toList()
              //     }.entries);
              //   }
              // });
              // final subCatData = await FBFireStore.subCategories
              //     .doc(product.defaultSubCatDocId)
              //     .get();
              // final subCat = SubCategory.fromJson(subCatData.data() ?? {});
              // if (subCat.allData.isEmpty) {
              //   subCat.allData.addAll(allDataProduct);
              // } else {
              //   subCat.allData.forEach(
              //     (key, value) {
              //       if (allDataProduct.containsKey(key)) {
              //         List listData2 = allDataProduct[key] ?? [];
              //         final listData =
              //             listData2.map((e) => e.toString()).toList();
              //         listData.addAll(value);
              //         listData.toSet();
              //         listData.toList();
              //         allDataProduct[key] = listData;
              //       } else {
              //         allDataProduct.addEntries({
              //           (key.toString()):
              //               (value as List).map((e) => e.toString()).toList()
              //         }.entries);
              //       }
              //     },
              //   );
              // }
              String? vardocId = existingVariants
                  .firstWhereOrNull((e) => e.aqpiId == element['id'])
                  ?.docId;
              if (vardocId == null) {
                await FBFireStore.variants.add({
                  'id': getRandomId(6),
                  // 'newImages': [],
                  'images': element['images'],
                  'description': element['description'],
                  'defaultt': element['defaultt'],
                  'show': element['show'],
                  'lowerName': data.first['lowerName'],
                  'fixedprice': element['fixedprice'],
                  'priceType': element['priceType'],
                  'priceRange': element['priceRange'],
                  'detailTypes': element['detailTypes'],
                  'productId': existingId,
                  'subCatId': product.defaultSubCatDocId,
                  'mainCatId': product.mainCatDocId,
                  'apiId': element['id'],
                });
                print("------4==z");
              } else {
                print("------5==a");
                await FBFireStore.variants.doc(vardocId).update({
                  // 'id': getRandomId(6),
                  // 'newImages': [],
                  'images': element['images'],
                  'description': element['description'],
                  'defaultt': element['defaultt'],
                  'show': element['show'],
                  'lowerName': data.first['lowerName'],
                  'fixedprice': element['fixedprice'],
                  'priceType': element['priceType'],
                  'priceRange': element['priceRange'],
                  'detailTypes': element['detailTypes'],
                  'productId': existingId,
                  'subCatId': product.defaultSubCatDocId,
                  'mainCatId': product.mainCatDocId,
                  // 'apiId': element['id'],
                });
                print("------5==z");
              }
              (element['detailTypes'] as Map?)?.forEach((key, value) {
                print('Processing key: $key, value: $value');

                if (allDataProduct.containsKey(key as String)) {
                  List listData2 = allDataProduct[key] ?? [];
                  print('Existing listData2 for key "$key": $listData2');

                  final listData = listData2.map((e) => e.toString()).toList();
                  print('Mapped listData (toString): $listData');

                  listData.addAll([value as String]);
                  print('After adding element[\'detailTypes\']: $listData');

                  // Note: toSet() and toList() are not assigned back, so ineffective here.
                  // Assign back to remove duplicates:
                  final uniqueList = listData.toSet().toList();
                  print('Unique listData after toSet and toList: $uniqueList');

                  allDataProduct[key] = uniqueList;
                  print('Updated allDataProduct[$key]: ${allDataProduct[key]}');
                } else {
                  print(
                      'Key "$key" not found in allDataProduct. Adding new entry.');
                  allDataProduct.addEntries({
                    (key.toString()): [value as String]
                    // (value as List).map((e) => e.toString()).toList()
                  }.entries);
                  print(
                      'allDataProduct after adding new entry: $allDataProduct');
                }
              });

              final subCatData = await FBFireStore.subCategories
                  .doc(product.defaultSubCatDocId)
                  .get();
              print('Fetched subCategory data: ${subCatData.data()}');

              final subCat = SubCategory.fromJson(subCatData.data() ?? {});
              print('Parsed subCategory: $subCat');

              if (subCat.allData.isEmpty) {
                print('subCat.allData is empty. Adding allDataProduct.');
                subCat.allData.addAll(allDataProduct);
                print('subCat.allData after adding: ${subCat.allData}');
              } else {
                print('subCat.allData is not empty. Merging data.');
                subCat.allData.forEach((key, value) {
                  print('Processing subCat.allData key: $key, value: $value');

                  if (allDataProduct.containsKey(key)) {
                    List listData2 = allDataProduct[key] ?? [];
                    print('Existing allDataProduct[$key]: $listData2');

                    final listData =
                        listData2.map((e) => e.toString()).toList();
                    print('Mapped listData (toString): $listData');
                    print(value);
                    listData.addAll(value);
                    print('After adding subCat.allData value: $listData');

                    // Remove duplicates by assigning back
                    final uniqueList = listData.toSet().toList();
                    print(
                        'Unique listData after toSet and toList: $uniqueList');

                    allDataProduct[key] = uniqueList;
                    print(
                        'Updated allDataProduct[$key]: ${allDataProduct[key]}');
                  } else {
                    print(
                        'Key "$key" not found in allDataProduct. Adding new entry.');
                    allDataProduct.addEntries({
                      (key.toString()):
                          (value as List).map((e) => e.toString()).toList()
                    }.entries);
                    print(
                        'allDataProduct after adding new entry: $allDataProduct');
                  }
                });
              }
            }
            await FBFireStore.subCategories
                .doc(product.defaultSubCatDocId)
                .update({'allData': allDataProduct});
          } else {
            print("------7");

            final id = await FBFireStore.products.add(productData);
            Map<String, List<String>> allDataProduct = {};

            for (var element in (data.first['variants'] as List)) {
              print("------eee");

              await FBFireStore.variants.add({
                'id': getRandomId(6),
                // 'newImages': [],
                'images': element['images'],
                'description': element['description'],
                'defaultt': element['defaultt'],
                'show': element['show'],
                'lowerName': data.first['lowerName'],
                'fixedprice': element['fixedprice'],
                'priceType': element['priceType'],
                'priceRange': element['priceRange'],
                'detailTypes': element['detailTypes'],
                'productId': id.id,
                'subCatId': product.defaultSubCatDocId,
                'mainCatId': product.mainCatDocId
              });
              (element['detailTypes'] as Map?)?.forEach((key, value) {
                if (allDataProduct.containsKey(key as String)) {
                  List listData2 = allDataProduct[key] ?? [];
                  final listData = listData2.map((e) => e.toString()).toList();
                  listData.addAll([value as String]);
                  listData.toSet();
                  listData.toList();
                  allDataProduct[key] = listData;
                } else {
                  allDataProduct.addEntries({
                    (key.toString()): [(value as String)]
                  }.entries);
                }
              });
              final subCatData = await FBFireStore.subCategories
                  .doc(product.defaultSubCatDocId)
                  .get();
              final subCat = SubCategory.fromJson(subCatData.data() ?? {});
              if (subCat.allData.isEmpty) {
                subCat.allData.addAll(allDataProduct);
              } else {
                subCat.allData.forEach(
                  (key, value) {
                    if (allDataProduct.containsKey(key)) {
                      List listData2 = allDataProduct[key] ?? [];
                      final listData =
                          listData2.map((e) => e.toString()).toList();
                      listData.addAll(value);
                      listData.toSet();
                      listData.toList();
                      allDataProduct[key] = listData;
                    } else {
                      allDataProduct.addEntries({
                        (key.toString()):
                            value.map((e) => e.toString()).toList()
                      }.entries);
                    }
                  },
                );
              }
            }
            await FBFireStore.subCategories
                .doc(product.defaultSubCatDocId)
                .update({'allData': allDataProduct});
          }

          print(productData);

          productCompletionCount++;
          setState(() {});
        }
        String? subCatDocId = Get.find<HomeCtrl>()
            .vendors
            .firstWhereOrNull((element) => element.docId == widget.vendor.docId)
            ?.apiSubCatIds
            .firstWhereOrNull((el) => el.subCatApiId == subCatProd.toString())
            ?.subCatdocId;
        print("------9");
        final maxPriceProdData = await FBFireStore.products
            .where('defaultSubCatDocId', isEqualTo: subCatDocId)
            .orderBy('maxPrice', descending: true)
            .limit(1)
            .get();
        print("------10");
        final minPriceProdData = await FBFireStore.products
            .where('defaultSubCatDocId', isEqualTo: subCatDocId)
            .orderBy('minPrice', descending: false)
            .limit(1)
            .get();
        print("------11");
        final maxPriceProd =
            ProductModel.fromDocSnap(maxPriceProdData.docs.first);
        print("------12");
        final minPriceProd =
            ProductModel.fromDocSnap(minPriceProdData.docs.first);
        print("------13");

        print("------14");
        if (subCatDocId != null) {
          await FBFireStore.subCategories.doc(subCatDocId).update({
            'maxPrice': maxPriceProd.maxPrice,
            "minPrice": minPriceProd.minPrice
          });
          print("------15");
        }
      } //   bre9
      prodConverted = true;
    } catch (e) {
      showAppSnackBar(context, "---${e.toString()}");
      debugPrint(e.toString());
    }
    convertingProd = false;
    setState(() {});
  }
}
