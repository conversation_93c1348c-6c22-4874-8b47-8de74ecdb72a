import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wedding_super_admin/controller/home_ctrl.dart';
import 'package:wedding_super_admin/models/bookings_model.dart';
import 'package:wedding_super_admin/models/session_model.dart';
import 'package:wedding_super_admin/models/user_model.dart';
import 'package:wedding_super_admin/shared/firebase.dart';
import 'package:wedding_super_admin/shared/methods.dart';
import 'package:wedding_super_admin/views/common/buttons.dart';
import 'package:wedding_super_admin/views/common/page_header.dart';
import '../../../shared/booking_methods.dart';
import '../../../shared/theme.dart';

class BookingForm extends StatefulWidget {
  const BookingForm({super.key, this.bookingId});
  final String? bookingId;
  @override
  State<BookingForm> createState() => _BookingFormState();
}

class _BookingFormState extends State<BookingForm> {
  bool onSubmitLoad = false;
  List<String> slotsList = [];
  DateTime selectedDate = DateTime.now();
  String? selectedSlot;
  int? slotGap;
  BookingsModel? oldBookingData;
  TextEditingController fnameCtrl = TextEditingController();
  TextEditingController lnameCtrl = TextEditingController();
  TextEditingController emailCtrl = TextEditingController();
  TextEditingController mobileCtrl = TextEditingController();
  TextEditingController bookingComment = TextEditingController();
  String? selectedConsultantId;
  String? selectedStylerId;
  String? selectedEnquiryId;
  // List<UserModel> searchedUserList = [];
  UserModel? selectedUser;
  List<SessionModel> userActiveEnquiryList = [];

  setBookingForm() {
    final hctrl = Get.find<HomeCtrl>();
    final settings = hctrl.settings;
    if (settings != null) {
      DateTime bookingStartTime = DateTime(
        selectedDate.year,
        selectedDate.month,
        selectedDate.day,
        settings.startHour,
        settings.startMin,
      );

      final bookingEndTime = DateTime(
        selectedDate.year,
        selectedDate.month,
        selectedDate.day,
        settings.endHour,
        settings.endMin,
      );

      slotGap = slotGap ?? settings.slotGap;
      slotsList = generateTimeSlotsWithBlocked(
        bookingStartTime,
        bookingEndTime,
        settings.slotGap,
        hctrl.blockedSlotModelList,
      );
    }
  }

  Future<List<UserModel>> getSearchedData(String str) async {
    // results.addAll(await FBFireStore.vendors
    //     .where('company_name', isGreaterThanOrEqualTo: str)
    //     .where('company_name', isLessThanOrEqualTo: "$str\uf7ff")

    //     // .limit(5)
    //     .get()
    //     .then((value) => value.docs
    //         .map((e) => VendorModel.fromSnap(e))
    //         .toList()
    //         .map((e) => e.companyName.toLowerCase())));
    List<UserModel> searchedUserList = [];
    if (str.isEmpty) {
      searchedUserList.clear();
    }
    searchedUserList.clear();
    final userByNameSnap = await FBFireStore.users
        .where('name', isGreaterThanOrEqualTo: str)
        .where('name', isLessThanOrEqualTo: "$str\uf7ff")
        .limit(5)
        .get();
    searchedUserList.addAll(
      userByNameSnap.docs.map((e) => UserModel.fromDocSnap(e)).toList(),
    );
    return searchedUserList;
  }

  bool editBookingDetails = true;

  setBookingData() async {
    final hCtrl = Get.find<HomeCtrl>();
    oldBookingData = hCtrl.bookingList.firstWhereOrNull(
      (element) => element.docID == widget.bookingId,
    );
    if (oldBookingData != null) {
      editBookingDetails = false;
      final fullName = oldBookingData!.fullnameAtBooking ?? '';
      if (fullName.contains(' ')) {
        final firstSpaceIndex = fullName.indexOf(' ');
        fnameCtrl.text = fullName.substring(0, firstSpaceIndex);
        lnameCtrl.text = fullName.substring(firstSpaceIndex + 1);
      } else {
        fnameCtrl.text = fullName;
        lnameCtrl.text = '';
      }
      emailCtrl.text = oldBookingData?.emailAtBooking ?? '';
      mobileCtrl.text = oldBookingData?.mobileAtBooking ?? '';
      bookingComment.text = oldBookingData?.bookingComment ?? '';
      final userSnap = await FBFireStore.users.doc(oldBookingData?.uId).get();
      if (userSnap.exists) {
        selectedUser = UserModel.fromDocSnap(userSnap);
      }
      selectedConsultantId = oldBookingData?.teamMemberId;
      selectedStylerId = oldBookingData?.stylerId;
      userActiveEnquiryList.clear();
      userActiveEnquiryList.addAll(
        hCtrl.activeSessionsList.where(
          (element) => element.uId == oldBookingData?.uId,
        ),
      );
      if (userActiveEnquiryList
          .map((e) => e.docId)
          .contains(oldBookingData?.sessionId)) {
        selectedEnquiryId = oldBookingData?.sessionId;
      }
      slotGap = oldBookingData?.slotgap;
      selectedDate = oldBookingData?.bookingDate ?? DateTime.now();
      selectedSlot = dateTimeToSlotString(
        oldBookingData!.bookingstartTime,
        oldBookingData!.bookingendTime,
      );
    }

    if (mounted) setState(() {});
  }

  @override
  void initState() {
    super.initState();
    if (widget.bookingId != null) {
      setBookingData();
    }
  }

  @override
  Widget build(BuildContext context) {
    setBookingForm();
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
      child: Column(
        children: [
          PageHeaderWithTrailingAndBack(
            title: 'Booking Form',
            showTrailing: true,
            trailing: onSubmitLoad
                ? const Center(
                    child: SizedBox(
                      height: 25,
                      width: 25,
                      child: CircularProgressIndicator(
                        strokeWidth: 2.5,
                        color: themeColor,
                      ),
                    ),
                  )
                : ElevatedButton.icon(
                    onPressed: () async {
                      if (selectedUser == null) {
                        showErrorAppSnackBar(context, 'User not selected');
                        return;
                      }

                      if (selectedSlot == null) {
                        showErrorAppSnackBar(context, 'Slot not selected');
                        return;
                      }

                      if (onSubmitLoad) return;
                      setState(() {
                        onSubmitLoad = true;
                      });
                      try {
                        final convertedSlot = stringToDateTime(selectedSlot!);

                        final bookingDate = Timestamp.fromDate(selectedDate);

                        final bookingstartTime = Timestamp.fromDate(
                          DateTime(
                            selectedDate.year,
                            selectedDate.month,
                            selectedDate.day,
                            convertedSlot['startTime']!.hour,
                            convertedSlot['startTime']!.minute,
                          ),
                        );
                        final bookingendTime = Timestamp.fromDate(
                          DateTime(
                            selectedDate.year,
                            selectedDate.month,
                            selectedDate.day,
                            convertedSlot['endTime']!.hour,
                            convertedSlot['endTime']!.minute,
                          ),
                        );

                        final bookingRef = widget.bookingId != null
                            ? FBFireStore.bookings.doc(widget.bookingId)
                            : FBFireStore.bookings.doc();

                        final inquiryRef = (selectedEnquiryId != null) &&
                                (selectedEnquiryId != 'createNew')
                            ? FBFireStore.sessions.doc(selectedEnquiryId)
                            : FBFireStore.sessions.doc();

                        final messageData = {
                          'type': MessageTypes.booking,
                          'sessionId': inquiryRef.id,
                          'loadingText': 'Booking details',
                          'data': {
                            'date': selectedDate.millisecondsSinceEpoch,
                            'bookingstartTime':
                                bookingstartTime.millisecondsSinceEpoch,
                            'bookingendTime':
                                bookingendTime.millisecondsSinceEpoch,
                            'slotgap': slotGap,
                            'bookingId': bookingRef.id,
                          },
                          'senderId': FBAuth.auth.currentUser?.uid,
                          'sendAt': widget.bookingId != null
                              ? oldBookingData?.createdAt.millisecondsSinceEpoch
                              : DateTime.now().millisecondsSinceEpoch,
                        };

                        final data = {
                          'uId': selectedUser?.docId,
                          'createdAt':
                              (oldBookingData?.createdAt ?? DateTime.now())
                                  .millisecondsSinceEpoch,
                          'createdByDocId': oldBookingData?.createdBy ??
                              FBAuth.auth.currentUser?.uid,
                          'createdBy': oldBookingData?.createdBy ??
                              Get.find<HomeCtrl>().currentUserType,
                          'teamMemberId': selectedConsultantId,
                          'stylerId': selectedStylerId,
                          'sessionId': inquiryRef.id,
                          'messageId': null,
                          'bookingDate': bookingDate.millisecondsSinceEpoch,
                          'bookingstartTime':
                              bookingstartTime.millisecondsSinceEpoch,
                          'bookingendTime':
                              bookingendTime.millisecondsSinceEpoch,
                          'slotgap': slotGap,
                          'teamsLink': '',
                          'productIds': oldBookingData?.productIds ?? [],
                          'teamMemberComment': '',
                          'bookingComment': bookingComment.text.trim(),
                          'isCompleted': false,
                          'completedAt': null,
                          'cancelledAt': null,
                          'isCancelled': false,
                          'emailAtBooking': emailCtrl.text.toLowerCase().trim(),
                          'fullnameAtBooking':
                              '${fnameCtrl.text.trim().toLowerCase()} ${lnameCtrl.text.trim().toLowerCase()}',
                          'mobileAtBooking': mobileCtrl.text.trim(),
                          'cancelledReason': null,
                        };

                        final res =
                            await bookingRef.set(data).then((value) => true);
                        if (selectedEnquiryId == 'createNew') {
                          await FBFireStore.sessions.doc(inquiryRef.id).set({
                            'uId': selectedUser?.docId,
                            'createdAt': DateTime.now().millisecondsSinceEpoch,
                            'variantIds': [],
                            'inquiryId': generateInquiryId(),
                            'consultantId': selectedConsultantId,
                            'stylerId': selectedStylerId,
                            'endedOn': null,
                            'isActive': true,
                            'lastMessage': null,
                            'finalVariantIds': [],
                            'finalVariantIds2': {},
                            'updatedAt': DateTime.now().millisecondsSinceEpoch,
                          });
                        }
                        await FBFireStore.sessions
                            .doc(inquiryRef.id)
                            .collection('messages')
                            .add(messageData);
                        if (res) {
                          await FBFunctions.ff
                              .httpsCallable('sendBookingDetails')
                              .call(<String, String>{
                            'bookingDate':
                                bookingDate.toDate().convertToDDMMYY(),
                            'bookingstartTime':
                                bookingstartTime.toDate().goodTime(),
                            'bookingendTime':
                                bookingendTime.toDate().goodTime(),
                            'emailAtBooking':
                                emailCtrl.text.trim().toLowerCase(),
                            'fullnameAtBooking': capilatlizeFirstLetter(
                              '${fnameCtrl.text.trim().toLowerCase()} ${lnameCtrl.text.trim().toLowerCase()}',
                            ),
                          });
                        }
                        if (context.mounted) {
                          setState(() {
                            onSubmitLoad = false;
                          });
                          showAppSnackBar(
                            context,
                            widget.bookingId != null
                                ? 'Booking Updated'
                                : 'Booking Added',
                          );
                          context.pop();
                        }
                      } on Exception catch (e) {
                        setState(() {
                          onSubmitLoad = false;
                        });
                        debugPrint(e.toString());
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: themeColor,
                      elevation: 0,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(4),
                      ),
                      // padding: const EdgeInsets.fromLTRB(5, 15, 10, 15),
                    ),
                    icon: const Icon(CupertinoIcons.floppy_disk, size: 20),
                    label: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8.0),
                      child: Text(
                        "Save",
                        style: GoogleFonts.livvic(
                          letterSpacing: 1.3,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
          ),
          const SizedBox(height: 20),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (widget.bookingId == null) ...[
                Autocomplete<UserModel>(
                  fieldViewBuilder: (
                    context,
                    textEditingController,
                    focusNode,
                    onFieldSubmitted,
                  ) {
                    return TextFormField(
                      focusNode: focusNode,
                      onFieldSubmitted: (value) {
                        onFieldSubmitted();
                      },
                      onChanged: (value) {
                        getSearchedData(
                          textEditingController.text.toLowerCase().trim(),
                        );
                      },
                      controller: textEditingController,
                      decoration: inpDecor().copyWith(
                        hintText: ' search user',
                        hintStyle: TextStyle(
                          color: Color(0xff737373),
                          fontSize: 14.5,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    );
                  },
                  displayStringForOption: (option) =>
                      '${capilatlizeFirstLetter(option.name ?? '')} , ${option.email}, ${option.phone}',
                  optionsViewBuilder: (context, onSelected, options) {
                    return Align(
                      alignment: Alignment.topLeft,
                      child: Material(
                        elevation: 4,
                        child: ListView.separated(
                          padding: EdgeInsets.zero,
                          itemCount: options.length,
                          shrinkWrap: true,
                          separatorBuilder: (context, index) {
                            return Divider(
                              height: 0,
                              color: Colors.grey.shade400,
                            );
                          },
                          itemBuilder: (context, index) {
                            final UserModel option = options.elementAt(index);
                            final displayText =
                                '${capilatlizeFirstLetter(option.name ?? '')} , ${option.email}, ${option.phone ?? ''}';

                            return true
                                ? Padding(
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 8.0),
                                    child: Row(
                                      children: [
                                        SizedBox(width: 10),
                                        Expanded(
                                          child: Text(
                                            capilatlizeFirstLetter(
                                                option.name ?? ''),
                                            maxLines: 1,
                                            style: TextStyle(
                                              fontSize: 15,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                        ),
                                        SizedBox(width: 10),
                                        Expanded(
                                          child: Text(
                                            option.email,
                                            maxLines: 1,
                                            style: TextStyle(
                                              fontSize: 15,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                        ),
                                        SizedBox(width: 10),
                                        Expanded(
                                          child: Text(
                                            option.phone ?? '',
                                            maxLines: 1,
                                            style: TextStyle(
                                              fontSize: 15,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                        ),
                                        SizedBox(width: 10),
                                      ],
                                    ),
                                  )
                                : ListTile(
                                    title: Text(displayText),
                                    onTap: () {
                                      onSelected(option);
                                    },
                                  );
                          },
                        ),
                      ),
                    );
                  },
                  optionsBuilder: (textEditingValue) {
                    return getSearchedData(
                      textEditingValue.text.toLowerCase().trim(),
                    );
                  },
                  onSelected: (option) {
                    selectedUser = option;
                    final nameSplit = selectedUser?.name?.split(" ").toList();
                    if (nameSplit != null && nameSplit.isNotEmpty) {
                      fnameCtrl.text = nameSplit[0];
                      if (nameSplit.length >= 2) lnameCtrl.text = nameSplit[1];
                    }
                    emailCtrl.text = option.email;
                    mobileCtrl.text = option.phone ?? '';
                  },
                ),
                const SizedBox(height: 15),
              ],
              Row(
                children: [
                  textField('First Name', fnameCtrl),
                  const SizedBox(width: 12.5),
                  textField('Last Name', lnameCtrl),
                ],
              ),
              const SizedBox(height: 20),
              Row(
                children: [
                  textField('Email', emailCtrl),
                  const SizedBox(width: 12.5),
                  textField('Mobile', mobileCtrl),
                ],
              ),
              const SizedBox(height: 20),

              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'Consulatnt',
                          style: GoogleFonts.livvic(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 5),
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Expanded(
                              child: DropdownButtonHideUnderline(
                                child: DropdownButtonFormField(
                                  hint: Text(
                                    'Consultant',
                                    style: GoogleFonts.mulish(
                                      color: const Color(0XffCBCBCB),
                                      fontSize: 14,
                                    ),
                                  ),
                                  decoration: const InputDecoration(
                                    border: OutlineInputBorder(
                                      borderSide: BorderSide(
                                        color: Color(0xffCBCBCB),
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderSide: BorderSide(
                                        color: Color(0xffCBCBCB),
                                      ),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderSide: BorderSide(
                                        color: Color(0xffCBCBCB),
                                      ),
                                    ),
                                  ),
                                  value: selectedConsultantId,
                                  items: [
                                    ...List.generate(
                                      Get.find<HomeCtrl>().consultants.length,
                                      (index) {
                                        final consultant = Get.find<HomeCtrl>()
                                            .consultants[index];
                                        return DropdownMenuItem(
                                          value: consultant.docId,
                                          child: Text(
                                            capilatlizeFirstLetter(
                                              consultant.name,
                                            ),
                                          ),
                                        );
                                      },
                                    ),
                                  ],
                                  onChanged: (value) {
                                    selectedConsultantId = value;
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 12.5),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'Styler',
                          style: GoogleFonts.livvic(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 5),
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Expanded(
                              child: DropdownButtonHideUnderline(
                                child: DropdownButtonFormField(
                                  hint: Text(
                                    'Styler',
                                    style: GoogleFonts.mulish(
                                      color: const Color(0XffCBCBCB),
                                      fontSize: 14,
                                    ),
                                  ),
                                  decoration: const InputDecoration(
                                    border: OutlineInputBorder(
                                      borderSide: BorderSide(
                                        color: Color(0xffCBCBCB),
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderSide: BorderSide(
                                        color: Color(0xffCBCBCB),
                                      ),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderSide: BorderSide(
                                        color: Color(0xffCBCBCB),
                                      ),
                                    ),
                                  ),
                                  value: selectedStylerId,
                                  items: [
                                    ...List.generate(
                                      Get.find<HomeCtrl>().stylers.length,
                                      (index) {
                                        final styler =
                                            Get.find<HomeCtrl>().stylers[index];
                                        return DropdownMenuItem(
                                          value: styler.docId,
                                          child: Text(
                                            capilatlizeFirstLetter(styler.name),
                                          ),
                                        );
                                      },
                                    ),
                                  ],
                                  onChanged: (value) {
                                    selectedStylerId = value;
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'Assign Enquiry',
                          style: GoogleFonts.livvic(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 5),
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Expanded(
                              child: DropdownButtonHideUnderline(
                                child: DropdownButtonFormField(
                                  hint: Text(
                                    'Enquiry',
                                    style: GoogleFonts.mulish(
                                      color: const Color(0XffCBCBCB),
                                      fontSize: 14,
                                    ),
                                  ),
                                  decoration: const InputDecoration(
                                    border: OutlineInputBorder(
                                      borderSide: BorderSide(
                                        color: Color(0xffCBCBCB),
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderSide: BorderSide(
                                        color: Color(0xffCBCBCB),
                                      ),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderSide: BorderSide(
                                        color: Color(0xffCBCBCB),
                                      ),
                                    ),
                                  ),
                                  value: selectedEnquiryId,
                                  items: [
                                    ...List.generate(
                                      userActiveEnquiryList.length,
                                      (index) {
                                        final activeEnquiry =
                                            userActiveEnquiryList[index];
                                        return DropdownMenuItem(
                                          value: activeEnquiry.docId,
                                          child: Text(
                                            activeEnquiry.inquiryId,

                                            /* subtitle: Text(
                                              capilatlizeFirstLetter(
                                                  activeEnquiry.lastMessage?[
                                                      'loadingText']),
                                            ), */
                                          ),
                                        );
                                      },
                                    ),
                                    const DropdownMenuItem(
                                      value: 'createNew',
                                      child: Text('Create New'),
                                    ),
                                  ],
                                  onChanged: (value) {
                                    selectedEnquiryId = value;
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 12.5),
                  const Expanded(child: SizedBox()),
                ],
              ),

              const SizedBox(height: 25),
              if (oldBookingData != null)
                true
                    ? Container(
                        padding: EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color:
                              true ? dashboardSelectedColor : Color(0xfffbf1ef),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Booking Details',
                              style: TextStyle(
                                letterSpacing: .5,
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            SizedBox(height: 20),
                            Row(
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "Date:",
                                        style: TextStyle(
                                          letterSpacing: .7,
                                          color: Color.fromARGB(
                                            255,
                                            107,
                                            107,
                                            107,
                                          ),
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                      SizedBox(height: 5),
                                      Text(
                                        oldBookingData?.bookingDate
                                                .goodDayDate() ??
                                            '-',
                                        style: TextStyle(
                                          letterSpacing: .7,
                                          color:
                                              Color.fromARGB(255, 80, 80, 80),
                                          fontSize: 14.5,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                SizedBox(width: 15),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "Start Time:",
                                        style: TextStyle(
                                          letterSpacing: .7,
                                          color: Color.fromARGB(
                                            255,
                                            107,
                                            107,
                                            107,
                                          ),
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                      SizedBox(height: 5),
                                      Text(
                                        oldBookingData?.bookingstartTime
                                                .goodTime() ??
                                            '-',
                                        style: TextStyle(
                                          letterSpacing: .7,
                                          color:
                                              Color.fromARGB(255, 80, 80, 80),
                                          fontSize: 14.5,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                SizedBox(width: 15),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "End Time:",
                                        style: TextStyle(
                                          letterSpacing: .7,
                                          color: Color.fromARGB(
                                            255,
                                            107,
                                            107,
                                            107,
                                          ),
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                      SizedBox(height: 5),
                                      Text(
                                        oldBookingData?.bookingendTime
                                                .goodTime() ??
                                            '-',
                                        style: TextStyle(
                                          letterSpacing: .7,
                                          color:
                                              Color.fromARGB(255, 80, 80, 80),
                                          fontSize: 14.5,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                SizedBox(width: 15),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "Slot Gap:",
                                        style: TextStyle(
                                          letterSpacing: .7,
                                          color: Color.fromARGB(
                                            255,
                                            107,
                                            107,
                                            107,
                                          ),
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                      SizedBox(height: 5),
                                      Text(
                                        oldBookingData?.slotgap.toString() ??
                                            '-',
                                        style: TextStyle(
                                          letterSpacing: .7,
                                          color:
                                              Color.fromARGB(255, 80, 80, 80),
                                          fontSize: 14.5,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                SizedBox(width: 15),
                                Align(
                                  alignment: Alignment.center,
                                  child: OutlinedButton(
                                    style: OutlinedButton.styleFrom(
                                      overlayColor: Colors.transparent,
                                      padding: EdgeInsets.all(15),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(5),
                                      ),
                                      side:
                                          BorderSide(color: Color(0xffE68B7B)),
                                      backgroundColor: Colors.white,
                                    ),
                                    // icon: Icon(CupertinoIcons.pen),
                                    onPressed: () {
                                      editBookingDetails = true;
                                      setState(() {});
                                    },
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          Icons.edit,
                                          size: 18,
                                          color: Color(0xffE68B7B),
                                        ),
                                        SizedBox(width: 10),
                                        Text(
                                          'Edit',
                                          style: TextStyle(
                                            color: Color(0xffE68B7B),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            // SizedBox(height: 15),
                          ],
                        ),
                      )
                    : Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            width: double.maxFinite,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 15,
                              vertical: 8,
                            ),
                            decoration: BoxDecoration(
                              color: dashboardColor,
                              border: Border.all(color: dashboardColor),
                            ),
                            child: Text(
                              'Booking Details',
                              style: GoogleFonts.livvic(
                                fontSize: 15,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          // const SizedBox(height: 15),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 15,
                              vertical: 15,
                            ),
                            decoration: const BoxDecoration(
                              border: Border(
                                bottom: BorderSide(
                                  color: Color.fromARGB(255, 193, 193, 193),
                                ),
                                left: BorderSide(
                                  color: Color.fromARGB(255, 193, 193, 193),
                                ),
                                right: BorderSide(
                                  color: Color.fromARGB(255, 193, 193, 193),
                                ),
                              ),
                            ),
                            child: Row(
                              children: [
                                Expanded(
                                  child: Text.rich(
                                    TextSpan(
                                      children: [
                                        const TextSpan(
                                          text: 'Date: ',
                                          style: TextStyle(
                                            fontSize: 13.5,
                                            color: Color(0xff3E3E3E),
                                          ),
                                        ),
                                        TextSpan(
                                          text: oldBookingData?.bookingDate
                                              .goodDayDate(),
                                          style: const TextStyle(
                                            fontSize: 15,
                                            color: Color(0xff6C6C6C),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 15),
                                Expanded(
                                  child: Text.rich(
                                    TextSpan(
                                      children: [
                                        const TextSpan(
                                          text: 'Start Time: ',
                                          style: TextStyle(
                                            fontSize: 13.5,
                                            color: Color(0xff3E3E3E),
                                          ),
                                        ),
                                        TextSpan(
                                          text: oldBookingData?.bookingstartTime
                                              .goodTime(),
                                          style: const TextStyle(
                                            fontSize: 15,
                                            color: Color(0xff6C6C6C),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 15),
                                Expanded(
                                  child: Text.rich(
                                    TextSpan(
                                      children: [
                                        const TextSpan(
                                          text: 'End Time: ',
                                          style: TextStyle(
                                            fontSize: 13.5,
                                            color: Color(0xff3E3E3E),
                                          ),
                                        ),
                                        TextSpan(
                                          text: oldBookingData?.bookingendTime
                                              .goodTime(),
                                          style: const TextStyle(
                                            fontSize: 15,
                                            color: Color(0xff6C6C6C),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 15),
                                Expanded(
                                  child: Text.rich(
                                    TextSpan(
                                      children: [
                                        const TextSpan(
                                          text: 'Slot Gap: ',
                                          style: TextStyle(
                                            fontSize: 13.5,
                                            color: Color(0xff3E3E3E),
                                          ),
                                        ),
                                        TextSpan(
                                          text: oldBookingData?.slotgap
                                              .toString(),
                                          style: const TextStyle(
                                            fontSize: 15,
                                            color: Color(0xff6C6C6C),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 15),
                                ButtonWithIcon(
                                  icon: Icons.edit,
                                  buttonName: 'Edit',
                                  onTap: () {
                                    editBookingDetails = true;
                                    setState(() {});
                                  },
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),

              const SizedBox(height: 25),
              if (editBookingDetails)
                IntrinsicHeight(
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: CalendarDatePicker(
                          initialDate: isTodayOrBefore(selectedDate)
                              ? DateTime.now()
                              : selectedDate,
                          firstDate: DateTime.now(),
                          lastDate: DateTime(3000),
                          onDateChanged: (value) {
                            selectedDate = value;
                            setState(() {});
                          },
                        ),
                      ),
                      const SizedBox(width: 15),
                      const VerticalDivider(),
                      const SizedBox(width: 15),
                      Expanded(
                        flex: 2,
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text("Slots"),
                              const SizedBox(height: 15),
                              slotsList.isEmpty
                                  ? const Center(
                                      child: Text("No Slots Available"),
                                    )
                                  : Wrap(
                                      // maxCrossAxisExtent: 170,
                                      // mainAxisSpacing: 15,
                                      // crossAxisSpacing: 15,
                                      spacing: 10,
                                      runSpacing: 10,
                                      runAlignment: WrapAlignment.start,
                                      alignment: WrapAlignment.start,
                                      children: [
                                        ...List.generate(slotsList.length, (
                                          index,
                                        ) {
                                          final selected =
                                              selectedSlot == slotsList[index];
                                          return InkWell(
                                            onTap: () {
                                              selectedSlot = slotsList[index];
                                              setState(() {});
                                            },
                                            child: Container(
                                              width: 180,
                                              padding: const EdgeInsets.all(7),
                                              decoration: BoxDecoration(
                                                color: selected
                                                    ? themeColor
                                                    : null,
                                                border: Border.all(
                                                  color: selected
                                                      ? themeColor
                                                      : const Color(
                                                          0xff6C6C6C,
                                                        ),
                                                ),
                                              ),
                                              child: Center(
                                                child: Text(
                                                  slotsList[index],
                                                  style: GoogleFonts.livvic(
                                                    color: selected
                                                        ? Colors.white
                                                        : null,
                                                    fontSize: 14,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          );
                                        }),
                                      ],
                                    ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              const SizedBox(height: 25),
              // Text("SelectedDate: ${selectedDate.convertToDDMMYY()}"),
              // Text("selectedSLot: ${selectedSlot}")
            ],
          ),
        ],
      ),
    );
  }

  Widget textField(String labelText, TextEditingController controller) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            labelText,
            style: GoogleFonts.livvic(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 5),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Expanded(
                child: TextField(
                  controller: controller,
                  decoration: InputDecoration(
                    hintText: ' $labelText',
                    hintStyle: GoogleFonts.mulish(
                      color: const Color(0XffCBCBCB),
                      fontSize: 14,
                    ),
                    border: const OutlineInputBorder(
                      borderSide: BorderSide(color: Color(0xffCBCBCB)),
                    ),
                    enabledBorder: const OutlineInputBorder(
                      borderSide: BorderSide(color: Color(0xffCBCBCB)),
                    ),
                    focusedBorder: const OutlineInputBorder(
                      borderSide: BorderSide(color: Color(0xffCBCBCB)),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
