import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import '../../../models/bookings_model.dart';
import '../../../models/teammodel.dart';
import '../../../shared/methods.dart';
import '../../../shared/router.dart';
import '../../../shared/theme.dart';

class BookingCard extends StatefulWidget {
  const BookingCard({super.key, required this.bookingsModel});
  final BookingsModel bookingsModel;

  @override
  State<BookingCard> createState() => _BookingCardState();
}

class _BookingCardState extends State<BookingCard> {
  @override
  Widget build(BuildContext context) {
    return InkWell(
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          // color: themeData.scaffoldBackgroundColor.withOpacity(.93),
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
          border: Border.all(
            color: dashboardSelectedColor2.withOpacity(.3),
            width: 1.7,
          ),
          boxShadow: [
            BoxShadow(
              blurRadius: 0,
              offset: const Offset(-4, 0),
              color: dashboardSelectedColor2.withOpacity(.5),
            ),
            // BoxShadow(
            //   blurRadius: 0,
            //   offset: Offset(4, 0),
            //   color: dashboardSelectedColor.withOpacity(.4),
            // ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // SizedBox(height: 10),
            Row(
              children: [
                CircleAvatar(
                  radius: 18,
                  child: Center(
                    child: Text(
                      capilatlizeFirstLetter(
                        widget.bookingsModel.fullnameAtBooking
                                ?.substring(0, 1)
                                .toUpperCase() ??
                            "",
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 10),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      capilatlizeFirstLetter(
                        widget.bookingsModel.fullnameAtBooking ?? "",
                      ),
                      style: const TextStyle(
                        letterSpacing: .7,
                        // color: Color.fromARGB(255, 93, 93, 93),
                        color: Color.fromARGB(255, 34, 34, 34),
                        fontWeight: FontWeight.w500,
                        fontSize: 15,
                      ),
                    ),
                    Text(
                      widget.bookingsModel.mobileAtBooking ?? "",
                      style: const TextStyle(
                        fontSize: 12,
                        color: Color.fromARGB(255, 118, 118, 118),
                      ),
                    ),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 12),
            const Text(
              "Booking for consultation",
              style: TextStyle(
                fontSize: 13,
                letterSpacing: .7,
                fontWeight: FontWeight.w500,
                color: Color.fromARGB(255, 33, 33, 33),
              ),
            ),
            const SizedBox(height: 10),
            IntrinsicHeight(
              child: Row(
                children: [
                  const Icon(
                    CupertinoIcons.calendar,
                    color: Color.fromARGB(255, 118, 118, 118),
                    size: 14,
                  ),
                  const SizedBox(width: 5),
                  Text(
                    widget.bookingsModel.bookingDate.goodDayDate(),
                    style: const TextStyle(
                      color: Color.fromARGB(255, 118, 118, 118),
                      fontSize: 12,
                    ),
                  ),
                  const VerticalDivider(),
                  const Icon(
                    CupertinoIcons.clock,
                    color: Color.fromARGB(255, 118, 118, 118),
                    size: 14,
                  ),
                  const SizedBox(width: 5),
                  Text(
                    DateFormat(
                      'hh:mm a',
                    ).format(widget.bookingsModel.bookingstartTime),
                    style: const TextStyle(
                      color: Color.fromARGB(255, 118, 118, 118),
                      fontSize: 12,
                    ),
                  ),
                  const VerticalDivider(),
                  const Icon(
                    CupertinoIcons.stopwatch,
                    color: Color.fromARGB(255, 118, 118, 118),
                    size: 14,
                  ),
                  const SizedBox(width: 5),
                  Text(
                    '${widget.bookingsModel.slotgap} min',
                    style: const TextStyle(
                      color: Color.fromARGB(255, 118, 118, 118),
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 18),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    style: OutlinedButton.styleFrom(
                      overlayColor: const Color.fromARGB(255, 131, 131, 131),
                      side: const BorderSide(color: Colors.transparent),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(15),
                      ),
                    ),
                    onPressed: () async {
                      context.push(
                        '${Routes.booking}/${widget.bookingsModel.docID}',
                      );
                    },
                    child: Text(
                      "View Details",
                      style: GoogleFonts.livvic(
                        fontWeight: FontWeight.w600,
                        color: themeColor,
                        fontSize: 12,
                        letterSpacing: 1.3,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            // const SizedBox(height: 5),
          ],
        ),
      ),
    );
  }

  Future<dynamic> bookingDetailsDialog(
    BuildContext context,
    BookingsModel bookingsModel,
  ) {
    return showDialog(
      context: context,
      builder: (context) {
        return Dialog(
          backgroundColor: Colors.white,
          surfaceTintColor: Colors.white,
          child: StatefulBuilder(
            builder: (context, setState2) {
              return Container(
                constraints: const BoxConstraints(maxWidth: 800),
                decoration: const BoxDecoration(),
                padding: const EdgeInsets.all(20),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Booking Details',
                      style: GoogleFonts.zcoolXiaoWei(
                        fontSize: 23,
                        color: themeColor,
                      ),
                    ),
                    const SizedBox(height: 20),
                    const SizedBox(height: 20),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TextButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                          child: const Text('Cancel'),
                        ),
                        const SizedBox(width: 15),
                        ElevatedButton(
                          onPressed: () {},
                          child: const Text('Save'),
                        ),
                      ],
                    ),
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }
}

class BookingTile extends StatefulWidget {
  const BookingTile({
    super.key,
    required this.isLast,
    required this.bookingData,
    required this.consultant,
    required this.styler,
    required this.index,
  });

  final bool isLast;
  final int index;
  final BookingsModel bookingData;
  final TeamModel? consultant;
  final TeamModel? styler;

  @override
  State<BookingTile> createState() => _BookingTileState();
}

class _BookingTileState extends State<BookingTile> {
  // String userName = '-';

  @override
  void initState() {
    super.initState();
    // getUserName();
  }

  // getUserName() async {
  //   final res = await FBFireStore.users.doc(widget.bookingData.uId).get();
  //   userName = res.data()?['name'] ?? '-';
  //   setState(() {});
  // }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: const Border(
          bottom: BorderSide(color: dividerColor),
          left: BorderSide(color: dividerColor),
          right: BorderSide(color: dividerColor),
        ),
        borderRadius: widget.isLast
            ? const BorderRadius.only(
                bottomLeft: Radius.circular(8),
                bottomRight: Radius.circular(8),
              )
            : null,
      ),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              (widget.index + 1).toString(),
              textAlign: TextAlign.center,
            ),
          ),

          const SizedBox(width: 5),
          Expanded(child: Text(widget.bookingData.bookingDate.goodDayDate())),

          const SizedBox(width: 5),
          Expanded(
            child: Text(
              '${widget.bookingData.bookingstartTime.goodTime()} - ${widget.bookingData.bookingendTime.goodTime()}, ${widget.bookingData.slotgap}',
            ),
          ),
          const SizedBox(width: 5),
          const SizedBox(width: 5),
          Expanded(
            child: Text(
              capilatlizeFirstLetter(
                widget.bookingData.fullnameAtBooking ?? '-',
              ),
            ),
          ),
          Expanded(
            child: Text(capilatlizeFirstLetter(widget.consultant?.name ?? '-')),
          ),
          const SizedBox(width: 5),
          Expanded(
            child: Text(capilatlizeFirstLetter(widget.styler?.name ?? '-')),
          ),

          const SizedBox(width: 5),
          Expanded(
            child: Text(
              widget.bookingData.cancelledAt != null
                  ? 'Cancelled'
                  : widget.bookingData.completedAt != null
                      ? 'Completed'
                      : 'Pending',
            ),
          ),
          const SizedBox(width: 5),
          Expanded(
            child: Text(
              '${widget.bookingData.createdAt.goodDayDate()}  ${widget.bookingData.createdAt.goodTime()}',
            ),
          ),
          const SizedBox(width: 5),

          // const SizedBox(width: 5),
          // Expanded(
          //     child: Text(inquiry.endedOn != null
          //         ? '${inquiry.endedOn?.goodDayDate()}  ${inquiry.endedOn?.goodTime()}'
          //         : '-')),
          Opacity(
            opacity: 0,
            child: IgnorePointer(
              ignoring: true,
              child: SizedBox(
                width: 60,
                child: IconButton(
                  highlightColor: Colors.transparent,
                  hoverColor: Colors.transparent,
                  onPressed: null,
                  icon: const Icon(Icons.edit, size: 22),
                ),
              ),
            ),
          ),
          // SizedBox(
          //   width: 60,
          //   child: IconButton(
          //     highlightColor: Colors.transparent,
          //     hoverColor: Colors.transparent,
          //     onPressed: () {},
          //     icon: const Icon(
          //       Icons.delete,
          //       color: themeColor,
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }
}
