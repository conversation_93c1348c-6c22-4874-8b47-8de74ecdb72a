import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wedding_super_admin/models/address_model.dart';
import 'package:wedding_super_admin/models/order_model.dart';
import 'package:wedding_super_admin/models/product.dart';
import 'package:wedding_super_admin/models/session_model.dart';
import 'package:wedding_super_admin/models/teammodel.dart';
import 'package:wedding_super_admin/models/user_model.dart';
import 'package:wedding_super_admin/models/variants.dart';
import 'package:wedding_super_admin/models/vendor.dart';
import 'package:wedding_super_admin/shared/const.dart';
import 'package:wedding_super_admin/shared/firebase.dart';
import 'package:wedding_super_admin/shared/methods.dart';
import 'package:wedding_super_admin/views/generate_pdf.dart';
import '../../../models/display_order_details_model.dart';
import '../../../shared/router.dart';
import '../../../shared/theme.dart';
import 'extracted_widget.dart';

class OrderDetailPage extends StatefulWidget {
  const OrderDetailPage({
    super.key,
    required this.orderDocId,
    // required this.orderModel
  });
  final String orderDocId;
  // final OrderModel orderModel;
  @override
  State<OrderDetailPage> createState() => _OrdeDetailrPageState();
}

class _OrdeDetailrPageState extends State<OrderDetailPage> {
  bool onSubmitLoad = false;
  bool dataLoaded = false;
  List<ChargeModel> charges = [];
  String? currentOrderStatus;
  bool isPaid = false;
  bool isLoading = false;
  SessionModel? sessionModel;
  UserModel? userModel;
  TeamModel? createdByModel;
  TeamModel? statusUpdatedByModel;
  TeamModel? updatedByModel;
  OrderModel? orderModel;
  // List<ProductModel> products = [];
  List<DisplayProductsDetails> allOrderedProductDetails = [];

  List<TransactionModel> transactionList = [];
  List<DeliveryDetailsModel> allDeliveryDetails = [];
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? transactionStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>?
      deliveryDetailsStream;
  StreamSubscription<DocumentSnapshot<Map<String, dynamic>>>? orderStream;

  bool edit = false;
  @override
  void initState() {
    super.initState();
    getOrderStream();

    // getRequiredData();
  }

  @override
  void dispose() {
    super.dispose();
    orderStream?.cancel();

    transactionStream?.cancel();
    deliveryDetailsStream?.cancel();
  }

  getTransactionData() async {
    transactionList.clear();
    transactionStream = FBFireStore.transaction
        .where('orderId', isEqualTo: orderModel?.orderId)
        .snapshots()
        .listen((event) {
      transactionList.clear();
      transactionList
          .addAll(event.docs.map((e) => TransactionModel.fromSnap(e)).toList());
      transactionList.sort((a, b) => a.createdAt.compareTo(b.createdAt));
      if (mounted) setState(() {});
    });
  }

  getDeliveryDetails() async {
    allDeliveryDetails.clear();
    deliveryDetailsStream = FBFireStore.deliveryDetails
        .where('orderId', isEqualTo: orderModel?.orderId)
        .snapshots()
        .listen((event) {
      allDeliveryDetails.clear();
      allDeliveryDetails.addAll(
          event.docs.map((e) => DeliveryDetailsModel.fromSnap(e)).toList());
      allDeliveryDetails.sort((a, b) => a.createdAt.compareTo(b.createdAt));
      if (mounted) setState(() {});
    });
  }

  getOrderStream() async {
    orderStream?.cancel();
    orderStream =
        FBFireStore.orders.doc(widget.orderDocId).snapshots().listen((event) {
      orderModel = OrderModel.fromDocSnap(event);
      getRequiredData();
    });
  }

  getRequiredData() async {
    // dataLoaded = false;
    // setState(() {});
    if (orderModel != null) {
      currentOrderStatus = orderModel?.orderStatus;
      charges.clear();
      charges.addAll(orderModel!.charges);
      try {
        final userSnap = await FBFireStore.users.doc(orderModel?.uid).get();

        userModel = UserModel.fromDocSnap(userSnap);
        print('User Fetched');
        if (!orderModel!.isDirectOrder) {
          final createdByTeamSnap = await FBFireStore.teamMember
              .doc(orderModel?.createdByDocId)
              .get();

          createdByModel = TeamModel.fromDocSnap(createdByTeamSnap);
        }

        if (orderModel?.isDirectOrder == false) {
          final statusupdatedByTeamSnap = await FBFireStore.teamMember
              .doc(orderModel?.statusUpdatedBy)
              .get();
          statusUpdatedByModel = orderModel?.statusUpdatedBy != null
              ? TeamModel.fromDocSnap(statusupdatedByTeamSnap)
              : null;

          final sessionSnap =
              await FBFireStore.sessions.doc(orderModel?.sessionId).get();
          sessionModel = sessionSnap.data() != null
              ? SessionModel.fromSnap(sessionSnap)
              : null;
        }

        allOrderedProductDetails.clear();
        for (var orderData in orderModel!.orderProductData) {
          final productSnap =
              await FBFireStore.products.doc(orderData.productId).get();
          print('Product Fetched');
          final productData = ProductModel.fromDocSnap(productSnap);

          final variantSnap =
              await FBFireStore.variants.doc(orderData.variantId).get();
          print("Variant Fetched");
          final variantData = NewVariantModel.fromDocSnap(variantSnap);

          final vendorSnap =
              await FBFireStore.vendors.doc(productData.vendorDocId).get();
          print("Vendor Fetched");
          final vendorData = VendorModel.fromDocSnap(vendorSnap);
          print("All DataFetched");
          allOrderedProductDetails.add(DisplayProductsDetails(
              product: productData,
              variant: variantData,
              vendorData: vendorData,
              orderData: orderData));
        }
        await getTransactionData();
        await getDeliveryDetails();
        dataLoaded = true;
        setState(() {});
      } on Exception catch (e) {
        dataLoaded = true;
        setState(() {});
        debugPrint(e.toString());
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return !dataLoaded
        ? const Center(child: CircularProgressIndicator())
        : orderModel != null
            ? SingleChildScrollView(
                padding:
                    const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Row(
                          children: [
                            IconButton(
                              onPressed: () {
                                context.pop();
                              },
                              icon: const Icon(
                                CupertinoIcons.arrow_left,
                                color: themeColor,
                              ),
                            ),
                            const SizedBox(width: 10),
                            Text(
                              'Order Details',
                              style: GoogleFonts.zcoolXiaoWei(
                                fontSize: 35,
                                color: themeColor,
                              ),
                            ),
                          ],
                        ),
                        const Spacer(),
                        InkWell(
                          hoverColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          splashColor: Colors.transparent,
                          onTap: () {
                            if (orderModel != null && userModel != null) {
                              createOrderPdfAndPrint(
                                  orderModel!,
                                  allOrderedProductDetails,
                                  userModel!,
                                  charges);
                            }
                          },
                          child: Container(
                            height: 28,
                            width: 28,
                            padding: EdgeInsets.all(5),
                            decoration: BoxDecoration(
                              color: themeColor,
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              CupertinoIcons.arrow_down,
                              size: 18,
                              color: Colors.white,
                            ),
                          ),
                        ),
                        const SizedBox(width: 10),
                        if (orderModel!.orderStatus != OrderStatus.cancelled)
                          if (!orderModel!.isPaid)
                            if (!edit) ...[
                              OutlinedButton(
                                  style: OutlinedButton.styleFrom(
                                    foregroundColor: themeColor,
                                    elevation: 0,
                                    backgroundColor: Colors.transparent,
                                    side: const BorderSide(color: themeColor),
                                    shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(4)),
                                  ),
                                  onPressed: () {
                                    edit = true;
                                    setState(() {});
                                  },
                                  child: Text("Edit",
                                      style: GoogleFonts.livvic(
                                          letterSpacing: 1.3,
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500))),
                              const SizedBox(width: 10),
                            ],
                        if (orderModel!.orderStatus != OrderStatus.cancelled)
                          onSubmitLoad
                              ? const Center(
                                  child: SizedBox(
                                      height: 25,
                                      width: 25,
                                      child: CircularProgressIndicator(
                                          strokeWidth: 2.5, color: themeColor)),
                                )
                              : ElevatedButton.icon(
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: themeColor,
                                    elevation: 0,
                                    foregroundColor: Colors.white,
                                    shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(4)),
                                  ),
                                  onPressed: onSave,
                                  icon: const Icon(
                                    CupertinoIcons.checkmark_alt,
                                    size: 20,
                                  ),
                                  label: Padding(
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 8.0),
                                    child: Text("Save",
                                        style: GoogleFonts.livvic(
                                            letterSpacing: 1.3,
                                            fontSize: 14,
                                            fontWeight: FontWeight.w500)),
                                  ),
                                ),
                      ],
                    ),
                    const SizedBox(height: 20),

                    GeneralOrderDetails(
                      orderModel: orderModel!,
                      changeOrderStatus: () async {
                        showDialog(
                          context: context,
                          builder: (context) {
                            return AlertDialog(
                              backgroundColor: Colors.white,
                              surfaceTintColor: Colors.white,
                              title: Text(
                                "Change Status",
                                style: GoogleFonts.zcoolXiaoWei(fontSize: 20),
                              ),
                              content: DropdownButtonHideUnderline(
                                child: Theme(
                                  data: Theme.of(context).copyWith(
                                    focusColor: Colors.grey.shade200,
                                  ),
                                  child: DropdownButtonFormField(
                                    dropdownColor: Colors.white,
                                    focusColor: Colors.transparent,
                                    decoration: inpDecor().copyWith(),
                                    value: currentOrderStatus,
                                    items: [
                                      if (!orderModel!.isPaid)
                                        const DropdownMenuItem(
                                          value: OrderStatus.paymentPending,
                                          child:
                                              Text(OrderStatus.paymentPending),
                                        ),
                                      const DropdownMenuItem(
                                        value: OrderStatus.processing,
                                        child: Text(
                                          OrderStatus.processing,
                                          style: TextStyle(
                                              fontWeight: FontWeight.w400,
                                              letterSpacing: .7,
                                              color: Color.fromARGB(
                                                  255, 255, 174, 52)),
                                        ),
                                      ),
                                      const DropdownMenuItem(
                                        value: OrderStatus.orderCompleted,
                                        child: Text(
                                          OrderStatus.orderCompleted,
                                          style: TextStyle(
                                              fontWeight: FontWeight.w400,
                                              letterSpacing: .7,
                                              color: Color.fromARGB(
                                                  255, 111, 190, 114)),
                                        ),
                                      ),
                                      const DropdownMenuItem(
                                        value: OrderStatus.cancelled,
                                        child: Text(
                                          OrderStatus.cancelled,
                                          style: TextStyle(
                                              fontWeight: FontWeight.w400,
                                              letterSpacing: .7,
                                              color: Color.fromARGB(
                                                  255, 242, 85, 85)),
                                        ),
                                      ),
                                    ],
                                    onChanged: (value) {
                                      currentOrderStatus = value;
                                      Navigator.of(context).pop();
                                      setState(() {});
                                    },
                                  ),
                                ),
                              ),
                              actions: [
                                TextButton(
                                    onPressed: () {
                                      Navigator.of(context).pop();
                                    },
                                    child: const Text("Cancel")),
                              ],
                            );
                          },
                        );
                      },
                      sessionModel: sessionModel,
                      createdByModel: createdByModel,
                      currentOrderStatus: currentOrderStatus,
                      statusUpdatedByModel: statusUpdatedByModel,
                    ),

                    const SizedBox(height: 20),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // USER DETAILS CONTAINER
                        UserDetailsContainer(
                            userModel: userModel, orderModel: orderModel!),

                        const SizedBox(width: 20),

                        // CHARGES CONTAINER
                        Expanded(
                          child: IgnorePointer(
                            ignoring: !edit,
                            child: Container(
                              padding: const EdgeInsets.all(18),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                    color: const Color.fromARGB(
                                        255, 225, 225, 225)),
                              ),
                              // padding: const EdgeInsets.symmetric(
                              //     vertical: 17, horizontal: 17),
                              // decoration: BoxDecoration(
                              //     color: Colors.white,
                              //     borderRadius: BorderRadius.circular(7),
                              //     boxShadow: const [
                              //       BoxShadow(
                              //         color: Color.fromARGB(12, 0, 0, 0),
                              //         blurRadius: 2,
                              //         offset: Offset(1, 2),
                              //       )
                              //     ]),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Container(
                                    width: double.maxFinite,
                                    decoration: BoxDecoration(
                                        // color: tableHeaderColor,
                                        borderRadius: BorderRadius.circular(5)),
                                    child: Row(
                                      children: [
                                        Text(
                                          'Charges',
                                          style: GoogleFonts.zcoolXiaoWei(
                                              fontSize: 18,
                                              color: themeColor,
                                              fontWeight: FontWeight.w500),
                                        ),
                                        const Spacer(),
                                        if (edit)
                                          InkWell(
                                              hoverColor: Colors.transparent,
                                              splashColor: Colors.transparent,
                                              highlightColor:
                                                  Colors.transparent,
                                              onTap: () async {
                                                final chargeName =
                                                    TextEditingController();
                                                final chargePrice =
                                                    TextEditingController();
                                                showDialog(
                                                  context: context,
                                                  builder: (context) {
                                                    bool isLoading = false;
                                                    return StatefulBuilder(
                                                        builder: (context,
                                                            setState2) {
                                                      return AlertDialog(
                                                        backgroundColor:
                                                            Colors.white,
                                                        surfaceTintColor:
                                                            Colors.white,
                                                        title: const Text(
                                                            'Charge'),
                                                        content: Column(
                                                          mainAxisSize:
                                                              MainAxisSize.min,
                                                          children: [
                                                            TextFormField(
                                                              controller:
                                                                  chargeName,
                                                              decoration: inpDecor()
                                                                  .copyWith(
                                                                      labelText:
                                                                          'Charge Name'),
                                                            ),
                                                            const SizedBox(
                                                                height: 10),
                                                            TextFormField(
                                                              controller:
                                                                  chargePrice,
                                                              decoration: inpDecor()
                                                                  .copyWith(
                                                                      labelText:
                                                                          'Charge Price'),
                                                            ),
                                                          ],
                                                        ),
                                                        actions: isLoading
                                                            ? [
                                                                const SizedBox(
                                                                  height: 28,
                                                                  width: 28,
                                                                  child: Center(
                                                                    child:
                                                                        CircularProgressIndicator(),
                                                                  ),
                                                                )
                                                              ]
                                                            : [
                                                                TextButton(
                                                                  onPressed:
                                                                      () {
                                                                    Navigator.of(
                                                                            context)
                                                                        .pop();
                                                                  },
                                                                  child: const Text(
                                                                      'Cancel'),
                                                                ),
                                                                ElevatedButton(
                                                                  onPressed:
                                                                      () {
                                                                    if (isLoading) {
                                                                      return;
                                                                    }

                                                                    isLoading =
                                                                        true;
                                                                    setState2(
                                                                        () {});
                                                                    charges.add(ChargeModel(
                                                                        chargeName:
                                                                            chargeName
                                                                                .text,
                                                                        price: chargePrice
                                                                            .text));
                                                                    isLoading =
                                                                        false;
                                                                    setState2(
                                                                        () {});
                                                                    Navigator.of(
                                                                            context)
                                                                        .pop();
                                                                    setState(
                                                                        () {});
                                                                  },
                                                                  child:
                                                                      const Text(
                                                                          'Add'),
                                                                ),
                                                              ],
                                                      );
                                                    });
                                                  },
                                                );
                                              },
                                              child: Container(
                                                padding: EdgeInsets.symmetric(
                                                    horizontal: 8, vertical: 5),
                                                decoration: BoxDecoration(
                                                  border: Border.all(
                                                      color: Color(0xffe2e8f0)),
                                                  borderRadius:
                                                      BorderRadius.circular(4),
                                                ),
                                                child: Row(
                                                  children: [
                                                    const Icon(
                                                        CupertinoIcons.add,
                                                        size: 18),
                                                    SizedBox(width: 4),
                                                    Text(
                                                      "Charge",
                                                      style: TextStyle(
                                                          fontSize: 13.5,
                                                          letterSpacing: 1,
                                                          fontWeight:
                                                              FontWeight.w500),
                                                    )
                                                  ],
                                                ),
                                              ))
                                      ],
                                    ),
                                  ),
                                  // const Divider(
                                  //     color: Color.fromARGB(35, 0, 0, 0),
                                  //     thickness: .3),
                                  const SizedBox(height: 18),
                                  ...List.generate(
                                    charges.length,
                                    (index) {
                                      final charge = charges[index];
                                      // final variantProduct = products.firstWhereOrNull(
                                      //     (element) => element.docId == variant.productId);
                                      return Padding(
                                        padding: EdgeInsets.only(
                                            top: index == 0 ? 0 : 12.0),
                                        child: Row(
                                          children: [
                                            Text(
                                              capilatlizeFirstLetter(
                                                  charge.chargeName),
                                              style: GoogleFonts.livvic(
                                                  fontSize: 14),
                                            ),
                                            const Spacer(),
                                            Text.rich(
                                              TextSpan(
                                                children: [
                                                  const TextSpan(
                                                      text: '\$',
                                                      style: TextStyle(
                                                          fontSize: 13,
                                                          fontWeight:
                                                              FontWeight.w500)),
                                                  TextSpan(
                                                      text: charge.price,
                                                      style: const TextStyle(
                                                          fontWeight:
                                                              FontWeight.w500,
                                                          fontSize: 15.5)),
                                                ],
                                              ),
                                            ),
                                            if (edit) ...[
                                              const SizedBox(width: 12),
                                              InkWell(
                                                hoverColor: Colors.transparent,
                                                highlightColor:
                                                    Colors.transparent,
                                                onTap: () {
                                                  charges.removeAt(index);
                                                  setState(() {});
                                                },
                                                child: const Icon(
                                                  CupertinoIcons.delete,
                                                  size: 18,
                                                ),
                                              ),
                                            ]
                                          ],
                                        ),
                                      );
                                    },
                                  ),
                                  const SizedBox(height: 8),
                                  Divider(
                                      height: 0, color: Colors.grey.shade100),
                                  const SizedBox(height: 8),
                                  Row(
                                    children: [
                                      Text(
                                        capilatlizeFirstLetter('Total Charge'),
                                        style: const TextStyle(
                                            fontSize: 14,
                                            letterSpacing: 1.2,
                                            fontWeight: FontWeight.bold),
                                      ),
                                      const Spacer(),
                                      Text.rich(
                                        TextSpan(
                                          children: [
                                            const TextSpan(
                                                text: '\$',
                                                style: TextStyle(
                                                    fontSize: 13,
                                                    fontWeight:
                                                        FontWeight.w600)),
                                            TextSpan(
                                              text: formatPriceWithCommas(
                                                  charges.fold(
                                                      0,
                                                      (previousValue,
                                                              element) =>
                                                          previousValue +
                                                          (int.tryParse(element
                                                                  .price) ??
                                                              0))),
                                              style: const TextStyle(
                                                  fontWeight: FontWeight.w600,
                                                  fontSize: 15.5),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    if (orderModel?.userConfirmed ?? false) ...[
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            padding: const EdgeInsets.all(18),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                    color: const Color.fromARGB(
                                        255, 225, 225, 225))),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  width: double.maxFinite,
                                  decoration: BoxDecoration(
                                      // color: tableHeaderColor,
                                      borderRadius: BorderRadius.circular(5)),
                                  child: Row(
                                    children: [
                                      Text(
                                        'Transaction',
                                        style: GoogleFonts.zcoolXiaoWei(
                                            fontSize: 18,
                                            color: themeColor,
                                            fontWeight: FontWeight.w500),
                                      ),
                                      const Spacer(),
                                      if (!(orderModel?.isDirectOrder ?? false))
                                        InkWell(
                                          hoverColor: Colors.transparent,
                                          splashColor: Colors.transparent,
                                          highlightColor: Colors.transparent,
                                          onTap: () async {
                                            addEditTransactionDialog(
                                                context, null);
                                          },
                                          child: Container(
                                            padding: EdgeInsets.symmetric(
                                                horizontal: 8, vertical: 5),
                                            decoration: BoxDecoration(
                                              border: Border.all(
                                                  color: Color(0xffe2e8f0)),
                                              borderRadius:
                                                  BorderRadius.circular(4),
                                            ),
                                            child: Row(
                                              children: [
                                                const Icon(CupertinoIcons.add,
                                                    size: 18),
                                                SizedBox(width: 4),
                                                Text(
                                                  "Transaction",
                                                  style: TextStyle(
                                                      fontSize: 13.5,
                                                      letterSpacing: 1,
                                                      fontWeight:
                                                          FontWeight.w500),
                                                )
                                              ],
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                ),
                                // const Divider(
                                //     color: Color.fromARGB(35, 0, 0, 0),
                                //     thickness: .3),
                                if (transactionList.isNotEmpty)
                                  const SizedBox(height: 18),

                                ...List.generate(
                                  transactionList.length,
                                  (index) {
                                    final transactionData =
                                        transactionList[index];
                                    // final variantProduct = products.firstWhereOrNull(
                                    //     (element) => element.docId == variant.productId);
                                    return TransactionTile(
                                      index: index,
                                      transactionData: transactionData,
                                      onTap: orderModel?.isDirectOrder ?? false
                                          ? null
                                          : () async {
                                              addEditTransactionDialog(
                                                  context, transactionData);
                                            },
                                    );
                                  },
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 20),
                          Container(
                            padding: const EdgeInsets.all(18),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                    color: const Color.fromARGB(
                                        255, 225, 225, 225))),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  width: double.maxFinite,
                                  decoration: BoxDecoration(
                                      // color: tableHeaderColor,
                                      borderRadius: BorderRadius.circular(5)),
                                  child: Row(
                                    children: [
                                      Text(
                                        'Delivery Details',
                                        style: GoogleFonts.zcoolXiaoWei(
                                            fontSize: 18,
                                            color: themeColor,
                                            fontWeight: FontWeight.w500),
                                      ),
                                      const Spacer(),
                                      InkWell(
                                        hoverColor: Colors.transparent,
                                        splashColor: Colors.transparent,
                                        highlightColor: Colors.transparent,
                                        onTap: () async {
                                          await addEditDeliveryDetailsDialog(
                                              context, null);
                                        },
                                        child: Container(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 8, vertical: 5),
                                          decoration: BoxDecoration(
                                            border: Border.all(
                                                color: Color(0xffe2e8f0)),
                                            borderRadius:
                                                BorderRadius.circular(4),
                                          ),
                                          child: Row(
                                            children: [
                                              const Icon(CupertinoIcons.add,
                                                  size: 18),
                                              SizedBox(width: 4),
                                              Text(
                                                "Delivery",
                                                style: TextStyle(
                                                    fontSize: 13.5,
                                                    letterSpacing: 1,
                                                    fontWeight:
                                                        FontWeight.w500),
                                              )
                                            ],
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                // const Divider(
                                //   color: Color.fromARGB(35, 0, 0, 0),
                                //   thickness: .3,
                                // ),
                                if (allDeliveryDetails.isNotEmpty)
                                  const SizedBox(height: 18),
                                ...List.generate(
                                  allDeliveryDetails.length,
                                  (index) {
                                    final deliveryDetail =
                                        allDeliveryDetails[index];
                                    // final variantProduct = products.firstWhereOrNull(
                                    //     (element) => element.docId == variant.productId);
                                    return DeliveryDetailTile(
                                        index: index,
                                        onDelete: () {},
                                        onTap: () async {
                                          await addEditDeliveryDetailsDialog(
                                              context, deliveryDetail);
                                        },
                                        allOrderedProductsLength:
                                            allOrderedProductDetails.length,
                                        deliveryDetail: deliveryDetail);
                                  },
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                    ],
                    Container(
                      padding: const EdgeInsets.symmetric(vertical: 18),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                            color: const Color.fromARGB(255, 225, 225, 225)),
                      ),
                      // padding: const EdgeInsets.symmetric(
                      //     vertical: 17, horizontal: 17),
                      // decoration: BoxDecoration(
                      //     color: Colors.white,
                      //     borderRadius: BorderRadius.circular(7),
                      //     boxShadow: const [
                      //       BoxShadow(
                      //         color: Color.fromARGB(12, 0, 0, 0),
                      //         blurRadius: 2,
                      //         offset: Offset(1, 2),
                      //       )
                      //     ]),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 18),
                            width: double.maxFinite,
                            decoration: BoxDecoration(
                                // color: tableHeaderColor,
                                borderRadius: BorderRadius.circular(5)),
                            child: Row(
                              children: [
                                Text(
                                  'Products List',
                                  style: GoogleFonts.zcoolXiaoWei(
                                      fontSize: 18,
                                      color: themeColor,
                                      fontWeight: FontWeight.w500),
                                ),
                                const Spacer(),
                                if (edit) addNewProductButton(context)
                              ],
                            ),
                          ),
                          const SizedBox(height: 25),
                          Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 18.0),
                            child: Row(
                              children: [
                                const SizedBox(
                                    width: 55,
                                    child: Text(
                                      'Sr No',
                                      style: TextStyle(
                                        color: Color.fromARGB(255, 66, 72, 82),
                                        fontWeight: FontWeight.w500,
                                        letterSpacing: .5,
                                        wordSpacing: 1,
                                      ),
                                    )),
                                const SizedBox(width: 10),
                                SizedBox(
                                  width: 50,
                                  child: const Text(
                                    'Image',
                                    style: TextStyle(
                                      color: Color.fromARGB(255, 66, 72, 82),
                                      fontWeight: FontWeight.w500,
                                      letterSpacing: .5,
                                      wordSpacing: 1,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 15),
                                Expanded(
                                  child: Text(
                                    'Product Code',
                                    maxLines: 2,
                                    style: TextStyle(
                                      color: Color.fromARGB(255, 66, 72, 82),
                                      fontWeight: FontWeight.w500,
                                      letterSpacing: .5,
                                      wordSpacing: 1,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 15),
                                Expanded(
                                  child: Text(
                                    'Product Name',
                                    maxLines: 2,
                                    style: TextStyle(
                                      color: Color.fromARGB(255, 66, 72, 82),
                                      fontWeight: FontWeight.w500,
                                      letterSpacing: .5,
                                      wordSpacing: 1,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 15),
                                Expanded(
                                  child: Text(
                                    'Vendor Name',
                                    maxLines: 2,
                                    style: TextStyle(
                                      color: Color.fromARGB(255, 66, 72, 82),
                                      fontWeight: FontWeight.w500,
                                      letterSpacing: .5,
                                      wordSpacing: 1,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 15),
                                Expanded(
                                  child: Text(
                                    'Price (₹)',
                                    maxLines: 2,
                                    style: TextStyle(
                                      color: Color.fromARGB(255, 66, 72, 82),
                                      fontWeight: FontWeight.w500,
                                      letterSpacing: .5,
                                      wordSpacing: 1,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 15),
                                const Expanded(
                                    child: Text(
                                  'Selling Price (\$)',
                                  style: TextStyle(
                                    color: Color.fromARGB(255, 66, 72, 82),
                                    fontWeight: FontWeight.w500,
                                    letterSpacing: .5,
                                    wordSpacing: 1,
                                  ),
                                )),
                                const SizedBox(width: 15),
                                const Expanded(
                                    child: Text(
                                  'Ordered Qty',
                                  style: TextStyle(
                                    color: Color.fromARGB(255, 66, 72, 82),
                                    fontWeight: FontWeight.w500,
                                    letterSpacing: .5,
                                    wordSpacing: 1,
                                  ),
                                )),
                                const SizedBox(width: 15),
                                const Expanded(
                                    child: Text(
                                  'User Note',
                                  style: TextStyle(
                                    color: Color.fromARGB(255, 66, 72, 82),
                                    fontWeight: FontWeight.w500,
                                    letterSpacing: .5,
                                    wordSpacing: 1,
                                  ),
                                )),
                                const SizedBox(width: 15),
                                const Expanded(
                                    child: Text(
                                  'Product Status',
                                  style: TextStyle(
                                    color: Color.fromARGB(255, 66, 72, 82),
                                    fontWeight: FontWeight.w500,
                                    letterSpacing: .5,
                                    wordSpacing: 1,
                                  ),
                                )),
                                const SizedBox(width: 15),
                                const Expanded(
                                    child: Text(
                                  'Admin Note',
                                  style: TextStyle(
                                    color: Color.fromARGB(255, 66, 72, 82),
                                    fontWeight: FontWeight.w500,
                                    letterSpacing: .5,
                                    wordSpacing: 1,
                                  ),
                                )),
                                const SizedBox(width: 15),
                                const Expanded(
                                    child: Text(
                                  'Delivery Date',
                                  style: TextStyle(
                                    color: Color.fromARGB(255, 66, 72, 82),
                                    fontWeight: FontWeight.w500,
                                    letterSpacing: .5,
                                    wordSpacing: 1,
                                  ),
                                )),
                                if (edit) ...[
                                  const SizedBox(width: 12),
                                  Opacity(
                                    opacity: 0,
                                    child: IgnorePointer(
                                      ignoring: true,
                                      child: IconButton(
                                        onPressed: () {},
                                        icon: const Icon(
                                          CupertinoIcons.delete,
                                          size: 18,
                                        ),
                                      ),
                                    ),
                                  ),
                                ]
                              ],
                            ),
                          ),
                          const SizedBox(height: 18),
                          const Divider(
                              height: 0,
                              color: Color.fromARGB(255, 225, 225, 225)),
                          // const SizedBox(height: 18),
                          ...List.generate(
                            allOrderedProductDetails.length,
                            (index) {
                              final displayOrderDt =
                                  allOrderedProductDetails[index];
                              // final variantProduct = products.firstWhereOrNull(
                              //     (element) => element.docId == variant.productId);
                              return MainScreenOrderProductTile(
                                onAdminNoteChange: (value) {
                                  displayOrderDt.orderData.adminNote = value;
                                },
                                onDateChange: () async {
                                  final res = await showDatePicker(
                                      context: context,
                                      firstDate: DateTime(2000),
                                      lastDate: DateTime.now());
                                  displayOrderDt.orderData.deliveryDate = res;
                                },
                                onDelete: () {
                                  allOrderedProductDetails.removeAt(index);
                                  setState(() {});
                                },
                                onQtyChange: (value) {
                                  displayOrderDt.orderData.qty =
                                      int.tryParse(value) ??
                                          displayOrderDt.orderData.qty;
                                },
                                onSellingPriceChange: (value) {
                                  displayOrderDt.orderData.sellingPrice =
                                      num.tryParse(value) ??
                                          displayOrderDt.orderData.sellingPrice;
                                },
                                index: index,
                                displayOrderData: displayOrderDt,
                                wanttoEdit: edit,
                              );
                            },
                          ),
                          const SizedBox(height: 18),
                          // const Divider(
                          //     height: 0,
                          //     color: Color.fromARGB(255, 225, 225, 225)),
                          // const SizedBox(height: 18),
                          Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 18.0),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                Text.rich(
                                  TextSpan(children: [
                                    const TextSpan(
                                        text: 'Vendor sub total:  ',
                                        style: TextStyle(
                                            fontWeight: FontWeight.w500)),
                                    const TextSpan(
                                        text: ' ₹ ',
                                        style: TextStyle(
                                            fontSize: 12.2,
                                            fontWeight: FontWeight.w600)),
                                    TextSpan(
                                        text: formatPriceWithCommas(
                                            allOrderedProductDetails.map((e) {
                                          return (e.orderData.qty *
                                              e.orderData.purchasePrice);
                                        }).fold(
                                                0,
                                                (previousValue, element) =>
                                                    previousValue +
                                                    element.toInt())),
                                        style: const TextStyle(
                                            fontWeight: FontWeight.w600)),
                                  ]),
                                ),
                                SizedBox(width: 25),
                                Text.rich(
                                  TextSpan(children: [
                                    const TextSpan(
                                        text: 'Sub total:  ',
                                        style: TextStyle(
                                            fontWeight: FontWeight.w500)),
                                    const TextSpan(
                                        text: ' \$',
                                        style: TextStyle(
                                            fontSize: 12.2,
                                            fontWeight: FontWeight.w600)),
                                    TextSpan(
                                        text: formatPriceWithCommas(
                                            allOrderedProductDetails
                                                .map((e) => (e.orderData.qty *
                                                    e.orderData.sellingPrice))
                                                .fold(
                                                    0,
                                                    (previousValue, element) =>
                                                        previousValue +
                                                        element.toInt())),
                                        style: const TextStyle(
                                            fontWeight: FontWeight.w600)),
                                  ]),
                                ),
                              ],
                            ),
                          )
                        ],
                      ),
                    ),
                    // const SizedBox(height: 20),
                  ],
                ),
              )
            : const Center(child: Text('No Order Found'));
  }

  Future<dynamic> addEditDeliveryDetailsDialog(
      BuildContext context, DeliveryDetailsModel? deliveryDetails) {
    final deliveryPartnerCtrl = TextEditingController();
    final trackingIdCtrl = TextEditingController();
    final trackingLinkCtrl = TextEditingController();
    final chargeCtrl = TextEditingController();
    // final locationCtrl = TextEditingController();
    DateTime? dispatchedOn;
    DateTime? deliveredOn;
    bool isDelivered = false;
    List<DeliveryProductData> selectedDeliveryProducts = [];
    List<AddressModel> userAddress = userModel?.addresses ?? [];
    AddressModel? selectedAddress;
    if (deliveryDetails != null) {
      deliveryPartnerCtrl.text = deliveryDetails.deliveryPartner;
      trackingIdCtrl.text = deliveryDetails.trackingId;
      trackingLinkCtrl.text = deliveryDetails.trackingLink;
      chargeCtrl.text = deliveryDetails.charges.toString();
      // locationCtrl.text = deliveryDetails.location;

      dispatchedOn = deliveryDetails.dispatchedOn;
      deliveredOn = deliveryDetails.deliveredOn;
      isDelivered = deliveryDetails.isDelivered;
      selectedDeliveryProducts.clear();
      selectedDeliveryProducts.addAll(deliveryDetails.dispatchedProduct);
      selectedAddress = deliveryDetails.userAddress;
      if (!(userAddress.map((e) => e.id).contains(selectedAddress?.id))) {
        userAddress.add(selectedAddress!);
      }
    }

    return showDialog(
      context: context,
      builder: (context) {
        bool isLoading = false;
        bool qtyExceed = false;
        return StatefulBuilder(builder: (context, setState2) {
          return AlertDialog(
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            title: Text('Delivery Details',
                style:
                    GoogleFonts.zcoolXiaoWei(fontSize: 25, color: themeColor)),
            content: Container(
              constraints: const BoxConstraints(maxWidth: 800),
              width: double.maxFinite,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          controller: deliveryPartnerCtrl,
                          decoration: inpDecor()
                              .copyWith(labelText: 'Delivery Partner'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: DropdownButtonHideUnderline(
                            child: DropdownButtonFormField(
                          decoration: inpDecor().copyWith(labelText: 'Address'),
                          items: List.generate(
                            userAddress.length,
                            (index) {
                              final adddress = userAddress[index];
                              return DropdownMenuItem(
                                value: adddress,
                                child: Text(
                                  '${adddress.flatHouse}, ${adddress.area}, ${adddress.city}, ${adddress.state}, ${adddress.pincode}',
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              );
                            },
                          ),
                          onChanged: (value) {
                            selectedAddress = value;
                            setState2(() {});
                          },
                        )),
                      ),
                    ],
                  ),
                  const SizedBox(height: 15),
                  Row(
                    children: [
                      Expanded(
                        child: InkWell(
                          onTap: () async {
                            final res = await showDatePicker(
                                context: context,
                                firstDate: DateTime(DateTime.now().year,
                                    DateTime.now().month - 1),
                                lastDate: DateTime.now());
                            dispatchedOn = res;
                            setState2(() {});
                          },
                          child: TextFormField(
                            enabled: false,
                            controller: TextEditingController(
                                text: dispatchedOn != null
                                    ? dispatchedOn!.goodDayDate()
                                    : '-'),
                            style: const TextStyle(color: Color(0xff3E3E3E)),
                            decoration: inpDecor().copyWith(
                                labelStyle:
                                    const TextStyle(color: Colors.black),
                                labelText: 'Dispatch Date',
                                suffixIcon:
                                    const Icon(CupertinoIcons.calendar)),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: TextFormField(
                          controller: chargeCtrl,
                          decoration: inpDecor().copyWith(labelText: 'Charge'),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 15),
                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          controller: trackingIdCtrl,
                          decoration:
                              inpDecor().copyWith(labelText: 'Tracking Id'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: TextFormField(
                          controller: trackingLinkCtrl,
                          decoration:
                              inpDecor().copyWith(labelText: 'Tracking Link'),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 15),
                  if (deliveryDetails != null)
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            clipBehavior: Clip.antiAlias,
                            height: 48,
                            padding: const EdgeInsets.only(left: 3),
                            decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey.shade400),
                                borderRadius: BorderRadius.circular(7),
                                // color: const Color.fromARGB(9, 0, 0, 0),
                                color: Colors.transparent),
                            child: Row(
                              children: [
                                Checkbox(
                                  side: const BorderSide(color: Colors.grey),
                                  checkColor: Colors.white,
                                  activeColor: themeColor,
                                  hoverColor: Colors.transparent,
                                  // focusColor: Colors.transparent,
                                  overlayColor: const WidgetStatePropertyAll(
                                      Colors.transparent),
                                  value: isDelivered,
                                  onChanged: (value) async {
                                    isDelivered = value!;
                                    setState2(() {});
                                  },
                                ),
                                const SizedBox(width: 5),
                                const Text(
                                  "Delivered",
                                  style: TextStyle(color: Colors.black),
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: InkWell(
                            onTap: () async {
                              final res = await showDatePicker(
                                  context: context,
                                  firstDate: DateTime(DateTime.now().year,
                                      DateTime.now().month - 1),
                                  lastDate: DateTime.now());
                              deliveredOn = res;
                              setState2(() {});
                            },
                            child: TextFormField(
                              enabled: false,
                              controller: TextEditingController(
                                  text: deliveredOn != null
                                      ? deliveredOn!.goodDayDate()
                                      : '-'),
                              style: const TextStyle(color: Color(0xff3E3E3E)),
                              decoration: inpDecor().copyWith(
                                  labelStyle:
                                      const TextStyle(color: Colors.black),
                                  labelText: 'Delivered On',
                                  suffixIcon:
                                      const Icon(CupertinoIcons.calendar)),
                            ),
                          ),
                        ),
                      ],
                    ),
                  const SizedBox(height: 5),
                  SelectProductInDialogForDelivery(
                    refresh: () {
                      setState2(() {});
                    },
                    allDeliveryDetails: allDeliveryDetails,
                    allOrderedProducts: allOrderedProductDetails,
                    selectedDeliveryProducts: selectedDeliveryProducts,
                  ),
                  const SizedBox(height: 10),
                  Column(
                    children: [
                      const Row(
                        children: [
                          Text('Image'),
                          SizedBox(width: 15),
                          Expanded(
                            child: Text('Product Code'),
                          ),
                          SizedBox(width: 15),
                          Expanded(flex: 2, child: Text('Product Name')),
                          SizedBox(width: 15),
                          Expanded(child: Text('Vendor Name')),
                          SizedBox(width: 15),
                          Expanded(child: Text('Ordered Qty')),
                          SizedBox(width: 15),
                          Expanded(child: Text('Dispatch Qty')),
                          SizedBox(width: 15),
                          Expanded(child: Text('Status')),
                        ],
                      ),
                      const SizedBox(height: 8),
                      const Divider(height: 0),
                      ...List.generate(
                        selectedDeliveryProducts.length,
                        (index) {
                          final selectedProductId =
                              selectedDeliveryProducts[index].id;

                          final displayProductData = allOrderedProductDetails
                              .firstWhereOrNull((element) =>
                                  element.orderData.id == selectedProductId);

                          if (deliveryDetails == null) {
                            selectedDeliveryProducts[index].qty =
                                selectedDeliveryProducts[index].qty != 0
                                    ? selectedDeliveryProducts[index].qty
                                    : displayProductData?.orderData.qty ?? 0;

                            for (var deliveryDetail in allDeliveryDetails) {
                              for (var dispatched
                                  in deliveryDetail.dispatchedProduct) {
                                if (dispatched.id == selectedProductId) {
                                  selectedDeliveryProducts[index].qty -=
                                      dispatched.qty;
                                }
                              }
                            }
                          }

                          return Padding(
                            padding: const EdgeInsets.only(top: 8.0),
                            child: Row(
                              children: [
                                Container(
                                  height: 50,
                                  width: 50,
                                  clipBehavior: Clip.antiAlias,
                                  decoration: const BoxDecoration(
                                      shape: BoxShape.circle),
                                  child: Image.network(
                                    displayProductData?.variant.images.first ??
                                        "",
                                    fit: BoxFit.cover,
                                    errorBuilder: (context, error, stackTrace) {
                                      return const Icon(CupertinoIcons.photo);
                                    },
                                  ),
                                ),
                                const SizedBox(width: 15),
                                Expanded(
                                  child: Text(
                                      displayProductData?.product.sku ?? '-'),
                                ),
                                const SizedBox(width: 15),
                                Expanded(
                                    flex: 2,
                                    child: Text(capilatlizeFirstLetter(
                                        displayProductData?.product.name ??
                                            '-'))),
                                const SizedBox(width: 15),
                                Expanded(
                                    child: Text(capilatlizeFirstLetter(
                                        displayProductData?.vendorData.name ??
                                            '-'))),
                                const SizedBox(width: 15),
                                Expanded(
                                    child: Text(capilatlizeFirstLetter(
                                        displayProductData?.orderData.qty
                                                .toString() ??
                                            '-'))),
                                const SizedBox(width: 15),
                                Expanded(
                                  child: TextFormField(
                                    controller: TextEditingController(
                                        text: selectedDeliveryProducts[index]
                                            .qty
                                            .toString()),
                                    onChanged: (value) {
                                      final res = int.tryParse(value);
                                      int previousQty = 0;
                                      int currentDeliveryOldQty = 0;

                                      // CHECKING IF ORDERED QTY IS NOT MORE THAN PREVIOUS DELIVERED QTY + ENTERED QTY

                                      if (res != null) {
                                        selectedDeliveryProducts[index].qty =
                                            res;
                                        for (var element
                                            in allDeliveryDetails) {
                                          if (deliveryDetails != null &&
                                              element.docId ==
                                                  deliveryDetails.docId) {
                                            for (var product
                                                in element.dispatchedProduct) {
                                              if (product.productId ==
                                                      selectedDeliveryProducts[
                                                              index]
                                                          .productId &&
                                                  product.variantId ==
                                                      selectedDeliveryProducts[
                                                              index]
                                                          .variantId) {
                                                currentDeliveryOldQty =
                                                    product.qty;
                                              }
                                            }
                                          }
                                          for (var product
                                              in element.dispatchedProduct) {
                                            if (product.productId ==
                                                    selectedDeliveryProducts[
                                                            index]
                                                        .productId &&
                                                product.variantId ==
                                                    selectedDeliveryProducts[
                                                            index]
                                                        .variantId) {
                                              previousQty += product.qty;
                                            }
                                          }
                                        }
                                        int totalQty = previousQty +
                                            res -
                                            currentDeliveryOldQty;
                                        qtyExceed = totalQty >
                                            displayProductData!.orderData.qty;

                                        // print(
                                        //     'in textfeild after change: ${displayOrderDetails.firstWhereOrNull((element) => element.orderData.id == selectedDeliveryProducts[index].id)?.orderData.qty}');
                                      }
                                    },
                                  ),
                                ),
                                const SizedBox(width: 15),
                                Expanded(
                                  child: Text(
                                    capilatlizeFirstLetter(displayProductData
                                            ?.orderData.currentStatus ??
                                        '-'),
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      )
                    ],
                  )
                ],
              ),
            ),
            actions: isLoading
                ? [
                    const SizedBox(
                      height: 28,
                      width: 28,
                      child: Center(
                        child: CircularProgressIndicator(),
                      ),
                    )
                  ]
                : [
                    TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: const Text('Cancel'),
                    ),
                    ElevatedButton(
                      onPressed: () async {
                        if (deliveryPartnerCtrl.text.trim().isEmpty) {
                          showErrorAppSnackBar(context, 'Partner name empty');
                          return;
                        }
                        if (trackingLinkCtrl.text.trim().isEmpty) {
                          showErrorAppSnackBar(context, 'Tracking link empty');
                          return;
                        }
                        if (trackingIdCtrl.text.trim().isEmpty) {
                          showErrorAppSnackBar(context, 'Tracking id empty');
                          return;
                        }
                        if (selectedDeliveryProducts.isEmpty) {
                          showErrorAppSnackBar(context, 'No product selected');
                          return;
                        }
                        if (qtyExceed) {
                          showErrorAppSnackBar(
                              context, 'Delivery qty exceeded');
                          return;
                        }
                        try {
                          if (isLoading) return;

                          isLoading = true;
                          setState2(() {});

                          final isDirectOrder =
                              orderModel?.isDirectOrder ?? false;

                          final messageRef = isDirectOrder
                              ? null
                              : FBFireStore.sessions
                                  .doc(orderModel?.sessionId)
                                  .collection('messages')
                                  .doc();
                          final deliveryRef = deliveryDetails != null
                              ? FBFireStore.deliveryDetails
                                  .doc(deliveryDetails.docId)
                              : FBFireStore.deliveryDetails.doc();

                          final orderRef =
                              FBFireStore.orders.doc(widget.orderDocId);

                          final deliveryData = {
                            'deliveryPartner': deliveryPartnerCtrl.text.trim(),
                            'trackingId': trackingIdCtrl.text.trim(),
                            'trackingLink': trackingLinkCtrl.text.trim(),
                            'dispatchedOn':
                                dispatchedOn?.millisecondsSinceEpoch,
                            'dispatchedProduct':
                                selectedDeliveryProducts.map((e) {
                              int allDeliveryDetailsQty = 0;
                              for (var deliveryDetail in allDeliveryDetails) {
                                for (var element
                                    in deliveryDetail.dispatchedProduct) {
                                  if (element.id == e.id) {
                                    allDeliveryDetailsQty += element.qty;
                                  }
                                }
                              }
                              allDeliveryDetailsQty += e.qty;
                              final originalProductQty =
                                  allOrderedProductDetails
                                      .firstWhere((element) =>
                                          element.orderData.id == e.id)
                                      .orderData
                                      .qty;
                              if (deliveryDetails != null) {
                                allDeliveryDetailsQty -
                                    deliveryDetails.dispatchedProduct
                                        .firstWhere((element) =>
                                            element.variantId == e.variantId)
                                        .qty;
                              }
                              return e.toPartialDeliveredJson(
                                  allDeliveryDetailsQty < originalProductQty
                                      ? OrderProductStatus.partDeliver
                                      : isDelivered
                                          ? OrderProductStatus.delivered
                                          : OrderProductStatus.outfordeliver,
                                  e.qty);
                            }).toList(),
                            'teamMemberDocId': FBAuth.auth.currentUser?.uid,
                            'location': '',
                            'userAddress': selectedAddress?.toJson(),
                            'orderId': orderModel?.orderId,
                            'inquiryId':
                                isDirectOrder ? null : orderModel?.sessionId,
                            'messageId': isDirectOrder ? null : messageRef!.id,
                            'uId': orderModel?.uid,
                            'charges':
                                num.tryParse(chargeCtrl.text.trim()) ?? 0,
                            'createdAt': DateTime.now().millisecondsSinceEpoch,
                            'deliveredOn': deliveredOn?.millisecondsSinceEpoch,
                            'isDelivered': isDelivered,
                          };
                          final messageData = {
                            'type': MessageTypes.deliveryDetails,
                            'sessionId':
                                isDirectOrder ? null : orderModel?.sessionId,
                            'loadingText': 'delivery ',
                            'data': {
                              'deliveryDocId': deliveryRef.id,
                            },
                            'senderId': FBAuth.auth.currentUser?.uid,
                            'sendAt': DateTime.now().millisecondsSinceEpoch,
                          };

                          final batch = FBFireStore.fb.batch();

                          batch.update(orderRef, {
                            'orderProductData':
                                allOrderedProductDetails.map((e) {
                              int allDeliveryDetailsQty = 0;

                              for (var deliveryDetail in allDeliveryDetails) {
                                for (var element
                                    in deliveryDetail.dispatchedProduct) {
                                  if (element.id == e.orderData.id) {
                                    allDeliveryDetailsQty += element.qty;
                                  }
                                }
                              }

                              if (deliveryDetails != null) {
                                allDeliveryDetailsQty -= deliveryDetails
                                        .dispatchedProduct
                                        .firstWhereOrNull((element) {
                                      return (element.variantId ==
                                          e.orderData.variantId);
                                    })?.qty ??
                                    0;
                              }

                              // allDeliveryDetailsQty += e.qty;
                              return selectedDeliveryProducts
                                      .map((ele) => ele.id)
                                      .contains(e.orderData.id)
                                  ? e.orderData.toPartialDeliveredJson(
                                      (allDeliveryDetailsQty +=
                                                  selectedDeliveryProducts
                                                      .firstWhere((element) =>
                                                          element.id ==
                                                          e.orderData.id)
                                                      .qty) <
                                              e.orderData.qty
                                          ? OrderProductStatus.partDeliver
                                          : isDelivered
                                              ? OrderProductStatus.delivered
                                              : OrderProductStatus
                                                  .outfordeliver,
                                      e.orderData.qty)
                                  : e.orderData.toJson();
                            }),
                          });

                          batch.set(deliveryRef, deliveryData);
                          if (!isDirectOrder) {
                            batch.set(messageRef!, messageData);
                          }

                          await batch.commit();
                          isLoading = false;
                          setState2(() {});
                          Navigator.of(context).pop();
                        } on Exception catch (e) {
                          debugPrint(e.toString());
                          isLoading = false;
                          setState2(() {});
                        }
                      },
                      child: const Text('Save'),
                    ),
                  ],
          );
        });
      },
    );
  }

  onSave() async {
    if (onSubmitLoad) return;

    onSubmitLoad = true;
    setState(() {});
    try {
      num subTotal = 0;
      num totalPayableAmount = 0;
      num paidAmount = 0;

      for (var transaction in transactionList) {
        if (transaction.isPaid) {
          paidAmount += transaction.amount;
        }
      }

      List<OrderProductData> orderProductData =
          allOrderedProductDetails.map((e) => e.orderData).toList();
      isPaid = orderModel!.isPaid
          ? orderModel!.isPaid
          : currentOrderStatus == OrderStatus.processing;

      for (var product in orderProductData) {
        subTotal += (product.sellingPrice * product.qty);
      }
      totalPayableAmount = subTotal;
      final finalChargeMap = {};

      for (var charge in charges) {
        totalPayableAmount += (num.tryParse(charge.price) ?? 0);
        finalChargeMap.addEntries({MapEntry(charge.chargeName, charge.price)});
      }

      final orderStatus = orderModel?.orderStatus == OrderStatus.cancelled ||
              currentOrderStatus == OrderStatus.cancelled
          ? OrderStatus.cancelled
          : orderModel!.isPaid
              ? currentOrderStatus
              : isPaid
                  ? OrderStatus.processing
                  : OrderStatus.userConfirmation;

      final data = {
        'orderId': orderModel?.orderId,
        'createdAt': orderModel?.createdAt.millisecondsSinceEpoch,
        'createdByDocId': orderModel?.createdByDocId,
        'isDirectOrder': orderModel?.isDirectOrder,
        'uid': orderModel?.uid,
        'charges': finalChargeMap,
        'orderProductData': orderProductData.map((e) => e.toJson()).toList(),
        'userConfirmed': isPaid ? orderModel?.userConfirmed : null,
        'userConfirmedOn':
            isPaid ? orderModel?.userConfirmedOn?.millisecondsSinceEpoch : null,
        'isPaid': isPaid,
        'paidOn': orderModel!.isPaid
            ? orderModel?.paidOn?.millisecondsSinceEpoch
            : isPaid
                ? DateTime.now().millisecondsSinceEpoch
                : null,
        'subTotal': subTotal,
        'totalAmount': totalPayableAmount,
        'paidAmount': paidAmount,
        'rejectionReason': null,
        'address': orderModel?.address,
        'orderStatus': orderStatus,
        'statusUpdatedBy': FBAuth.auth.currentUser?.uid,
        'statusUpdatedAt': DateTime.now().millisecondsSinceEpoch,
        'sessionId': orderModel?.sessionId,
        'previousDocIds': FieldValue.arrayUnion([widget.orderDocId])
      };

      final orderRef = isPaid
          ? FBFireStore.orders.doc(orderModel?.docId)
          : FBFireStore.orders.doc();

      final messageRef = FBFireStore.sessions
          .doc(orderModel?.sessionId)
          .collection('messages')
          .doc();

      final infoData = {
        'type': MessageTypes.info,
        'sessionId': orderModel?.sessionId,
        'loadingText': 'info',
        'data': {
          'text': orderStatus == OrderStatus.processing
              ? 'Order Placed. Your order id is ${orderModel?.orderId}'
              : 'Order Status Updated'
        },
        'senderId': FBAuth.auth.currentUser?.uid,
        'sendAt': DateTime.now().millisecondsSinceEpoch,
      };

      final messageData = {
        'type': MessageTypes.orderAccRej,
        'sessionId': orderModel?.sessionId,
        'loadingText': 'order accept reject',
        'data': {
          'orderDocId': orderRef.id,
        },
        'senderId': FBAuth.auth.currentUser?.uid,
        'sendAt': DateTime.now().millisecondsSinceEpoch,
      };

      final batch = FBFireStore.fb.batch();
      // NEW ORDER ADDED
      if (orderStatus == OrderStatus.userConfirmation) {
        batch.set(orderRef, data);
        batch.update(FBFireStore.orders.doc(widget.orderDocId), {
          'orderStatus': OrderStatus.cancelled,
          'statusUpdatedBy': FBAuth.auth.currentUser?.uid,
          'statusUpdatedAt': DateTime.now().millisecondsSinceEpoch,
        });
      }
      // CANCELLING CURRENT ORDER
      else if (orderStatus == OrderStatus.cancelled) {
        batch.update(FBFireStore.orders.doc(widget.orderDocId), {
          'orderStatus': OrderStatus.cancelled,
          'statusUpdatedBy': FBAuth.auth.currentUser?.uid,
          'statusUpdatedAt': DateTime.now().millisecondsSinceEpoch,
        });
      }
      // CANCELLING OLD ORDER
      else {
        batch.update(FBFireStore.orders.doc(widget.orderDocId), {
          'isPaid': isPaid,
          'paidOn': orderModel!.isPaid
              ? orderModel?.paidOn?.millisecondsSinceEpoch
              : isPaid
                  ? DateTime.now().millisecondsSinceEpoch
                  : null,
          'paidAmount': paidAmount,
          'orderStatus': orderStatus,
          'statusUpdatedBy': FBAuth.auth.currentUser?.uid,
          'statusUpdatedAt': DateTime.now().millisecondsSinceEpoch,
        });
      }

      batch.set(messageRef,
          orderStatus == OrderStatus.userConfirmation ? messageData : infoData);
      batch.update(FBFireStore.sessions.doc(orderModel?.sessionId),
          {'lastMessage': infoData});
      //  CREATING VENDOR ORDER
      if (orderStatus == OrderStatus.processing) {
        List<String> vendorIds =
            orderProductData.map((e) => e.vendorId).toSet().toList();
        for (var vendorId in vendorIds) {
          final orderedVendorProducts = orderProductData
              .where((element) => element.vendorId == vendorId)
              .toList();
          if (orderedVendorProducts.isNotEmpty) {
            final vendorOrderdata = {
              'orderId': orderModel?.orderId,
              'orderDocId': orderModel?.docId,
              'vendorId': vendorId,
              'orderProducts': orderedVendorProducts
                  .map((product) => product.toJson())
                  .toList(),
              'createdAt': DateTime.now().millisecondsSinceEpoch,
              'statusUpdatedAt': DateTime.now().millisecondsSinceEpoch,
              'statusUpdatedBy': FBAuth.auth.currentUser?.uid,
              'createdBy': FBAuth.auth.currentUser?.uid,
              'status': 'Created',
            };
            batch.set(FBFireStore.vendorOrder.doc(), vendorOrderdata);
          }
        }
      }
      await batch.commit();
      setState(() {
        onSubmitLoad = false;
      });
      Navigator.of(context).pop();
      orderStatus == OrderStatus.userConfirmation
          ? context.push('${Routes.order}/${orderRef.id}')
          : context.pop();

      showAppSnackBar(context, 'Order updated');
    } catch (e) {
      setState(() {
        onSubmitLoad = false;
      });
      showErrorAppSnackBar(context, 'Error updating order');
      debugPrint(e.toString());
    }
  }

  Future<dynamic> addEditTransactionDialog(
      BuildContext context, TransactionModel? transaction) {
    final amountCtrl = TextEditingController();
    final paymentLinkCtrl = TextEditingController();
    final noteCtrl = TextEditingController();
    final transactionIdCtrl = TextEditingController();
    final methodCtrl = TextEditingController();
    bool isPaid = false;
    DateTime? paidOn;

    if (transaction != null) {
      amountCtrl.text = transaction.amount.toString();
      paymentLinkCtrl.text = transaction.paymentLink;
      noteCtrl.text = transaction.note;
      transactionIdCtrl.text = transaction.transactionId ?? '';
      methodCtrl.text = transaction.method ?? '';
      isPaid = transaction.isPaid;
      paidOn = transaction.paymentTime;
    }

    return showDialog(
      context: context,
      builder: (context) {
        bool isLoading = false;
        return StatefulBuilder(builder: (context, setState2) {
          return AlertDialog(
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            title: Text('Transaction',
                style:
                    GoogleFonts.zcoolXiaoWei(fontSize: 25, color: themeColor)),
            content: Container(
              constraints: const BoxConstraints(maxWidth: 800),
              width: double.maxFinite,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          controller: amountCtrl,
                          decoration: inpDecor().copyWith(labelText: 'Amount'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: TextFormField(
                          controller: noteCtrl,
                          decoration: inpDecor().copyWith(labelText: 'Note'),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 15),
                  TextFormField(
                    controller: paymentLinkCtrl,
                    decoration: inpDecor().copyWith(labelText: 'Payment Link'),
                  ),
                  if (transaction != null) ...[
                    const SizedBox(height: 15),
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: transactionIdCtrl,
                            decoration: inpDecor()
                                .copyWith(labelText: 'Transaction Id'),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: TextFormField(
                            controller: methodCtrl,
                            decoration:
                                inpDecor().copyWith(labelText: 'Payment Mode'),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 15),
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            clipBehavior: Clip.antiAlias,
                            height: 48,
                            padding: const EdgeInsets.only(left: 3),
                            decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey.shade400),
                                borderRadius: BorderRadius.circular(7),
                                // color: const Color.fromARGB(9, 0, 0, 0),
                                color: Colors.transparent),
                            child: Row(
                              children: [
                                Checkbox(
                                  side: const BorderSide(color: Colors.grey),
                                  checkColor: Colors.white,
                                  activeColor: themeColor,
                                  hoverColor: Colors.transparent,
                                  // focusColor: Colors.transparent,
                                  overlayColor: const WidgetStatePropertyAll(
                                      Colors.transparent),
                                  value: isPaid,
                                  onChanged: (value) async {
                                    isPaid = value!;
                                    setState2(() {});
                                  },
                                ),
                                const SizedBox(width: 5),
                                const Text(
                                  "Payment Recieved",
                                  style: TextStyle(color: Colors.black),
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: InkWell(
                            onTap: () async {
                              final res = await showDatePicker(
                                  context: context,
                                  firstDate: DateTime(2000),
                                  lastDate: DateTime.now());
                              paidOn = res;
                              setState2(() {});
                            },
                            child: TextFormField(
                              controller: TextEditingController(
                                  text: paidOn?.convertToDDMMYY() ?? ''),
                              enabled: false,
                              style: const TextStyle(color: Colors.black),
                              decoration: inpDecor().copyWith(
                                  labelText: 'paid on',
                                  labelStyle:
                                      const TextStyle(color: Colors.black),
                                  suffixIcon: const Icon(
                                    CupertinoIcons.calendar,
                                    color: Colors.black,
                                  )),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ]
                ],
              ),
            ),
            actions: isLoading
                ? [
                    const SizedBox(
                      height: 28,
                      width: 28,
                      child: Center(
                        child: CircularProgressIndicator(),
                      ),
                    )
                  ]
                : [
                    TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: const Text('Cancel'),
                    ),
                    ElevatedButton(
                      onPressed: () async {
                        try {
                          if (amountCtrl.text.trim().isEmpty) {
                            showErrorAppSnackBar(context, 'Amount missing');
                            return;
                          }
                          if (num.tryParse(amountCtrl.text.trim()) == null) {
                            showErrorAppSnackBar(
                                context, 'Amount in Numbers only');
                            return;
                          }
                          if (paymentLinkCtrl.text.trim().isEmpty) {
                            showErrorAppSnackBar(
                                context, 'Payment link required');
                            return;
                          }

                          if (isLoading) return;

                          isLoading = true;
                          setState2(() {});
                          final messageRef = FBFireStore.sessions
                              .doc(orderModel?.sessionId)
                              .collection('messages')
                              .doc();

                          final transactionRef = transaction != null
                              ? FBFireStore.transaction.doc(transaction.docId)
                              : FBFireStore.transaction.doc();

                          final transactionData = {
                            'amount': num.tryParse(amountCtrl.text),
                            'orderId': orderModel?.orderId,
                            'isDebit': true,
                            'paymentTime': paidOn?.millisecondsSinceEpoch,
                            'note': noteCtrl.text,
                            'uId': orderModel?.uid,
                            'isPaid': isPaid,
                            'paymentLink': paymentLinkCtrl.text,
                            'transactionId': transaction != null
                                ? transactionIdCtrl.text.trim()
                                : null,
                            'method': transaction != null
                                ? methodCtrl.text.trim()
                                : null,
                            'createdAt':
                                transaction?.createdAt.millisecondsSinceEpoch ??
                                    DateTime.now().millisecondsSinceEpoch,
                            'inquiryId': orderModel?.sessionId,
                            'messageId': messageRef.id,
                          };

                          final messageData = {
                            'type': MessageTypes.payment,
                            'sessionId': orderModel?.sessionId,
                            'loadingText': 'payment',
                            'data': {
                              'amount': num.tryParse(amountCtrl.text),
                              'paymentLink': paymentLinkCtrl.text,
                              'note': noteCtrl.text,
                              'transactionId': transactionRef.id,
                            },
                            'senderId': FBAuth.auth.currentUser?.uid,
                            'sendAt': DateTime.now().millisecondsSinceEpoch,
                          };

                          final batch = FBFireStore.fb.batch();
                          batch.set(transactionRef, transactionData);

                          if (transaction == null) {
                            batch.set(messageRef, messageData);
                          }
                          await batch.commit();
                          isLoading = false;
                          setState2(() {});
                          Navigator.of(context).pop();
                        } on Exception catch (e) {
                          debugPrint(e.toString());
                          isLoading = false;
                          setState2(() {});
                        }
                      },
                      child: const Text('Save'),
                    ),
                  ],
          );
        });
      },
    );
  }

  Widget addNewProductButton(BuildContext context) {
    return InkWell(
      hoverColor: Colors.transparent,
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      onTap: () async {
        if (sessionModel == null) return;
        final userSessionProductSnap = await FBFireStore.variants
            .where(FieldPath.documentId,
                whereIn: sessionModel?.finalVariantIds
                        .map((e) => e.variantId)
                        .toList() ??
                    [])
            .get();
        final userSessionProduct = userSessionProductSnap.docs
            .map((e) => NewVariantModel.fromSnap(e))
            .toList();
        showDialog(
          context: context,
          builder: (context) {
            bool isLoading = false;
            List<NewVariantModel> tempOrderedVariants = [];
            List<NewVariantModel> productFromSearch = [];
            tempOrderedVariants
                .addAll(allOrderedProductDetails.map((e) => e.variant));
            return StatefulBuilder(builder: (context, setState2) {
              return Dialog(
                backgroundColor: Colors.white,
                surfaceTintColor: Colors.white,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8)),
                child: Container(
                  constraints: const BoxConstraints(
                    maxHeight: 800,
                    maxWidth: 1000,
                  ),
                  child: SingleChildScrollView(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 15, vertical: 8),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      // mainAxisSize:
                      //     MainAxisSize.min,
                      children: [
                        const SizedBox(height: 10),
                        Row(
                          children: [
                            Text(
                              'Products',
                              style: GoogleFonts.zcoolXiaoWei(fontSize: 25),
                            ),
                            Spacer(),
                            InkWell(
                                hoverColor: Colors.transparent,
                                highlightColor: Colors.transparent,
                                splashColor: Colors.transparent,
                                onTap: () {
                                  addProductBySearch(
                                      context,
                                      tempOrderedVariants,
                                      productFromSearch,
                                      setState2);
                                },
                                child: Container(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 8, vertical: 5),
                                  decoration: BoxDecoration(
                                    border:
                                        Border.all(color: Color(0xffe2e8f0)),
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: Row(
                                    children: [
                                      const Icon(CupertinoIcons.search,
                                          size: 18),
                                      SizedBox(width: 4),
                                      Text(
                                        "Search",
                                        style: TextStyle(
                                            fontSize: 13.5,
                                            letterSpacing: 1,
                                            fontWeight: FontWeight.w500),
                                      )
                                    ],
                                  ),
                                ))
                          ],
                        ),
                        const SizedBox(height: 20),
                        if (userSessionProduct.isNotEmpty)
                          Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "From User",
                                  style: GoogleFonts.mulish(
                                    fontSize: 15,
                                    color: themeColor,
                                  ),
                                ),
                                SizedBox(height: 7),
                                Divider(height: 0, color: dividerColor),
                                SizedBox(height: 15),
                                StaggeredGrid.extent(
                                  maxCrossAxisExtent: 200,
                                  mainAxisSpacing: 15,
                                  crossAxisSpacing: 10,
                                  children: [
                                    ...List.generate(
                                      userSessionProduct.length,
                                      (index) {
                                        bool isSelected = tempOrderedVariants
                                            .map((e) => e.docId)
                                            .contains(userSessionProduct[index]
                                                .docId);
                                        return Container(
                                          height: 250,
                                          clipBehavior: Clip.antiAlias,
                                          decoration: BoxDecoration(
                                              color: const Color.fromARGB(
                                                  255, 248, 248, 248),
                                              borderRadius:
                                                  BorderRadius.circular(8)),
                                          padding: const EdgeInsets.all(10),
                                          child: Stack(
                                            children: [
                                              Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  Container(
                                                    height: 180,
                                                    width: double.maxFinite,
                                                    clipBehavior:
                                                        Clip.antiAlias,
                                                    decoration: BoxDecoration(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(7)),
                                                    child: Image.network(
                                                      userSessionProduct[index]
                                                          .images
                                                          .first,
                                                      fit: BoxFit.cover,
                                                    ),
                                                  ),
                                                  const SizedBox(height: 5),
                                                  Expanded(
                                                    child: Text(
                                                      capilatlizeFirstLetter(
                                                          userSessionProduct[
                                                                  index]
                                                              .lowerName),
                                                      maxLines: 2,
                                                      overflow:
                                                          TextOverflow.ellipsis,
                                                      style: GoogleFonts.mulish(
                                                          fontSize: 14,
                                                          color: const Color(
                                                              0xff3E3E3E)),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              Align(
                                                alignment: Alignment.topRight,
                                                child: Checkbox(
                                                  side: const BorderSide(
                                                      color: Colors.grey),
                                                  checkColor: themeColor,
                                                  fillColor:
                                                      const WidgetStatePropertyAll(
                                                          Colors.white),
                                                  activeColor: Colors.white,
                                                  hoverColor:
                                                      Colors.transparent,
                                                  // focusColor: Colors.transparent,
                                                  overlayColor:
                                                      const WidgetStatePropertyAll(
                                                          Colors.transparent),
                                                  value: isSelected,
                                                  onChanged: (value) async {
                                                    if (value == true) {
                                                      tempOrderedVariants.add(
                                                          userSessionProduct[
                                                              index]);
                                                      setState2(() {});
                                                    }
                                                    if (value == false) {
                                                      tempOrderedVariants
                                                          .removeWhere((element) =>
                                                              element.docId ==
                                                              userSessionProduct[
                                                                      index]
                                                                  .docId);
                                                      setState2(() {});
                                                    }
                                                  },
                                                ),
                                              ),
                                            ],
                                          ),
                                        );
                                      },
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 15),
                              ]),
                        if (productFromSearch.isNotEmpty)
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "From Search",
                                style: GoogleFonts.mulish(
                                  fontSize: 15,
                                  color: themeColor,
                                ),
                              ),
                              SizedBox(height: 7),
                              Divider(height: 0, color: dividerColor),
                              SizedBox(height: 15),
                              StaggeredGrid.extent(
                                maxCrossAxisExtent: 200,
                                mainAxisSpacing: 15,
                                crossAxisSpacing: 10,
                                children: [
                                  ...List.generate(
                                    productFromSearch.length,
                                    (index) {
                                      bool isSelected = tempOrderedVariants
                                          .map((e) => e.docId)
                                          .contains(
                                              productFromSearch[index].docId);
                                      return Container(
                                        height: 250,
                                        clipBehavior: Clip.antiAlias,
                                        decoration: BoxDecoration(
                                            color: const Color.fromARGB(
                                                255, 248, 248, 248),
                                            borderRadius:
                                                BorderRadius.circular(8)),
                                        padding: const EdgeInsets.all(10),
                                        child: Stack(
                                          children: [
                                            Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Container(
                                                  height: 180,
                                                  width: double.maxFinite,
                                                  clipBehavior: Clip.antiAlias,
                                                  decoration: BoxDecoration(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              7)),
                                                  child: Image.network(
                                                    productFromSearch[index]
                                                        .images
                                                        .first,
                                                    fit: BoxFit.cover,
                                                  ),
                                                ),
                                                const SizedBox(height: 5),
                                                Expanded(
                                                  child: Text(
                                                    capilatlizeFirstLetter(
                                                        productFromSearch[index]
                                                            .lowerName),
                                                    maxLines: 2,
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                    style: GoogleFonts.mulish(
                                                        fontSize: 14,
                                                        color: const Color(
                                                            0xff3E3E3E)),
                                                  ),
                                                ),
                                              ],
                                            ),
                                            Align(
                                              alignment: Alignment.topRight,
                                              child: Checkbox(
                                                side: const BorderSide(
                                                    color: Colors.grey),
                                                checkColor: themeColor,
                                                fillColor:
                                                    const WidgetStatePropertyAll(
                                                        Colors.white),
                                                activeColor: Colors.white,
                                                hoverColor: Colors.transparent,
                                                // focusColor: Colors.transparent,
                                                overlayColor:
                                                    const WidgetStatePropertyAll(
                                                        Colors.transparent),
                                                value: isSelected,
                                                onChanged: (value) async {
                                                  if (value == true) {
                                                    tempOrderedVariants.add(
                                                        productFromSearch[
                                                            index]);
                                                    setState2(() {});
                                                  }
                                                  if (value == false) {
                                                    tempOrderedVariants
                                                        .removeWhere((element) =>
                                                            element.docId ==
                                                            productFromSearch[
                                                                    index]
                                                                .docId);
                                                    setState2(() {});
                                                  }
                                                },
                                              ),
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                  ),
                                ],
                              ),
                            ],
                          ),
                        const SizedBox(height: 15),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: isLoading
                              ? [
                                  const SizedBox(
                                    height: 28,
                                    width: 28,
                                    child: Center(
                                      child: CircularProgressIndicator(),
                                    ),
                                  )
                                ]
                              : [
                                  TextButton(
                                    onPressed: () {
                                      Navigator.of(context).pop();
                                    },
                                    child: const Text("Cancel"),
                                  ),
                                  const SizedBox(width: 10),
                                  ElevatedButton(
                                      onPressed: () async {
                                        isLoading = true;
                                        setState2(() {});

                                        try {
                                          for (var variant
                                              in tempOrderedVariants) {
                                            final selectedVariantIds =
                                                allOrderedProductDetails
                                                    .map((e) => e.variant.docId)
                                                    .toList();
                                            final existingorderData =
                                                allOrderedProductDetails
                                                    .firstWhereOrNull(
                                                        (element) {
                                              return selectedVariantIds
                                                  .contains(variant.docId);
                                            });

                                            if (existingorderData == null) {
                                              final productSnap =
                                                  await FBFireStore.products
                                                      .doc(variant.productId)
                                                      .get();
                                              final productData =
                                                  ProductModel.fromDocSnap(
                                                      productSnap);

                                              final vendorSnap =
                                                  await FBFireStore.vendors
                                                      .doc(productData
                                                          .vendorDocId)
                                                      .get();
                                              final vendorData =
                                                  VendorModel.fromDocSnap(
                                                      vendorSnap);
                                              allOrderedProductDetails.add(DisplayProductsDetails(
                                                  product: productData,
                                                  variant: variant,
                                                  vendorData: vendorData,
                                                  orderData: OrderProductData(
                                                      orderProductExtraData: OrderProductExtraData(
                                                          vendorDocId:
                                                              vendorData.docId,
                                                          vendorName:
                                                              vendorData.name,
                                                          vendorEmail:
                                                              vendorData.email,
                                                          vendorPhone:
                                                              vendorData.phone,
                                                          productDocId:
                                                              productData.docId ??
                                                                  '',
                                                          productSku:
                                                              productData.sku,
                                                          productName:
                                                              productData.name,
                                                          productDesc: productData
                                                              .description,
                                                          variantDocId:
                                                              variant.docId ??
                                                                  '',
                                                          variantImage: variant
                                                              .images.first,
                                                          variantPurchasePrice:
                                                              variant
                                                                  .fixedprice,
                                                          variantDescription: variant
                                                              .description,
                                                          variantDetailTypes:
                                                              variant.detailTypes),
                                                      vendorId: vendorData.docId,
                                                      id: getRandomId(6),
                                                      productId: productData.docId ?? "",
                                                      variantId: variant.docId!,
                                                      qty: 0,
                                                      currentStatus: null,
                                                      purchasePrice: variant.fixedprice,
                                                      sellingPrice: 0,
                                                      userNote: '',
                                                      adminNote: '',
                                                      productSKu: productData.sku,
                                                      deliveryDate: null)));
                                            }
                                          }

                                          isLoading = false;
                                          setState2(() {});
                                          Navigator.of(context).pop();
                                          setState(() {});
                                        } on Exception catch (e) {
                                          debugPrint(e.toString());
                                          isLoading = false;
                                          setState2(() {});
                                        }
                                      },
                                      child: const Text('Save'))
                                ],
                        )
                      ],
                    ),
                  ),
                ),
              );
            });
          },
        );
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 8, vertical: 5),
        decoration: BoxDecoration(
          border: Border.all(color: Color(0xffe2e8f0)),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Row(
          children: [
            const Icon(CupertinoIcons.add, size: 18),
            SizedBox(width: 4),
            Text(
              "Product",
              style: TextStyle(
                  fontSize: 13.5,
                  letterSpacing: 1,
                  fontWeight: FontWeight.w500),
            )
          ],
        ),
      ),
    );
  }

  Future<dynamic> addProductBySearch(
      BuildContext context,
      List<NewVariantModel> tempOrderedVariants,
      List<NewVariantModel> productFromSearch,
      StateSetter setState2) {
    return showDialog(
      context: context,
      builder: (context) {
        Timer? debounce;
        List<NewVariantModel> searchedList = [];
        getSearchVariant(String searchedStr) async {
          searchedList.clear();
          final searchedSnap = await FBFireStore.variants
              .where('lowerName', isGreaterThanOrEqualTo: searchedStr)
              .where('lowerName', isLessThanOrEqualTo: "$searchedStr\uf7ff")
              .limit(10)
              .get();
          searchedList.addAll(
              searchedSnap.docs.map((e) => NewVariantModel.fromSnap(e)));
        }

        return Dialog(
          backgroundColor: Colors.white,
          surfaceTintColor: Colors.white,
          child: StatefulBuilder(builder: (context, searchSetState) {
            return SingleChildScrollView(
              child: Container(
                constraints: const BoxConstraints(maxWidth: 800),
                padding: const EdgeInsets.all(20),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Search Product',
                        style: GoogleFonts.zcoolXiaoWei(
                            fontSize: 25, color: themeColor)),
                    const SizedBox(height: 15),
                    TextFormField(
                      decoration: InputDecoration(
                        hintText: ' search product name',
                        hintStyle: TextStyle(
                            color: Color(0xff737373),
                            fontSize: 14.5,
                            fontWeight: FontWeight.w500),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(5),
                          borderSide: BorderSide(color: Colors.grey.shade400),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(5),
                          borderSide: BorderSide(color: Colors.grey.shade400),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(5),
                          borderSide: BorderSide(color: Colors.grey.shade400),
                        ),
                      ),
                      onChanged: (value) async {
                        if (debounce?.isActive ?? false) {
                          debounce?.cancel();
                        }
                        debounce =
                            Timer(const Duration(milliseconds: 500), () async {
                          if (value.trim().isNotEmpty) {
                            await getSearchVariant(value.toLowerCase().trim());
                            searchSetState(() {});
                          } else {
                            searchedList.clear();
                            searchSetState(() {});
                          }
                        });
                      },
                    ),
                    if (searchedList.isNotEmpty) SizedBox(height: 15),
                    if (searchedList.isNotEmpty)
                      StaggeredGrid.extent(
                        maxCrossAxisExtent: 180,
                        mainAxisSpacing: 15,
                        crossAxisSpacing: 15,
                        children: [
                          ...List.generate(
                            searchedList.length,
                            (index) {
                              final variant = searchedList[index];
                              return InkWell(
                                hoverColor: Colors.transparent,
                                highlightColor: Colors.transparent,
                                onTap: () {
                                  if (tempOrderedVariants
                                          .map((e) => e.docId)
                                          .contains(variant.docId) ||
                                      productFromSearch
                                          .map((e) => e.docId)
                                          .contains(variant.docId)) {
                                    showAppSnackBar(context, 'Already in cart');
                                    return;
                                  }

                                  productFromSearch.add(variant);
                                  tempOrderedVariants.add(variant);
                                  // selectedVariantList
                                  //     .add(variant);
                                  Navigator.of(context).pop();
                                  setState2(() {});
                                },
                                child: Container(
                                  height: 250,
                                  clipBehavior: Clip.antiAlias,
                                  decoration: BoxDecoration(
                                      color: const Color.fromARGB(
                                          255, 248, 248, 248),
                                      borderRadius: BorderRadius.circular(8)),
                                  padding: const EdgeInsets.all(10),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        height: 180,
                                        width: double.maxFinite,
                                        clipBehavior: Clip.antiAlias,
                                        decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(7)),
                                        child: Image.network(
                                          variant.images.first,
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                      const SizedBox(height: 5),
                                      Expanded(
                                        child: Text(
                                          capilatlizeFirstLetter(
                                              variant.lowerName),
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                          style: GoogleFonts.mulish(
                                              fontSize: 14,
                                              color: const Color(0xff3E3E3E)),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                          )
                        ],
                      )

                    /*  if (searchedList.isNotEmpty) 
                    ...[
                      const SizedBox(height: 10),
                      const Row(
                        children: [
                          SizedBox(width: 50, child: Text('Sr No')),
                          SizedBox(width: 10),
                          SizedBox(
                              width: 100, child: Center(child: Text('Image'))),
                          SizedBox(width: 10),
                          Expanded(child: Text('Product Name')),
                          SizedBox(width: 10),
                        ],
                      ),
                      const SizedBox(height: 10),
                      ...List.generate(
                        searchedList.length,
                        (index) {
                          final variant = searchedList[index];
                          return Padding(
                            padding: EdgeInsets.only(top: index == 0 ? 0 : 8),
                            child: InkWell(
                              hoverColor: Colors.transparent,
                              highlightColor: Colors.transparent,
                              onTap: () {
                                if (tempOrderedVariants
                                        .map((e) => e.docId)
                                        .contains(variant.docId) ||
                                    productFromSearch
                                        .map((e) => e.docId)
                                        .contains(variant.docId)) {
                                  showAppSnackBar(context, 'Already in cart');
                                  return;
                                }
              
                                productFromSearch.add(variant);
                                tempOrderedVariants.add(variant);
                                // selectedVariantList
                                //     .add(variant);
                                Navigator.of(context).pop();
                                setState2(() {});
                              },
                              child: Row(
                                children: [
                                  SizedBox(
                                      width: 50,
                                      child: Text((index + 1).toString())),
                                  const SizedBox(width: 10),
                                  SizedBox(
                                    width: 100,
                                    child: Center(
                                      child: Container(
                                          height: 50,
                                          width: 50,
                                          clipBehavior: Clip.antiAlias,
                                          decoration: const BoxDecoration(
                                              shape: BoxShape.circle),
                                          child: Image.network(
                                              variant.images.first)),
                                    ),
                                  ),
                                  const SizedBox(width: 10),
                                  Expanded(
                                      child: Text(capilatlizeFirstLetter(
                                          variant.lowerName))),
                                ],
                              ),
                            ),
                          );
                        },
                      )
                    ],
                   */
                  ],
                ),
              ),
            );
          }),
        );
      },
    );
  }
}
