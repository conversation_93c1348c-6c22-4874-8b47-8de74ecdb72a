import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:wedding_super_admin/models/order_model.dart';
import 'package:wedding_super_admin/shared/methods.dart';
import '../../../shared/router.dart';
import '../../../shared/theme.dart';

class OrderTile extends StatefulWidget {
  const OrderTile(
      {super.key,
      required this.index,
      required this.orderModel,
      required this.isLast});
  final int index;
  final OrderModel orderModel;
  final bool isLast;
  @override
  State<OrderTile> createState() => _OrderTileState();
}

class _OrderTileState extends State<OrderTile> {
  @override
  Widget build(BuildContext context) {
    return InkWell(
      highlightColor: Colors.transparent,
      hoverColor: Colors.transparent,
      splashColor: Colors.transparent,
      onTap: () {
        context.push('${Routes.order}/${widget.orderModel.docId}');
      },
      child: Container(
        decoration: BoxDecoration(
            border: const Border(
                bottom: BorderSide(color: dividerColor),
                left: BorderSide(color: dividerColor),
                right: BorderSide(color: dividerColor)),
            borderRadius: widget.isLast
                ? const BorderRadius.only(
                    bottomLeft: Radius.circular(8),
                    bottomRight: Radius.circular(8))
                : null),
        child: Row(
          children: [
            SizedBox(
              width: 80,
              child: Text((widget.index + 1).toString(),
                  textAlign: TextAlign.center),
            ),
            const SizedBox(width: 5),
            Expanded(child: Text(widget.orderModel.orderId)),
            const SizedBox(width: 5),
            Expanded(
                flex: 2,
                child: Text(
                    '\$ ${formatPriceWithCommas(widget.orderModel.totalAmount)}')),
            const SizedBox(width: 5),
            Expanded(
                child:
                    Text(widget.orderModel.orderProductData.length.toString())),
            const SizedBox(width: 5),
            Expanded(
                child: Text(
              widget.orderModel.orderStatus.toUpperCase(),
              style: const TextStyle(fontSize: 12),
            )),
            const SizedBox(width: 5),
            Expanded(
                child: Text(
              (widget.orderModel.isDirectOrder ? 'Direct' : 'Indirect')
                  .toUpperCase(),
              style: const TextStyle(fontSize: 12),
            )),
            const SizedBox(width: 5),
            Expanded(
              child: Text(
                  '${widget.orderModel.createdAt.goodDayDate()}  ${widget.orderModel.createdAt.goodTime()}'),
            ),
            // const SizedBox(width: 5),
            // Opacity(
            //   opacity: 0,
            //   child: IgnorePointer(
            //     ignoring: true,
            //     child: SizedBox(
            //         width: 60,
            //         child: Transform.scale(
            //           scale: .65,
            //           child: CupertinoSwitch(
            //             value: true,
            //             onChanged: (value) {},
            //           ),
            //         )),
            //   ),
            // ),
            Opacity(
              opacity: 0,
              child: IgnorePointer(
                ignoring: true,
                child: SizedBox(
                  width: 60,
                  child: IconButton(
                    highlightColor: Colors.transparent,
                    hoverColor: Colors.transparent,
                    onPressed: () {},
                    icon: const Icon(
                      Icons.edit,
                      size: 22,
                    ),
                  ),
                ),
              ),
            ),
            Opacity(
              opacity: 0,
              child: IgnorePointer(
                ignoring: true,
                child: SizedBox(
                  width: 60,
                  child: IconButton(
                    highlightColor: Colors.transparent,
                    hoverColor: Colors.transparent,
                    onPressed: () {},
                    icon: const Icon(
                      Icons.delete,
                      color: themeColor,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
