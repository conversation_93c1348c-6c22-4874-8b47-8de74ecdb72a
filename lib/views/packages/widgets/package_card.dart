import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wedding_super_admin/controller/home_ctrl.dart';
import 'package:wedding_super_admin/shared/firebase.dart';
import 'package:wedding_super_admin/shared/theme.dart';
import 'package:wedding_super_admin/views/packages/packages.dart';

import '../../../models/package_model.dart';
import '../../../shared/methods.dart';

class PackageCard extends StatelessWidget {
  const PackageCard({
    super.key,
    required this.packageModel,
  });
  final PackageModel packageModel;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      hoverColor: Colors.transparent,
      highlightColor: Colors.transparent,
      splashColor: Colors.transparent,
      onTap: () {
        addEditPackages(context, packageModel,
            Get.find<HomeCtrl>().settings?.services ?? []);
      },
      child: Container(
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: const Color(0xffCBCBCB), width: .8)),
        padding: const EdgeInsets.symmetric(vertical: 25, horizontal: 13),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15),
                    border: Border.all(
                      color: const Color(0xffCBCBCB),
                      width: .7,
                    ),
                  ),
                  padding:
                      const EdgeInsets.symmetric(vertical: 5, horizontal: 15),
                  child: Text(
                    packageModel.packageName,
                    style: GoogleFonts.livvic(
                      color: const Color(0xff6C6C6C),
                      fontWeight: FontWeight.w500,
                      letterSpacing: .7,
                      fontSize: 14,
                    ),
                  ),
                ),
                const SizedBox(width: 10),
                Transform.scale(
                  scale: .85,
                  child: CupertinoSwitch(
                    value: packageModel.isActive,
                    onChanged: (value) async {
                      await FBFireStore.packages
                          .doc(packageModel.docId)
                          .update({
                        'isActive': value,
                      });
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text.rich(
                  TextSpan(children: [
                    TextSpan(
                      text: '\$',
                      style: GoogleFonts.zcoolXiaoWei(
                          fontSize: 22, color: const Color(0xff3E3E3E)),
                    ),
                    TextSpan(
                      text: formatPriceWithCommas(packageModel.price),
                      style: GoogleFonts.zcoolXiaoWei(
                          fontSize: 25, color: const Color(0xff3E3E3E)),
                    ),
                  ]),
                ),
                Text.rich(TextSpan(children: [
                  TextSpan(
                    text: packageModel.validity.toString(),
                    style: GoogleFonts.livvic(fontSize: 16),
                  ),
                  TextSpan(
                    text: ' ',
                    style: GoogleFonts.livvic(fontSize: 16),
                  ),
                  TextSpan(
                    text: 'Days',
                    style: GoogleFonts.livvic(fontSize: 13.5),
                  ),
                ]))
              ],
            ),
            const SizedBox(height: 10),
            Text(
              packageModel.description,
              style: GoogleFonts.poppins(fontSize: 15),
            ),
            const SizedBox(height: 20),
            Container(
              decoration: const BoxDecoration(
                border: Border(bottom: BorderSide(color: themeColor)),
              ),
              child: Text(
                "Services",
                style: GoogleFonts.zcoolXiaoWei(
                    fontSize: 23, letterSpacing: 1, color: themeColor),
              ),
            ),
            const SizedBox(height: 15),
            ...List.generate(
              packageModel.points.length,
              (index) {
                final pointModel = packageModel.points[index];
                return Padding(
                  padding: EdgeInsets.only(top: index == 0 ? 0 : 8.0),
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          pointModel.serviceName,
                          style: GoogleFonts.livvic(fontSize: 15.5),
                        ),
                      ),
                      const SizedBox(width: 5),
                      Text.rich(TextSpan(children: [
                        TextSpan(
                          text: pointModel.serviceQty.toString(),
                          style: GoogleFonts.livvic(fontSize: 16),
                        ),
                        TextSpan(
                          text: ' ',
                          style: GoogleFonts.livvic(fontSize: 16),
                        ),
                        TextSpan(
                          text: pointModel.serviceUnit.toString(),
                          style: GoogleFonts.livvic(fontSize: 13.5),
                        ),
                      ]))
                    ],
                  ),
                );
              },
            ),
            const SizedBox(height: 15),
            Align(
              alignment: Alignment.bottomRight,
              child: IconButton(
                  onPressed: () async {
                    showDialog(
                      context: context,
                      builder: (context) {
                        bool loading = false;
                        return StatefulBuilder(builder: (context, setState2) {
                          return AlertDialog(
                            backgroundColor: Colors.white,
                            surfaceTintColor: Colors.white,
                            title: const Text("Delete"),
                            content: const Text(
                                "Are you sure you want to delete this package?"),
                            actions: loading
                                ? [
                                    const SizedBox(
                                      height: 28,
                                      width: 28,
                                      child: Center(
                                          child: CircularProgressIndicator(
                                        strokeWidth: 2.5,
                                        color: themeColor,
                                      )),
                                    )
                                  ]
                                : [
                                    TextButton(
                                        onPressed: () async {
                                          if (loading) return;

                                          try {
                                            setState2(() {
                                              loading = true;
                                            });
                                            await FBFireStore.packages
                                                .doc(packageModel.docId)
                                                .delete();
                                            setState2(() {
                                              loading = false;
                                            });
                                            if (context.mounted) {
                                              showAppSnackBar(context,
                                                  'Package deleted successfully');
                                              Navigator.of(context).pop();
                                            }
                                          } on Exception catch (e) {
                                            showErrorAppSnackBar(
                                                context, e.toString());
                                            debugPrint(e.toString());
                                          }
                                        },
                                        child: const Text("Yes")),
                                    TextButton(
                                        onPressed: () {
                                          Navigator.of(context).pop();
                                        },
                                        child: const Text("No"))
                                  ],
                          );
                        });
                      },
                    );
                  },
                  icon: const Icon(CupertinoIcons.delete)),
            )
          ],
        ),
      ),
    );
  }
}
