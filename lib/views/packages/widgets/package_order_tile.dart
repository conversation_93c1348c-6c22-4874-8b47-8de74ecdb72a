import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wedding_super_admin/models/package_order_model.dart';
import 'package:wedding_super_admin/models/user_model.dart';
import 'package:wedding_super_admin/shared/firebase.dart';
import 'package:wedding_super_admin/shared/methods.dart';

import '../../../shared/theme.dart';

class PackageOrderTile extends StatefulWidget {
  const PackageOrderTile(
      {super.key,
      required this.packageOrderModel,
      required this.index,
      required this.isLast});
  final PackagePurchaseModel packageOrderModel;
  final int index;
  final bool isLast;
  @override
  State<PackageOrderTile> createState() => _PackageOrderTileState();
}

class _PackageOrderTileState extends State<PackageOrderTile> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          border: const Border(
              bottom: BorderSide(color: dividerColor),
              left: BorderSide(color: dividerColor),
              right: BorderSide(color: dividerColor)),
          borderRadius: widget.isLast
              ? const BorderRadius.only(
                  bottomLeft: Radius.circular(8),
                  bottomRight: Radius.circular(8))
              : null),
      child: InkWell(
        hoverColor: Colors.transparent,
        highlightColor: Colors.transparent,
        splashColor: Colors.transparent,
        onTap: () async {
          final userSnap =
              await FBFireStore.users.doc(widget.packageOrderModel.uid).get();

          UserModel? user =
              userSnap.exists ? UserModel.fromDocSnap(userSnap) : null;
          packageOrderDialog(context, user);
        },
        child: Row(
          children: [
            SizedBox(
              width: 80,
              child: Text(
                '${widget.index + 1}',
                textAlign: TextAlign.center,
                // style: TextStyle(
                //   fontWeight: FontWeight.w600,
                //   letterSpacing: 1.2,
                // ),
              ),
            ),
            const SizedBox(width: 5),
            Expanded(
              child: Text(widget.packageOrderModel.packageName),
            ),
            const SizedBox(width: 5),
            Expanded(
              child: Text(
                  capilatlizeFirstLetter(widget.packageOrderModel.userName)),
            ),

            const SizedBox(width: 5),
            Expanded(
              child: Text(widget.packageOrderModel.isPaid ? 'Paid' : 'Unpaid'),
            ),
            const SizedBox(width: 5),
            Expanded(
              child: Text(
                  '${widget.packageOrderModel.createdAt.goodDayDate()} - ${widget.packageOrderModel.createdAt.goodTime()}'),
            ),
            const SizedBox(width: 5),
            // IgnorePointer(
            //   ignoring: true,
            //   child: SizedBox(
            //       width: 60,
            //       child: Transform.scale(
            //         scale: .65,
            //         child: CupertinoSwitch(
            //           value: true,
            //           onChanged: (value) {},
            //         ),
            //       )),
            // ),
            IgnorePointer(
              ignoring: true,
              child: SizedBox(
                width: 60,
                child: IconButton(
                  highlightColor: Colors.transparent,
                  hoverColor: Colors.transparent,
                  onPressed: () async {
                    final userSnap = await FBFireStore.users
                        .doc(widget.packageOrderModel.uid)
                        .get();

                    UserModel? user = userSnap.exists
                        ? UserModel.fromDocSnap(userSnap)
                        : null;
                    packageOrderDialog(context, user);
                  },
                  icon: const Icon(
                    CupertinoIcons.eye,
                    size: 22,
                    color: Colors.grey,
                  ),
                ),
              ),
            ),
            /*  IgnorePointer(
              ignoring: true,
              child: SizedBox(
                width: 60,
                child: IconButton(
                  highlightColor: Colors.transparent,
                  hoverColor: Colors.transparent,
                  onPressed: () {},
                  icon: const Icon(
                    Icons.delete,
                    color: themeColor,
                  ),
                ),
              ),
            ), */
          ],
        ),
      ),
    );
  }

  Future<dynamic> packageOrderDialog(BuildContext context, UserModel? user) {
    final packageOrderModel = widget.packageOrderModel;
    return showDialog(
      context: context,
      builder: (context) {
        return Dialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
          backgroundColor: Colors.white,
          surfaceTintColor: Colors.white,
          child: SingleChildScrollView(
            child: Container(
              padding:
                  const EdgeInsets.symmetric(vertical: 20.0, horizontal: 20),
              constraints: BoxConstraints(maxWidth: 800, maxHeight: 600),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    children: [
                      Text(
                        'Order Purchase Details',
                        style: GoogleFonts.zcoolXiaoWei(
                            fontSize: 25, color: themeColor),
                      ),
                      Spacer(),
                      InkWell(
                        hoverColor: Colors.transparent,
                        highlightColor: Colors.transparent,
                        splashColor: Colors.transparent,
                        onTap: () {
                          Navigator.of(context).pop();
                        },
                        child: Container(
                            height: 28,
                            width: 28,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.grey,
                                  offset: Offset(0, 2),
                                  blurRadius: 5,
                                ),
                              ],
                            ),
                            child: Icon(CupertinoIcons.xmark, size: 16)),
                      )
                    ],
                  ),
                  SizedBox(height: 17),
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: Column(
                              children: [
                                Container(
                                  padding: EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                      // border: Border.all(color: dividerColor),
                                      borderRadius: BorderRadius.circular(5),
                                      color: Colors.white,
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.grey.shade400,
                                          offset: Offset(.5, .5),
                                          blurRadius: .7,
                                          // spreadRadius:
                                        ),
                                      ]),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "User Details",
                                        style: GoogleFonts.livvic(
                                          fontWeight: FontWeight.w500,
                                          letterSpacing: .5,
                                          wordSpacing: .8,
                                          fontSize: 15.5,
                                          color: Color(0xff3E3E3E),
                                        ),
                                      ),
                                      SizedBox(height: 10),
                                      Divider(
                                        color: const Color.fromARGB(
                                            255, 196, 137, 115),
                                        height: 0,
                                        thickness: .3,
                                      ),
                                      SizedBox(height: 10),
                                      Row(
                                        children: [
                                          Icon(
                                            CupertinoIcons.person,
                                            size: 17,
                                            color: Color.fromARGB(
                                                255, 111, 111, 111),
                                          ),
                                          SizedBox(width: 8),
                                          Text(user?.name ?? '')
                                        ],
                                      ),
                                      SizedBox(height: 13),
                                      Row(
                                        children: [
                                          Icon(
                                            CupertinoIcons.mail,
                                            size: 17,
                                            color: Color.fromARGB(
                                                255, 111, 111, 111),
                                          ),
                                          SizedBox(width: 8),
                                          Text(user?.email ?? '')
                                        ],
                                      ),
                                      SizedBox(height: 10),
                                      Row(
                                        children: [
                                          Icon(
                                            CupertinoIcons.phone,
                                            size: 17,
                                            color: Color.fromARGB(
                                                255, 111, 111, 111),
                                          ),
                                          SizedBox(width: 8),
                                          Text(user?.phone ?? '')
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                                SizedBox(height: 18),
                                Container(
                                  padding: EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                      // border: Border.all(color: dividerColor),
                                      borderRadius: BorderRadius.circular(5),
                                      color: Colors.white,
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.grey.shade400,
                                          offset: Offset(.5, .5),
                                          blurRadius: .7,
                                          // spreadRadius:
                                        ),
                                      ]),
                                  child: Column(
                                    children: [
                                      Row(
                                        children: [
                                          // Icon(CupertinoIcons.person),
                                          // SizedBox(width: 10),
                                          Text(
                                            "Payment Details",
                                            style: GoogleFonts.livvic(
                                              fontWeight: FontWeight.w500,
                                              letterSpacing: .5,
                                              wordSpacing: .8,
                                              fontSize: 15.5,
                                              color: Color(0xff3E3E3E),
                                            ),
                                          ),
                                        ],
                                      ),
                                      SizedBox(height: 10),
                                      Divider(
                                        color: const Color.fromARGB(
                                            255, 196, 137, 115),
                                        height: 0,
                                        thickness: .3,
                                      ),
                                      SizedBox(height: 10),
                                      Row(
                                        children: [
                                          Text('Package Amount'),
                                          SizedBox(width: 8),
                                          Spacer(),
                                          Text.rich(TextSpan(children: [
                                            TextSpan(text: '\$ '),
                                            TextSpan(
                                              text: formatPriceWithCommas(
                                                  packageOrderModel
                                                      .packagePrice),
                                            )
                                          ]))
                                        ],
                                      ),
                                      SizedBox(height: 10),
                                      Row(
                                        children: [
                                          Text('Extra Amount'),
                                          SizedBox(width: 8),
                                          Spacer(),
                                          Text.rich(TextSpan(children: [
                                            TextSpan(text: '\$ '),
                                            TextSpan(
                                              text: formatPriceWithCommas(
                                                  packageOrderModel
                                                      .extraServicesAmount),
                                            )
                                          ]))
                                        ],
                                      ),
                                      SizedBox(height: 13),
                                      Divider(
                                          color: const Color.fromARGB(
                                              255, 239, 239, 239),
                                          height: 0),
                                      SizedBox(height: 10),
                                      Row(
                                        children: [
                                          Text(
                                            'Total Amount',
                                            style: TextStyle(
                                                fontSize: 14,
                                                fontWeight: FontWeight.w600),
                                          ),
                                          SizedBox(width: 8),
                                          Spacer(),
                                          Text.rich(
                                            TextSpan(
                                              children: [
                                                TextSpan(
                                                  text: '\$ ',
                                                  style: TextStyle(
                                                      fontSize: 14,
                                                      fontWeight:
                                                          FontWeight.w600),
                                                ),
                                                TextSpan(
                                                  text: formatPriceWithCommas(
                                                      packageOrderModel
                                                          .totalAmount),
                                                  style: TextStyle(
                                                      fontSize: 14,
                                                      fontWeight:
                                                          FontWeight.w600),
                                                )
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                      // SizedBox(height: 13),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(width: 18),
                          Expanded(
                            child: Column(
                              children: [
                                Container(
                                  padding: EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                      // border: Border.all(color: dividerColor),
                                      borderRadius: BorderRadius.circular(5),
                                      color: Colors.white,
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.grey.shade400,
                                          offset: Offset(.5, .5),
                                          blurRadius: .7,
                                          // spreadRadius:
                                        ),
                                      ]),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          // Icon(CupertinoIcons.cube_box),
                                          // SizedBox(width: 10),
                                          Text(
                                            "Package Details",
                                            style: GoogleFonts.livvic(
                                              fontWeight: FontWeight.w500,
                                              letterSpacing: .5,
                                              wordSpacing: .8,
                                              fontSize: 15.5,
                                              color: Color(0xff3E3E3E),
                                            ),
                                          ),
                                        ],
                                      ),
                                      SizedBox(height: 10),
                                      Divider(
                                        color: const Color.fromARGB(
                                            255, 196, 137, 115),
                                        height: 0,
                                        thickness: .3,
                                      ),
                                      SizedBox(height: 10),
                                      Row(
                                        children: [
                                          Text(
                                            packageOrderModel.packageName,
                                            style: GoogleFonts.zcoolXiaoWei(
                                              fontSize: 17,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                          Spacer(),
                                          Text.rich(TextSpan(children: [
                                            TextSpan(
                                                text: 'Validity :  ',
                                                style: TextStyle(
                                                    fontSize: 14,
                                                    color: const Color.fromARGB(
                                                        255, 127, 127, 127))),
                                            TextSpan(
                                                text: packageOrderModel.validity
                                                    .toString(),
                                                style: TextStyle(
                                                    fontSize: 16,
                                                    color: Color(0xff3E3E3E)))
                                          ]))
                                        ],
                                      ),
                                      SizedBox(height: 10),
                                      ...List.generate(
                                        packageOrderModel.points.length,
                                        (index) {
                                          final point =
                                              packageOrderModel.points[index];
                                          return Column(
                                            children: [
                                              Row(
                                                children: [
                                                  Icon(
                                                    CupertinoIcons
                                                        .checkmark_alt_circle,
                                                    size: 20,
                                                    // color: Color.fromARGB(
                                                    //     255, 176, 176, 176),
                                                  ),
                                                  SizedBox(width: 8),
                                                  Text(point.serviceName),
                                                  Spacer(),
                                                  Text(point.serviceQty
                                                      .toString())
                                                ],
                                              ),
                                              if (packageOrderModel
                                                          .points.length -
                                                      1 !=
                                                  index)
                                                SizedBox(height: 13),
                                            ],
                                          );
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                                if (packageOrderModel
                                    .extraServices.isNotEmpty) ...[
                                  SizedBox(height: 18),
                                  Container(
                                    padding: EdgeInsets.all(12),
                                    decoration: BoxDecoration(
                                        // border: Border.all(color: dividerColor),
                                        borderRadius: BorderRadius.circular(5),
                                        color: Colors.white,
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.grey.shade400,
                                            offset: Offset(.5, .5),
                                            blurRadius: .7,
                                            // spreadRadius:
                                          ),
                                        ]),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          children: [
                                            // Icon(CupertinoIcons.cube_box),
                                            // SizedBox(width: 10),
                                            Text(
                                              "Added Services",
                                              style: GoogleFonts.livvic(
                                                fontWeight: FontWeight.w500,
                                                letterSpacing: .5,
                                                wordSpacing: .8,
                                                fontSize: 15.5,
                                                color: Color(0xff3E3E3E),
                                              ),
                                            ),
                                          ],
                                        ),
                                        SizedBox(height: 10),
                                        Divider(
                                          color: const Color.fromARGB(
                                              255, 196, 137, 115),
                                          height: 0,
                                          thickness: .3,
                                        ),
                                        SizedBox(height: 10),
                                        ...List.generate(
                                          packageOrderModel
                                              .extraServices.length,
                                          (index) {
                                            final point = packageOrderModel
                                                .extraServices[index];
                                            return Column(
                                              children: [
                                                Row(
                                                  children: [
                                                    Icon(
                                                      CupertinoIcons
                                                          .checkmark_alt_circle,
                                                      size: 20,
                                                      // color: Color.fromARGB(
                                                      //     255, 176, 176, 176),
                                                    ),
                                                    SizedBox(width: 8),
                                                    Text(point.serviceName),
                                                    Spacer(),
                                                    Text(point.qty.toString())
                                                  ],
                                                ),
                                                if (packageOrderModel
                                                            .extraServices
                                                            .length -
                                                        1 !=
                                                    index)
                                                  SizedBox(height: 13),
                                              ],
                                            );
                                          },
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
