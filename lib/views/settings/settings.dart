import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:wedding_super_admin/controller/home_ctrl.dart';
import 'package:wedding_super_admin/models/package_model.dart';
import 'package:wedding_super_admin/shared/const.dart';
import 'package:wedding_super_admin/shared/firebase.dart';
import 'package:wedding_super_admin/shared/methods.dart';
import 'package:wedding_super_admin/shared/theme.dart';
import 'package:wedding_super_admin/views/common/top_sections.dart';

import '../../shared/router.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  List<String> slotGaps = ['10', '15', '20', '30', '45', '60'];
  int? selectedSlotGap;
  TextEditingController startCtrl = TextEditingController();
  TextEditingController endCtrl = TextEditingController();

  DateTime selectyedDate = DateTime(2025, 3, 15, 10, 10);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const TopSectionOfPages(
            pageTile: 'Settings',
            pagesubTile: 'Configure your platform preferences'),
        Expanded(
          child: GetBuilder<HomeCtrl>(
            builder: (hCtrl) {
              selectedSlotGap = hCtrl.settings?.slotGap;
              startCtrl.text =
                  '${hCtrl.settings?.startHour.toString().padLeft(2, '0')}: ${hCtrl.settings?.startMin.toString().padLeft(2, '0')}';
              endCtrl.text =
                  '${hCtrl.settings?.endHour.toString().padLeft(2, '0')}: ${hCtrl.settings?.endMin.toString().padLeft(2, '0')}';
              return SingleChildScrollView(
                padding:
                    const EdgeInsets.symmetric(vertical: 20, horizontal: 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // TextButton(
                    //   onPressed: () async {
                    //     final data = {
                    //       "summary": "Team Sync",
                    //       "startDateTime": "2025-07-01T10:00:00-07:00",
                    //       "endDateTime": "2025-07-01T10:30:00-07:00",
                    //       "attendees": [
                    //         "<EMAIL>",
                    //         "<EMAIL>",
                    //       ]
                    //     };
                    //     final result = await FBFunctions.ff
                    //         .httpsCallable('createGoogleMeet')
                    //         .call(data);
                    //     print('Meet Link: ${result.data['meetLink']}');
                    //   },
                    //   child: Text("data"),
                    // ),
                    // TextButton(
                    //     onPressed: () async {
                    //       final res = await FBFireStore.settings.get();
                    //       await FBFireStore.testse.set(res.data()!);
                    //     },
                    //     child: Text("Copy ")),
                    StaggeredGrid.extent(
                      maxCrossAxisExtent: 250,
                      crossAxisSpacing: 15,
                      mainAxisSpacing: 15,
                      children: [
                        InkWell(
                          hoverColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          focusColor: Colors.transparent,
                          splashColor: Colors.transparent,
                          onTap: () {
                            context.push(Routes.gallery);
                          },
                          child: Container(
                            padding: EdgeInsets.all(10),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: dividerColor),
                            ),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Container(
                                  padding: EdgeInsets.all(10),
                                  decoration: BoxDecoration(
                                    color: Color(0xfffef7f6),
                                    shape: BoxShape.circle,
                                  ),
                                  child: Icon(
                                    CupertinoIcons.camera,
                                    size: 25,
                                    color: Color(0xfff5b7a3),
                                  ),
                                ),
                                SizedBox(width: 15),
                                Text(
                                  'Gallery',
                                  style: TextStyle(
                                      fontSize: 15,
                                      fontWeight: FontWeight.w600,
                                      color: Color(0xff333333)),
                                ),
                              ],
                            ),
                          ),
                        ),
                        // SizedBox(width: 15),
                        InkWell(
                          hoverColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          focusColor: Colors.transparent,
                          splashColor: Colors.transparent,
                          onTap: () {
                            context.push(Routes.testimonials);
                          },
                          child: Container(
                            padding: EdgeInsets.all(10),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: dividerColor),
                            ),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Container(
                                  padding: EdgeInsets.all(10),
                                  decoration: BoxDecoration(
                                    color: Color(0xfffef7f6),
                                    shape: BoxShape.circle,
                                  ),
                                  child: Icon(
                                    CupertinoIcons.hand_thumbsup,
                                    size: 25,
                                    color: Color(0xfff5b7a3),
                                  ),
                                ),
                                SizedBox(width: 15),
                                Text(
                                  'Testimonials',
                                  style: TextStyle(
                                      fontSize: 15,
                                      fontWeight: FontWeight.w600,
                                      color: Color(0xff333333)),
                                ),
                              ],
                            ),
                          ),
                        ),
                        // SizedBox(width: 15),
                        InkWell(
                          hoverColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          focusColor: Colors.transparent,
                          splashColor: Colors.transparent,
                          onTap: () {
                            context.push(Routes.blogs);
                          },
                          child: Container(
                            padding: EdgeInsets.all(10),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: dividerColor),
                            ),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Container(
                                  padding: EdgeInsets.all(10),
                                  decoration: BoxDecoration(
                                    color: Color(0xfffef7f6),
                                    shape: BoxShape.circle,
                                  ),
                                  child: Icon(
                                    CupertinoIcons.doc_append,
                                    size: 25,
                                    color: Color(0xfff5b7a3),
                                  ),
                                ),
                                SizedBox(width: 15),
                                Text(
                                  'Blogs',
                                  style: TextStyle(
                                      fontSize: 15,
                                      fontWeight: FontWeight.w600,
                                      color: Color(0xff333333)),
                                ),
                              ],
                            ),
                          ),
                        ),
                        // SizedBox(width: 15),
                        InkWell(
                          hoverColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          focusColor: Colors.transparent,
                          splashColor: Colors.transparent,
                          onTap: () {
                            context.push(Routes.questionaires);
                          },
                          child: Container(
                            padding: EdgeInsets.all(10),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: dividerColor),
                            ),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Container(
                                  padding: EdgeInsets.all(10),
                                  decoration: BoxDecoration(
                                    color: Color(0xfffef7f6),
                                    shape: BoxShape.circle,
                                  ),
                                  child: Icon(
                                    CupertinoIcons.question_circle,
                                    size: 25,
                                    color: Color(0xfff5b7a3),
                                  ),
                                ),
                                SizedBox(width: 15),
                                Text(
                                  'Questionnaire',
                                  style: TextStyle(
                                      fontSize: 15,
                                      fontWeight: FontWeight.w600,
                                      color: Color(0xff333333)),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),

                    // Booking Settings
                    SizedBox(height: 25),
                    Container(
                      width: double.maxFinite,
                      padding:
                          EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                      decoration: BoxDecoration(
                          color: dashboardSelectedColor,
                          border: Border.all(color: dividerColor),
                          borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(8),
                              topRight: Radius.circular(8))
                          // color: const Color.fromARGB(255, 228, 228, 228),
                          // color: themeColor,
                          // color: const Color.fromARGB(255, 177, 139, 86),
                          // color: tableHeaderColor,
                          // borderRadius: BorderRadius.circular(4),
                          ),
                      child: Text(
                        'Booking Settings',
                        style: TextStyle(
                            fontWeight: FontWeight.bold, letterSpacing: .8),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          vertical: 15, horizontal: 15),
                      decoration: const BoxDecoration(
                          border: Border(
                              bottom: BorderSide(color: dividerColor),
                              left: BorderSide(color: dividerColor),
                              right: BorderSide(color: dividerColor)),
                          borderRadius: BorderRadius.only(
                              bottomLeft: Radius.circular(8),
                              bottomRight: Radius.circular(8))),
                      child: Row(
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text("Start Time",
                                    style: TextStyle(letterSpacing: .7)),
                                const SizedBox(height: 5),
                                InkWell(
                                  onTap: () async {
                                    final res = await showTimePicker(
                                      context: context,
                                      initialTime: TimeOfDay.now(),
                                    );
                                    if (res != null) {
                                      await FBFireStore.settings.update({
                                        'startHour': res.hour,
                                        'startMin': res.minute,
                                      });
                                    }
                                  },
                                  child: TextFormField(
                                    controller: startCtrl,
                                    enabled: false,
                                    style: const TextStyle(color: Colors.black),
                                    decoration: InputDecoration(
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(7),
                                        borderSide: BorderSide(
                                            color: Colors.grey.shade400),
                                      ),
                                      disabledBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(7),
                                        borderSide: BorderSide(
                                            color: Colors.grey.shade400),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(width: 15),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text("End Time",
                                    style: TextStyle(letterSpacing: .7)),
                                const SizedBox(height: 5),
                                InkWell(
                                  onTap: () async {
                                    final res = await showTimePicker(
                                      context: context,
                                      initialTime: TimeOfDay.now(),
                                    );
                                    if (res != null) {
                                      await FBFireStore.settings.update({
                                        'endHour': res.hour,
                                        'endMin': res.minute,
                                      });
                                    }
                                  },
                                  child: TextFormField(
                                    controller: endCtrl,
                                    enabled: false,
                                    style: const TextStyle(color: Colors.black),
                                    decoration: InputDecoration(
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(7),
                                        borderSide: BorderSide(
                                            color: Colors.grey.shade400),
                                      ),
                                      disabledBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(7),
                                        borderSide: BorderSide(
                                            color: Colors.grey.shade400),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(7),
                                        borderSide: BorderSide(
                                            color: Colors.grey.shade400),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(width: 15),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text("Slot Interval",
                                    style: TextStyle(letterSpacing: .7)),
                                const SizedBox(height: 5),
                                DropdownButtonHideUnderline(
                                  child: DropdownButtonFormField(
                                    decoration: InputDecoration(
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(7),
                                        borderSide: BorderSide(
                                            color: Colors.grey.shade400),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(7),
                                        borderSide: BorderSide(
                                            color: Colors.grey.shade400),
                                      ),
                                      enabledBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(7),
                                        borderSide: BorderSide(
                                            color: Colors.grey.shade400),
                                      ),
                                    ),
                                    value: selectedSlotGap,
                                    items: [
                                      ...List.generate(
                                        slotGaps.length,
                                        (index) {
                                          return DropdownMenuItem(
                                            value:
                                                int.tryParse(slotGaps[index]),
                                            child: Text(slotGaps[index]),
                                          );
                                        },
                                      )
                                    ],
                                    onChanged: (value) async {
                                      await FBFireStore.settings
                                          .update({'slotGap': value});
                                      selectedSlotGap = value;
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                    SizedBox(height: 25),

                    // Package Services

                    Container(
                      width: double.maxFinite,
                      padding:
                          EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                      decoration: BoxDecoration(
                          color: dashboardSelectedColor,
                          border: Border.all(color: dividerColor),
                          borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(8),
                              topRight: Radius.circular(8))
                          // color: const Color.fromARGB(255, 228, 228, 228),
                          // color: themeColor,
                          // color: const Color.fromARGB(255, 177, 139, 86),
                          // color: tableHeaderColor,
                          // borderRadius: BorderRadius.circular(4),
                          ),
                      child: Row(
                        children: [
                          Text(
                            'Package Services',
                            style: TextStyle(
                                fontWeight: FontWeight.bold, letterSpacing: .8),
                          ),
                          SizedBox(width: 15),
                          InkWell(
                            hoverColor: Colors.transparent,
                            splashColor: Colors.transparent,
                            highlightColor: Colors.transparent,
                            onTap: () async {
                              addEditPackageService(context, null);
                              // addEditTransactionDialog(context, null);
                            },
                            child: Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 5),
                              decoration: BoxDecoration(
                                border: Border.all(
                                    color: Color.fromARGB(255, 125, 125, 125)),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Row(
                                children: [
                                  const Icon(CupertinoIcons.square_grid_2x2,
                                      size: 18),
                                  SizedBox(width: 5),
                                  Text(
                                    "Service",
                                    style: TextStyle(
                                        fontSize: 13.5,
                                        letterSpacing: 1,
                                        fontWeight: FontWeight.w500),
                                  )
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                        width: double.maxFinite,
                        padding: const EdgeInsets.symmetric(
                            vertical: 15, horizontal: 15),
                        decoration: const BoxDecoration(
                            border: Border(
                                bottom: BorderSide(color: dividerColor),
                                left: BorderSide(color: dividerColor),
                                right: BorderSide(color: dividerColor)),
                            borderRadius: BorderRadius.only(
                                bottomLeft: Radius.circular(8),
                                bottomRight: Radius.circular(8))),
                        child: StaggeredGrid.extent(
                          maxCrossAxisExtent: 350,
                          // alignment: WrapAlignment.start,
                          // runAlignment: WrapAlignment.start,
                          mainAxisSpacing: 10,
                          crossAxisSpacing: 10,
                          children: [
                            ...List.generate(
                              hCtrl.settings?.services.length ?? 0,
                              (index) {
                                final service = hCtrl.settings?.services[index];
                                return InkWell(
                                  hoverColor: Colors.transparent,
                                  focusColor: Colors.transparent,
                                  splashColor: Colors.transparent,
                                  highlightColor: Colors.transparent,
                                  onTap: () {
                                    addEditPackageService(context, service);
                                  },
                                  child: Container(
                                    padding: EdgeInsets.all(8),
                                    // width: 150,
                                    decoration: BoxDecoration(
                                        border: Border.all(color: dividerColor),
                                        borderRadius: BorderRadius.circular(4)),
                                    child: Row(
                                      children: [
                                        Container(
                                          padding: EdgeInsets.all(7),
                                          decoration: BoxDecoration(
                                            shape: BoxShape.circle,
                                            color: Color.fromARGB(
                                                255, 230, 230, 230),
                                          ),
                                          child: Text(
                                            (index + 1).toString(),
                                            style: TextStyle(
                                                fontSize: 13,
                                                color: Color(0xff6C6C6C)),
                                          ),
                                        ),
                                        SizedBox(width: 8),
                                        Expanded(
                                          child: Text(
                                            capilatlizeFirstLetter(
                                                service?.name ?? '-'),
                                            maxLines: 2,
                                            overflow: TextOverflow.ellipsis,
                                            style: TextStyle(
                                                fontSize: 16,
                                                color: Color(0xff3E3E3E)),
                                          ),
                                        ),
                                        SizedBox(width: 10),
                                        // Spacer(),
                                        Text.rich(
                                          TextSpan(
                                            children: [
                                              TextSpan(
                                                text: '\$',
                                                style: TextStyle(
                                                    fontSize: 12.5,
                                                    color: Color(0xff3E3E3E)),
                                              ),
                                              TextSpan(
                                                text: formatPriceWithCommas(
                                                    service?.price ?? 0),
                                                style: TextStyle(
                                                    fontSize: 15,
                                                    color: Color(0xff3E3E3E)),
                                              ),
                                              TextSpan(text: ' / '),
                                              TextSpan(
                                                text:
                                                    '${(service?.qty ?? 0).toString()} ${service?.unit ?? ''}',
                                                style: TextStyle(
                                                    fontSize: 13,
                                                    color: Color(0xff6C6C6C)),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            )
                          ],
                        )),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}

addEditPackageService(
    BuildContext context, PackageServiceModel? packageService) async {
  bool loading = false;
  final serviceNameCtrl = TextEditingController();
  String? selectedUnit;
  final priceCtrl = TextEditingController();
  final qtyCtrl = TextEditingController();

  if (packageService != null) {
    serviceNameCtrl.text = packageService.name;
    selectedUnit = packageService.unit;
    priceCtrl.text = packageService.price.toString();
    qtyCtrl.text = packageService.qty.toString();
  }

  return showDialog(
    context: context,
    builder: (context) {
      return StatefulBuilder(builder: (context, setState2) {
        return AlertDialog(
          // backgroundColor: dashboardColor,
          // surfaceTintColor: dashboardColor,
          // shadowColor: dashboardColor,
          backgroundColor: Colors.white,
          surfaceTintColor: Colors.white,
          contentPadding:
              const EdgeInsets.symmetric(vertical: 15, horizontal: 25),
          title: Text(packageService != null ? "Edit Service" : "Add Service"),
          content: SizedBox(
            width: 280,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: serviceNameCtrl,
                  cursorHeight: 20,
                  decoration: inpDecor().copyWith(labelText: 'Name'),
                ),
                const SizedBox(height: 20),
                DropdownButtonHideUnderline(
                  child: DropdownButtonFormField(
                    decoration: inpDecor().copyWith(labelText: 'Type'),
                    items: packageServiceType
                        .map((e) => DropdownMenuItem(
                            value: e, child: Text(capilatlizeFirstLetter(e))))
                        .toList(),
                    value: selectedUnit,
                    onChanged: (value) {
                      selectedUnit = value;
                      setState2(() {});
                    },
                  ),
                ),
                const SizedBox(height: 20),
                // TextFormField(
                //   controller: qtyCtrl,
                //   cursorHeight: 20,
                //   decoration: inpDecor().copyWith(labelText: 'Qty'),
                // ),
                // const SizedBox(height: 20),
                TextFormField(
                  controller: priceCtrl,
                  cursorHeight: 20,
                  decoration: inpDecor().copyWith(labelText: 'Price per unit'),
                ),
              ],
            ),
          ),
          actionsAlignment: MainAxisAlignment.center,
          actions: loading
              ? [
                  const Center(
                    child: SizedBox(
                      height: 25,
                      width: 25,
                      child: CircularProgressIndicator(
                        strokeWidth: 2.5,
                        color: themeColor,
                      ),
                    ),
                  )
                ]
              : [
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: themeColor,
                      foregroundColor: Colors.white,
                    ),
                    onPressed: () async {
                      if (loading) return;

                      if (serviceNameCtrl.text.trim().isEmpty) {
                        showErrorAppSnackBar(context, 'Name Required');
                        return;
                      }
                      if (selectedUnit == null) {
                        showErrorAppSnackBar(context, 'Select Type');
                        return;
                      }

                      if (priceCtrl.text.trim().isEmpty) {
                        showErrorAppSnackBar(context, 'Price Required');
                        return;
                      }

                      if (num.tryParse(priceCtrl.text.trim()) == null) {
                        showErrorAppSnackBar(
                            context, 'Something wrong with price');
                        return;
                      }
                      try {
                        setState2(() {
                          loading = true;
                        });
                        final data = {
                          'name': serviceNameCtrl.text,
                          'unit': selectedUnit,
                          'qty': 1,
                          'price': num.tryParse(priceCtrl.text),
                        };
                        await FBFireStore.settings.update({
                          'services.${packageService?.id ?? getRandomId(6)}':
                              data,
                        });
                        setState2(() {
                          loading = false;
                        });

                        if (context.mounted) {
                          Navigator.of(context).pop();
                          showAppSnackBar(
                            context,
                            packageService != null
                                ? 'Service Updated'
                                : 'Service Added',
                          );
                        }
                      } catch (e) {
                        debugPrint(e.toString());
                        showAppSnackBar(context, e.toString());
                      }
                    },
                    child: const Text("Save"),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: const Text("Cancel"),
                  ),
                ],
        );
      });
    },
  );
}
