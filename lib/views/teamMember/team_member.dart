import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get_state_manager/src/simple/get_state.dart';
import 'package:go_router/go_router.dart';
import 'package:wedding_super_admin/views/common/top_sections.dart';
import 'package:wedding_super_admin/views/teamMember/widgets/team_member_tile.dart';
import 'package:wedding_super_admin/views/teamMember/widgets/team_table_header.dart';

import '../../controller/home_ctrl.dart';
import '../../models/teammodel.dart';
import '../../shared/const.dart';
import '../../shared/firebase.dart';
import '../../shared/methods.dart';
import '../../shared/router.dart';
import '../../shared/theme.dart';
import '../common/header_search_feild.dart';
import '../common/page_header.dart';

class TeamMember extends StatefulWidget {
  const TeamMember({super.key});

  @override
  State<TeamMember> createState() => _TeamMemberState();
}

class _TeamMemberState extends State<TeamMember> {
  final searchController = TextEditingController();
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const TopSectionOfPages(
          pageTile: 'Team Management',
          pagesubTile: 'Manage teams and their activities',
        ),
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                PageHeaderWithButton(
                  title: 'Team Member',
                  button: true,
                  buttonName: 'Member',
                  icon: CupertinoIcons.person_add,
                  onPressed: () {
                    addEditMemberForm(context, null);
                  },
                ),
                const SizedBox(height: 20),
                SearchField(
                  searchController: searchController,
                  onChanged: (p0) async {
                    setState(() {});
                  },
                ),
                const SizedBox(height: 25),
                const TeamTableHeader(),
                GetBuilder<HomeCtrl>(builder: (hctrl) {
                  final srchStr = searchController.text.toLowerCase().trim();
                  List<TeamModel> filteredList = hctrl.teamMembers
                      .where((element) =>
                          (element.name.toLowerCase().contains(srchStr)) ||
                          (element.email.toLowerCase().contains(srchStr)) ||
                          (element.phone.toLowerCase().contains(srchStr)))
                      .toList();
                  return filteredList.isEmpty
                      ? Container(
                          padding: const EdgeInsets.symmetric(vertical: 15),
                          decoration: const BoxDecoration(
                              border: Border(
                                  bottom: BorderSide(color: dividerColor),
                                  left: BorderSide(color: dividerColor),
                                  right: BorderSide(color: dividerColor)),
                              borderRadius: BorderRadius.only(
                                  bottomLeft: Radius.circular(8),
                                  bottomRight: Radius.circular(8))),
                          child: const Center(
                              child: Text(
                            'No Data Available',
                            style: TextStyle(
                                color: Color(0xff737373),
                                fontSize: 14.5,
                                fontWeight: FontWeight.w500),
                          )))
                      : Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ...List.generate(
                              filteredList.length,
                              (index) {
                                final teamMember = filteredList[index];
                                return InkWell(
                                  hoverColor: Colors.transparent,
                                  highlightColor: Colors.transparent,
                                  splashColor: Colors.transparent,
                                  onTap: () {
                                    context.push(
                                        '${Routes.teamMember}/${teamMember.docId}');
                                  },
                                  child: TeamMemberTile(
                                    isLast: index == (filteredList.length - 1),
                                    onEdit: () {
                                      addEditMemberForm(context, teamMember);
                                    },
                                    index: index,
                                    teamMember: teamMember,
                                  ),
                                );
                              },
                            ),
                          ],
                        );
                })
              ],
            ),
          ),
        ),
      ],
    );
  }

  Future<dynamic> addEditMemberForm(
      BuildContext context, TeamModel? teamMember) {
    bool loading = false;
    TextEditingController nameCtrl = TextEditingController();
    TextEditingController numberCtrl = TextEditingController();
    TextEditingController emailCtrl = TextEditingController();
    TextEditingController addressCtrl = TextEditingController();
    TextEditingController specialityCtrl = TextEditingController();
    String? selectedUserType;
    List<String> userTypeList = [
      UserTypes.consultants,
      UserTypes.manager,
      UserTypes.styler
    ];
    if (teamMember != null) {
      nameCtrl.text = teamMember.name;
      numberCtrl.text = teamMember.phone;
      emailCtrl.text = teamMember.email;
      selectedUserType = teamMember.userType;
      specialityCtrl.text = teamMember.speciality ?? "";
      addressCtrl.text = teamMember.address;
    }
    return showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(builder: (context, setState2) {
          return AlertDialog(
            // backgroundColor: const Color(0xffFEF2D0),
            // surfaceTintColor: const Color(0xffFEF2D0),
            // shadowColor: const Color(0xffFEF2D0),
            // backgroundColor: dashboardColor,
            // surfaceTintColor: dashboardColor,
            // shadowColor: dashboardColor,
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            // backgroundColor: const Color.fromARGB(255, 255, 244, 228),
            // surfaceTintColor: const Color.fromARGB(255, 255, 244, 228),
            // shadowColor: const Color.fromARGB(255, 255, 244, 228),
            contentPadding:
                const EdgeInsets.symmetric(vertical: 15, horizontal: 25),
            title: Text(teamMember != null ? "Edit Member" : "Add Member"),
            content: SizedBox(
              width: 280,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextFormField(
                    controller: nameCtrl,
                    cursorHeight: 20,
                    decoration: inpDecor().copyWith(labelText: 'Name*'),
                  ),
                  const SizedBox(height: 20),
                  TextFormField(
                    controller: numberCtrl,
                    cursorHeight: 20,
                    decoration: inpDecor().copyWith(labelText: 'Number*'),
                  ),
                  const SizedBox(height: 20),
                  TextFormField(
                    controller: emailCtrl,
                    enabled: teamMember == null,
                    cursorHeight: 20,
                    decoration: inpDecor().copyWith(labelText: 'Email*'),
                  ),
                  const SizedBox(height: 20),
                  TextFormField(
                    controller: addressCtrl,
                    cursorHeight: 20,
                    maxLines: 4,
                    decoration: inpDecor().copyWith(labelText: 'Address'),
                  ),
                  const SizedBox(height: 20),
                  SizedBox(
                    height: 45,
                    child: DropdownButtonHideUnderline(
                      child: DropdownButtonFormField(
                        value: selectedUserType,
                        decoration:
                            inpDecor().copyWith(labelText: 'Select User Type*'),
                        items: [
                          ...List.generate(
                            userTypeList.length,
                            (index) {
                              return DropdownMenuItem(
                                  value: userTypeList[index],
                                  child: Text(userTypeList[index]));
                            },
                          )
                        ],
                        onChanged: (value) {
                          selectedUserType = value;
                          setState2(() {});
                        },
                      ),
                    ),
                  ),
                  if (selectedUserType == UserTypes.styler)
                    const SizedBox(height: 20),
                  if (selectedUserType == UserTypes.styler)
                    TextFormField(
                      controller: specialityCtrl,
                      cursorHeight: 20,
                      decoration: inpDecor().copyWith(labelText: 'Speciality*'),
                    ),
                ],
              ),
            ),
            actionsAlignment: MainAxisAlignment.center,
            actions: loading
                ? [
                    const Center(
                      child: SizedBox(
                        height: 25,
                        width: 25,
                        child: CircularProgressIndicator(
                          strokeWidth: 2.5,
                          color: themeColor,
                        ),
                      ),
                    )
                  ]
                : [
                    ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: themeColor,
                        foregroundColor: Colors.white,
                      ),
                      onPressed: () async {
                        if (loading) return;

                        try {
                          if (nameCtrl.text.isEmpty) {
                            showErrorAppSnackBar(context, 'Name Required');
                            return;
                          }
                          if (numberCtrl.text.isEmpty) {
                            showErrorAppSnackBar(context, 'Number Required');
                            return;
                          }
                          if (emailCtrl.text.isEmpty) {
                            showErrorAppSnackBar(context, 'Email Required');
                            return;
                          }
                          if (selectedUserType == null) {
                            showErrorAppSnackBar(context, 'Select User Type');
                            return;
                          }
                          if (selectedUserType == UserTypes.styler &&
                              specialityCtrl.text.trim().isEmpty) {
                            showErrorAppSnackBar(
                                context, 'Speciality Required');
                            return;
                          }

                          final teamMemberSnap = teamMember != null
                              ? await FBFireStore.teamMember
                                  .where('email',
                                      isEqualTo:
                                          emailCtrl.text.toLowerCase().trim())
                                  .where(FieldPath.documentId,
                                      isNotEqualTo: teamMember.docId)
                                  .get()
                              : await FBFireStore.teamMember
                                  .where('email',
                                      isEqualTo:
                                          emailCtrl.text.toLowerCase().trim())
                                  .get();

                          if (teamMemberSnap.size != 0) {
                            showErrorAppSnackBar(
                                context, 'Email Already exist');
                            return;
                          }

                          setState2(() {
                            loading = true;
                          });

                          String? speciality =
                              selectedUserType == UserTypes.styler
                                  ? specialityCtrl.text.trim()
                                  : null;

                          final phone =
                              numberCtrl.text.trim().substring(0, 3) == '+91'
                                  ? numberCtrl.text.trim()
                                  : '+91${numberCtrl.text.trim()}';
                          final data = {
                            'name': nameCtrl.text.toLowerCase().trim(),
                            'phone': phone,
                            'address': addressCtrl.text.trim(),
                            'email': emailCtrl.text.toLowerCase().trim(),
                            'password': teamMember != null
                                ? teamMember.password
                                : getRandomMix(8),
                            'subcatids':
                                teamMember != null ? teamMember.subcatids : [],
                            'userType': selectedUserType,
                            'speciality': speciality,
                            'createdAt': teamMember?.createdAt,
                            'isActive':
                                teamMember != null ? teamMember.isActive : true,
                          };
                          if (teamMember != null) {
                            try {
                              bool res = await FBFireStore.teamMember
                                  .doc(teamMember.docId)
                                  .update(data)
                                  .then((value) => true);
                              if (res) {
                                showAppSnackBar(context, 'Team Member Updated');
                              } else {
                                showAppSnackBar(
                                    context, 'Error Updating Member');
                              }
                            } on Exception catch (e) {
                              debugPrint(e.toString());
                            }
                          }

                          if (teamMember == null) {
                            try {
                              final res = await FBFunctions.ff
                                  .httpsCallable(testMode
                                      ? 'createTeamMemberTest'
                                      : 'createTeamMember')
                                  .call(data);
                              if (res.data['success'] as bool) {
                                showAppSnackBar(context, 'Team Member Added');
                              } else {
                                showErrorAppSnackBar(
                                    context, 'Error Adding Member');
                              }
                            } on Exception catch (e) {
                              debugPrint(e.toString());
                            }
                          }
                          // teamMember != null
                          //     ? await FBFireStore.teamMember
                          //         .doc(teamMember.docId)
                          //         .update(data)
                          //     : await FBFunctions.ff
                          //         .httpsCallable('createTeamMember')
                          //         .call(data);
                          setState2(() {
                            loading = false;
                          });
                          // const snackBar = SnackBar(
                          //   content: Text("Vendor Added"),
                          //   duration: Duration(seconds: 2),
                          // );
                          nameCtrl.clear();
                          numberCtrl.clear();
                          emailCtrl.clear();
                          addressCtrl.clear();
                          if (context.mounted) {
                            Navigator.of(context).pop();
                            // showAppSnackBar(
                            //     context,
                            //     teamMember != null
                            //         ? 'Team Menber Updated'
                            //         : 'Team Menber Added');
                            // ScaffoldMessenger.of(context).showSnackBar(snackBar);
                          }
                        } catch (e) {
                          debugPrint(e.toString());
                          showAppSnackBar(context, e.toString());
                        }
                      },
                      child: const Text("Save"),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: const Text("Cancel"),
                    ),
                  ],
          );
        });
      },
    );
  }
}
