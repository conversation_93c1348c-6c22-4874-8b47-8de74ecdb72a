import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../models/teammodel.dart';
import '../../../shared/firebase.dart';
import '../../../shared/methods.dart';
import '../../../shared/theme.dart';

class TeamMemberTile extends StatefulWidget {
  const TeamMemberTile(
      {super.key,
      required this.index,
      this.teamMember,
      required this.onEdit,
      required this.isLast});
  final int index;
  final TeamModel? teamMember;
  final Function()? onEdit;
  final bool isLast;
  @override
  State<TeamMemberTile> createState() => _TeamMemberTileState();
}

class _TeamMemberTileState extends State<TeamMemberTile> {
  @override
  Widget build(BuildContext context) {
    return widget.teamMember != null
        ? Container(
            decoration: BoxDecoration(
                border: const Border(
                    bottom: BorderSide(color: dividerColor),
                    left: BorderSide(color: dividerColor),
                    right: BorderSide(color: dividerColor)),
                borderRadius: widget.isLast
                    ? const BorderRadius.only(
                        bottomLeft: Radius.circular(8),
                        bottomRight: Radius.circular(8))
                    : null),
            child: Row(
              children: [
                SizedBox(
                    width: 80,
                    child: Text((widget.index + 1).toString(),
                        textAlign: TextAlign.center)),
                const SizedBox(width: 5),
                Expanded(
                    child: Text(
                        capilatlizeFirstLetter(widget.teamMember?.name ?? ""))),
                const SizedBox(width: 5),
                Expanded(child: Text(widget.teamMember?.userType ?? "")),
                const SizedBox(width: 5),
                Expanded(flex: 2, child: Text(widget.teamMember?.email ?? "")),
                const SizedBox(width: 5),
                Expanded(child: Text(widget.teamMember?.phone ?? "")),
                const SizedBox(width: 5),
                Expanded(
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Container(
                      constraints: const BoxConstraints(maxWidth: 100),
                      // width: 50,
                      padding: const EdgeInsets.symmetric(
                          vertical: 4, horizontal: 7),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(15),
                        color: widget.teamMember?.isAvailable ?? false
                            ? Colors.lightGreen.shade50
                            : Colors.red.shade50,
                      ),
                      child: Center(
                        child: Text(
                          widget.teamMember?.isAvailable ?? false
                              ? 'Available'
                              : 'Unavailable',
                          style: GoogleFonts.livvic(
                              fontWeight: FontWeight.w500,
                              color: widget.teamMember?.isAvailable ?? false
                                  ? Colors.green
                                  : Colors.red,
                              fontSize: 12),
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 5),
                Expanded(
                    child: Text(widget.teamMember?.createdAt
                            .toDate()
                            .convertToDDMMYY() ??
                        "")),
                const SizedBox(width: 5),
                SizedBox(
                  width: 60,
                  child: Transform.scale(
                    scale: .65,
                    child: CupertinoSwitch(
                      value: widget.teamMember!.isActive,
                      onChanged: (value) async {
                        await FBFireStore.teamMember
                            .doc(widget.teamMember?.docId)
                            .update({'isActive': value});
                      },
                    ),
                  ),
                ),
                SizedBox(
                  width: 60,
                  child: IconButton(
                    highlightColor: Colors.transparent,
                    hoverColor: Colors.transparent,
                    onPressed: widget.onEdit,
                    icon: const Icon(
                      Icons.edit,
                      color: themeColor,
                      size: 22,
                    ),
                  ),
                ),
                SizedBox(
                  width: 60,
                  child: IconButton(
                    highlightColor: Colors.transparent,
                    hoverColor: Colors.transparent,
                    onPressed: () {
                      showDialog(
                        context: context,
                        builder: (context) {
                          bool loading = false;
                          return StatefulBuilder(
                            builder: (context, setState2) {
                              return AlertDialog(
                                backgroundColor: Colors.white,
                                surfaceTintColor: Colors.white,
                                title: const Text("Alert"),
                                content: const Text(
                                    "Are you sure you want to delete"),
                                actions: loading
                                    ? [
                                        const Center(
                                          child: SizedBox(
                                            height: 25,
                                            width: 25,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2.5,
                                            ),
                                          ),
                                        )
                                      ]
                                    : [
                                        TextButton(
                                            onPressed: () async {
                                              try {
                                                setState2(() {
                                                  loading = true;
                                                });
                                                await FBFunctions.ff
                                                    .httpsCallable(testMode
                                                        ? 'deleteTeamMemberTest'
                                                        : 'deleteTeamMember')
                                                    .call({
                                                  'uid':
                                                      widget.teamMember?.docId,
                                                });
                                                const snackBar = SnackBar(
                                                    content: Text(
                                                        "Team Member deleted successfully"));
                                                if (context.mounted) {
                                                  Navigator.of(context).pop();
                                                  setState2(() {
                                                    loading = false;
                                                  });
                                                  ScaffoldMessenger.of(context)
                                                      .showSnackBar(snackBar);
                                                }
                                              } catch (e) {
                                                debugPrint(e.toString());

                                                if (context.mounted) {
                                                  setState2(() {
                                                    loading = false;
                                                  });
                                                  Navigator.of(context).pop();
                                                }
                                              }
                                              // await FBFireStore.vendors
                                              //     .doc(widget.vendorModel?.docId)
                                              //     .delete();
                                            },
                                            child: const Text('Yes')),
                                        TextButton(
                                            onPressed: () {
                                              Navigator.of(context).pop();
                                            },
                                            child: const Text('No')),
                                      ],
                              );
                            },
                          );
                        },
                      );
                    },
                    icon: const Icon(Icons.delete,
                        // color: Colors.redAccent,
                        color: themeColor),
                  ),
                ),
              ],
            ),
          )
        : const SizedBox();
  }
}
