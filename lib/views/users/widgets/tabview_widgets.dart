import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_date_range_picker/flutter_date_range_picker.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_network/image_network.dart';
import 'package:wedding_super_admin/controller/home_ctrl.dart';
import 'package:wedding_super_admin/models/bookings_model.dart';
import 'package:wedding_super_admin/models/order_model.dart';
import 'package:wedding_super_admin/models/package_order_model.dart';
import 'package:wedding_super_admin/models/session_model.dart';
import 'package:wedding_super_admin/models/user_wishlist_model.dart';
import 'package:wedding_super_admin/models/variants.dart';
import 'package:wedding_super_admin/views/common/header_search_feild.dart';

import '../../../models/user_model.dart';
import '../../../shared/const.dart';
import '../../../shared/firebase.dart';
import '../../../shared/methods.dart';
import '../../../shared/theme.dart';
import '../../common/table_header.dart';

class UserDeliveryTabView extends StatefulWidget {
  const UserDeliveryTabView({
    super.key,
    required this.userDocId,
  });
  final String userDocId;

  @override
  State<UserDeliveryTabView> createState() => _UserDeliveryTabViewState();
}

class _UserDeliveryTabViewState extends State<UserDeliveryTabView> {
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? todaysDeliveryStream;
  final searchCtrl = TextEditingController();
  List<DeliveryDetailsModel> filteredDeliveryList = [];
  List<DeliveryDetailsModel> dropdownfilteredDeliveryList = [];
  String? selectedDropdown;
  DateTime selstartDate = DateTime.now();
  DateTime selEndDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    todayDeliveryStream();
    selectedDropdown = historyDropdownList[0];
  }

  todayDeliveryStream() {
    try {
      selstartDate = DateTime.now();
      selEndDate = DateTime.now();
      final currentStartDate =
          DateTime(selstartDate.year, selstartDate.month, selstartDate.day);
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      todaysDeliveryStream?.cancel();
      todaysDeliveryStream = FBFireStore.deliveryDetails
          .where('uId', isEqualTo: widget.userDocId)
          .where('dispatchedOn',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('dispatchedOn',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .snapshots()
          .listen((event) {
        filteredDeliveryList.clear();
        filteredDeliveryList
            .addAll(event.docs.map((e) => DeliveryDetailsModel.fromSnap(e)));
        filteredDeliveryList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        dropdownfilteredDeliveryList.clear();
        dropdownfilteredDeliveryList.addAll(filteredDeliveryList);
        if (mounted) setState(() {});
      });
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  weeklyDeliveryStream() async {
    try {
      selEndDate = DateTime.now();
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      DateTime temp = currentEndDate.subtract(Duration(days: 6));
      final currentStartDate = DateTime(temp.year, temp.month, temp.day);
      final weeklyDeliverySnap = await FBFireStore.deliveryDetails
          .where('uId', isEqualTo: widget.userDocId)
          .where('dispatchedOn',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('dispatchedOn',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .get();
      filteredDeliveryList.clear();
      filteredDeliveryList.addAll(
          weeklyDeliverySnap.docs.map((e) => DeliveryDetailsModel.fromSnap(e)));
      filteredDeliveryList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      dropdownfilteredDeliveryList.clear();
      dropdownfilteredDeliveryList.addAll(filteredDeliveryList);
      if (mounted) setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  monthlyDeliveryStream() async {
    try {
      selEndDate = DateTime.now();
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      final currentStartDate = getMonthlyStartDate(currentEndDate);
      final monthlyTransactionSnap = await FBFireStore.deliveryDetails
          .where('uId', isEqualTo: widget.userDocId)
          .where('dispatchedOn',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('dispatchedOn',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .get();
      filteredDeliveryList.clear();
      filteredDeliveryList.addAll(monthlyTransactionSnap.docs
          .map((e) => DeliveryDetailsModel.fromSnap(e)));
      filteredDeliveryList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      dropdownfilteredDeliveryList.clear();
      dropdownfilteredDeliveryList.addAll(filteredDeliveryList);
      if (mounted) setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  customDeliveryStream() async {
    try {
      final currentStartDate =
          DateTime(selstartDate.year, selstartDate.month, selstartDate.day);
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      final customTransactionSnap = await FBFireStore.deliveryDetails
          .where('uId', isEqualTo: widget.userDocId)
          .where('dispatchedOn',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('dispatchedOn',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .get();
      filteredDeliveryList.clear();
      filteredDeliveryList.addAll(customTransactionSnap.docs
          .map((e) => DeliveryDetailsModel.fromSnap(e)));
      filteredDeliveryList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      dropdownfilteredDeliveryList.clear();
      dropdownfilteredDeliveryList.addAll(filteredDeliveryList);
      if (mounted) setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  getSearchedData(String str) {
    filteredDeliveryList.clear();
    if (str.isEmpty) {
      filteredDeliveryList.addAll(dropdownfilteredDeliveryList);
    } else {
      // filteredTransactionList
      //     .addAll(dropdownfilteredTransactionList.where((element) {
      // return element.transactionId.contains(str) ||
      //     (widget.allVendors
      //             .firstWhereOrNull((e) => e.docId == element.vendorId)
      //             ?.name
      //             .toLowerCase()
      //             .contains(str) ??
      //         false);
      // }));
    }
    filteredDeliveryList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    if (mounted) setState(() {});
  }

  @override
  void dispose() {
    super.dispose();
    todaysDeliveryStream?.cancel();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              SizedBox(
                  width: 400,
                  child: SearchField(
                    searchController: searchCtrl,
                    onChanged: (value) {
                      getSearchedData(value.trim());
                    },
                  )),
              SizedBox(width: 20),
              SizedBox(
                width: 150,
                child: DropdownButtonHideUnderline(
                    child: DropdownButtonFormField(
                  value: selectedDropdown,
                  decoration: InputDecoration(
                    border: OutlineInputBorder(
                        borderSide: const BorderSide(color: Color(0xffede2de)),
                        borderRadius: BorderRadius.circular(7)),
                    enabledBorder: OutlineInputBorder(
                        borderSide: const BorderSide(color: Color(0xffede2de)),
                        borderRadius: BorderRadius.circular(7)),
                    focusedBorder: OutlineInputBorder(
                        borderSide: const BorderSide(color: Color(0xffede2de)),
                        borderRadius: BorderRadius.circular(7)),
                  ),
                  items: historyDropdownList
                      .map((e) => DropdownMenuItem(value: e, child: Text(e)))
                      .toList(),
                  onChanged: (value) async {
                    selectedDropdown = value;
                    searchCtrl.clear();
                    if (value == historyDropdownList[0]) {
                      todayDeliveryStream();
                    } else if (value == historyDropdownList[1]) {
                      weeklyDeliveryStream();
                    } else if (value == historyDropdownList[2]) {
                      monthlyDeliveryStream();
                    } else if (value == historyDropdownList[3]) {
                      showDialog(
                        context: context,
                        builder: (context) {
                          DateRange? selectedDateRange;
                          return StatefulBuilder(
                            builder: (context, setState2) {
                              return AlertDialog(
                                backgroundColor: Colors.white,
                                surfaceTintColor: Colors.white,
                                content: SizedBox(
                                    height: 400,
                                    width: 600,
                                    child: DateRangePickerWidget(
                                      minDate: DateTime(2010),
                                      maxDate: DateTime.now(),
                                      onDateRangeChanged: (value) {
                                        selectedDateRange = value;
                                        setState2(() {});
                                      },
                                    )),
                                actions: [
                                  TextButton(
                                    onPressed: () {
                                      // print("Range confirmed: $_selectedRange");
                                      if (selectedDateRange != null) {
                                        selEndDate = selectedDateRange!.end;
                                        selstartDate = selectedDateRange!.start;
                                        customDeliveryStream();
                                      }
                                      Navigator.of(context).pop();
                                    },
                                    child: Text("Confirm"),
                                  ),
                                ],
                              );
                            },
                          );
                        },
                      );
                      //   final res = await showDateRangePicker(
                      //       context: context,
                      //       firstDate: DateTime(2010),
                      //       lastDate: DateTime.now());

                      //   if (res != null) {
                      //     selstartDate = res.start;
                      //     selEndDate = res.end;
                      //     customTransactionsStream();
                      //   }
                    }
                  },
                )),
              ),
              if (selectedDropdown == historyDropdownList[3]) ...[
                SizedBox(width: 20),
                InkWell(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Date Range',
                        style: TextStyle(
                            fontSize: 13,
                            fontWeight: FontWeight.w400,
                            color: Color.fromARGB(255, 92, 92, 92)),
                      ),
                      Text(
                        '${selstartDate.goodDayDate()} - ${selEndDate.goodDayDate()}',
                        style: TextStyle(
                            fontSize: 16, fontWeight: FontWeight.w500),
                      )
                    ],
                  ),
                ),
              ]
            ],
          ),
          SizedBox(height: 20),
          Container(
            decoration: BoxDecoration(
                color: dashboardSelectedColor,
                border: Border.all(color: dividerColor),
                borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8), topRight: Radius.circular(8))

                // color: const Color.fromARGB(255, 228, 228, 228),
                // color: themeColor,
                // color: const Color.fromARGB(255, 177, 139, 86),
                // color: tableHeaderColor,
                // borderRadius: BorderRadius.circular(4),
                ),
            child: Row(
              children: [
                const SizedBox(
                  width: 80,
                  child: Text(
                    'Sr No',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      letterSpacing: 1.2,
                    ),
                  ),
                ),
                const SizedBox(width: 5),
                const Expanded(
                    child: TableHeaderText(headerName: 'Dispatch Date')),
                const SizedBox(width: 5),
                const Expanded(
                    child: TableHeaderText(headerName: 'Delivery Partner')),
                const SizedBox(width: 5),
                const Expanded(child: TableHeaderText(headerName: 'Charge')),
                const SizedBox(width: 5),
                const Expanded(child: TableHeaderText(headerName: 'Order ID')),
                const SizedBox(width: 5),
                const Expanded(child: TableHeaderText(headerName: 'Items')),
                const SizedBox(width: 5),
                const Expanded(
                    child: TableHeaderText(headerName: 'Tracking ID')),
                const SizedBox(width: 5),
                const Expanded(child: TableHeaderText(headerName: 'Status')),
                const SizedBox(width: 5),
                const Expanded(
                    child: TableHeaderText(headerName: 'Delivered On')),
                Opacity(
                  opacity: 0,
                  child: IgnorePointer(
                    ignoring: true,
                    child: SizedBox(
                      width: 60,
                      child: IconButton(
                        highlightColor: Colors.transparent,
                        hoverColor: Colors.transparent,
                        onPressed: () {},
                        icon: const Icon(
                          Icons.delete,
                          color: themeColor,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          if (filteredDeliveryList.isEmpty)
            Container(
              padding: const EdgeInsets.symmetric(vertical: 15),
              decoration: const BoxDecoration(
                  border: Border(
                      bottom: BorderSide(color: dividerColor),
                      left: BorderSide(color: dividerColor),
                      right: BorderSide(color: dividerColor)),
                  borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(8),
                      bottomRight: Radius.circular(8))),
              child: const Center(
                child: Text(
                  'No Data Available',
                  style: TextStyle(
                      color: Color(0xff737373),
                      fontSize: 14.5,
                      fontWeight: FontWeight.w500),
                ),
              ),
            ),
          ...List.generate(
            filteredDeliveryList.length,
            (index) {
              final isLast = index == filteredDeliveryList.length - 1;
              final delivery = filteredDeliveryList[index];
              return Container(
                decoration: BoxDecoration(
                    border: const Border(
                        bottom: BorderSide(color: dividerColor),
                        left: BorderSide(color: dividerColor),
                        right: BorderSide(color: dividerColor)),
                    borderRadius: isLast
                        ? const BorderRadius.only(
                            bottomLeft: Radius.circular(8),
                            bottomRight: Radius.circular(8))
                        : null),
                child: Row(
                  children: [
                    SizedBox(
                      width: 80,
                      child: Text((index + 1).toString(),
                          textAlign: TextAlign.center),
                    ),
                    const SizedBox(width: 5),
                    Expanded(
                        child: Text(
                            '${delivery.dispatchedOn?.goodDayDate() ?? '-'}  ${delivery.dispatchedOn?.goodTime() ?? ''}')),

                    const SizedBox(width: 5),
                    Expanded(child: Text(delivery.deliveryPartner)),
                    const SizedBox(width: 5),
                    Expanded(
                        child: Text(
                            '\$ ${formatPriceWithCommas(delivery.charges)}')),

                    const SizedBox(width: 5),
                    Expanded(child: Text(delivery.orderId)),
                    const SizedBox(width: 5),
                    Expanded(
                        child:
                            Text(delivery.dispatchedProduct.length.toString())),
                    const SizedBox(width: 5),
                    Expanded(child: Text(delivery.trackingId)),
                    const SizedBox(width: 5),
                    Expanded(
                        child: Text(delivery.isDelivered
                            ? 'Delivered'
                            : 'Out for Delivery')),
                    const SizedBox(width: 5),
                    Expanded(
                        child: Text(delivery.deliveredOn != null
                            ? '${delivery.deliveredOn?.goodDayDate()}  ${delivery.deliveredOn?.goodTime()}'
                            : '-')),

                    // Opacity(
                    //   opacity: 0,
                    //   child: IgnorePointer(
                    //     ignoring: true,
                    //     child: SizedBox(
                    //       width: 60,
                    //       child: IconButton(
                    //         highlightColor: Colors.transparent,
                    //         hoverColor: Colors.transparent,
                    //         onPressed: () {},
                    //         icon: const Icon(
                    //           Icons.edit,
                    //           size: 22,
                    //         ),
                    //       ),
                    //     ),
                    //   ),
                    // ),
                    Opacity(
                      opacity: 0,
                      child: SizedBox(
                        width: 60,
                        child: IconButton(
                          highlightColor: Colors.transparent,
                          hoverColor: Colors.transparent,
                          onPressed: null,
                          icon: const Icon(
                            Icons.delete,
                            color: themeColor,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          )
        ],
      ),
    );
  }
}

class UserTransactionTabView extends StatefulWidget {
  const UserTransactionTabView({
    super.key,
    required this.userDocId,
  });
  final String userDocId;

  @override
  State<UserTransactionTabView> createState() => _UserTransactionTabViewState();
}

class _UserTransactionTabViewState extends State<UserTransactionTabView> {
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>?
      todaysTransactionStream;
  final searchCtrl = TextEditingController();
  List<TransactionModel> filteredTransactionList = [];
  List<TransactionModel> dropdownfilteredTransactionList = [];
  String? selectedDropdown;
  DateTime selstartDate = DateTime.now();
  DateTime selEndDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    todaysTransactionsStream();
    selectedDropdown = historyDropdownList[0];
  }

  todaysTransactionsStream() {
    try {
      selstartDate = DateTime.now();
      selEndDate = DateTime.now();
      final currentStartDate =
          DateTime(selstartDate.year, selstartDate.month, selstartDate.day);
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      todaysTransactionStream?.cancel();
      todaysTransactionStream = FBFireStore.transaction
          .where('uId', isEqualTo: widget.userDocId)
          .where('createdAt',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('createdAt',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .snapshots()
          .listen((event) {
        filteredTransactionList.clear();
        filteredTransactionList
            .addAll(event.docs.map((e) => TransactionModel.fromSnap(e)));
        filteredTransactionList
            .sort((a, b) => b.createdAt.compareTo(a.createdAt));
        dropdownfilteredTransactionList.clear();
        dropdownfilteredTransactionList.addAll(filteredTransactionList);

        if (mounted) setState(() {});
      });
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  weeklyTransactionsStream() async {
    try {
      selEndDate = DateTime.now();
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      DateTime temp = currentEndDate.subtract(Duration(days: 6));
      final currentStartDate = DateTime(temp.year, temp.month, temp.day);
      final weeklyTransactionSnap = await FBFireStore.transaction
          .where('uId', isEqualTo: widget.userDocId)
          .where('createdAt',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('createdAt',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .get();
      filteredTransactionList.clear();
      filteredTransactionList.addAll(
          weeklyTransactionSnap.docs.map((e) => TransactionModel.fromSnap(e)));
      filteredTransactionList
          .sort((a, b) => b.createdAt.compareTo(a.createdAt));
      dropdownfilteredTransactionList.clear();
      dropdownfilteredTransactionList.addAll(filteredTransactionList);
      if (mounted) setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  monthlyTransactionsStream() async {
    try {
      selEndDate = DateTime.now();
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      final currentStartDate = getMonthlyStartDate(currentEndDate);
      final monthlyTransactionSnap = await FBFireStore.transaction
          .where('uId', isEqualTo: widget.userDocId)
          .where('createdAt',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('createdAt',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .get();
      filteredTransactionList.clear();
      filteredTransactionList.addAll(
          monthlyTransactionSnap.docs.map((e) => TransactionModel.fromSnap(e)));
      filteredTransactionList
          .sort((a, b) => b.createdAt.compareTo(a.createdAt));
      dropdownfilteredTransactionList.clear();
      dropdownfilteredTransactionList.addAll(filteredTransactionList);
      if (mounted) setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  customTransactionsStream() async {
    try {
      final currentStartDate =
          DateTime(selstartDate.year, selstartDate.month, selstartDate.day);
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      final customTransactionSnap = await FBFireStore.transaction
          .where('uId', isEqualTo: widget.userDocId)
          .where('createdAt',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('createdAt',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .get();
      filteredTransactionList.clear();
      filteredTransactionList.addAll(
          customTransactionSnap.docs.map((e) => TransactionModel.fromSnap(e)));
      filteredTransactionList
          .sort((a, b) => b.createdAt.compareTo(a.createdAt));
      dropdownfilteredTransactionList.clear();
      dropdownfilteredTransactionList.addAll(filteredTransactionList);
      if (mounted) setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  getSearchedData(String str) {
    filteredTransactionList.clear();
    if (str.isEmpty) {
      filteredTransactionList.addAll(dropdownfilteredTransactionList);
    } else {
      // filteredTransactionList
      //     .addAll(dropdownfilteredTransactionList.where((element) {
      // return element.transactionId.contains(str) ||
      //     (widget.allVendors
      //             .firstWhereOrNull((e) => e.docId == element.vendorId)
      //             ?.name
      //             .toLowerCase()
      //             .contains(str) ??
      //         false);
      // }));
    }
    filteredTransactionList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    if (mounted) setState(() {});
  }

  @override
  void dispose() {
    super.dispose();
    todaysTransactionStream?.cancel();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              SizedBox(
                  width: 400,
                  child: SearchField(
                    searchController: searchCtrl,
                    onChanged: (value) {
                      getSearchedData(value.trim());
                    },
                  )),
              SizedBox(width: 20),
              SizedBox(
                width: 150,
                child: DropdownButtonHideUnderline(
                    child: DropdownButtonFormField(
                  value: selectedDropdown,
                  decoration: InputDecoration(
                    border: OutlineInputBorder(
                        borderSide: const BorderSide(color: Color(0xffede2de)),
                        borderRadius: BorderRadius.circular(7)),
                    enabledBorder: OutlineInputBorder(
                        borderSide: const BorderSide(color: Color(0xffede2de)),
                        borderRadius: BorderRadius.circular(7)),
                    focusedBorder: OutlineInputBorder(
                        borderSide: const BorderSide(color: Color(0xffede2de)),
                        borderRadius: BorderRadius.circular(7)),
                  ),
                  items: historyDropdownList
                      .map((e) => DropdownMenuItem(value: e, child: Text(e)))
                      .toList(),
                  onChanged: (value) async {
                    selectedDropdown = value;
                    searchCtrl.clear();
                    if (value == historyDropdownList[0]) {
                      todaysTransactionsStream();
                    } else if (value == historyDropdownList[1]) {
                      weeklyTransactionsStream();
                    } else if (value == historyDropdownList[2]) {
                      monthlyTransactionsStream();
                    } else if (value == historyDropdownList[3]) {
                      showDialog(
                        context: context,
                        builder: (context) {
                          DateRange? selectedDateRange;
                          return StatefulBuilder(
                            builder: (context, setState2) {
                              return AlertDialog(
                                backgroundColor: Colors.white,
                                surfaceTintColor: Colors.white,
                                content: SizedBox(
                                    height: 400,
                                    width: 600,
                                    child: DateRangePickerWidget(
                                      minDate: DateTime(2010),
                                      maxDate: DateTime.now(),
                                      onDateRangeChanged: (value) {
                                        selectedDateRange = value;
                                        setState2(() {});
                                      },
                                    )),
                                actions: [
                                  TextButton(
                                    onPressed: () {
                                      // print("Range confirmed: $_selectedRange");
                                      if (selectedDateRange != null) {
                                        selEndDate = selectedDateRange!.end;
                                        selstartDate = selectedDateRange!.start;
                                        customTransactionsStream();
                                      }
                                      Navigator.of(context).pop();
                                    },
                                    child: Text("Confirm"),
                                  ),
                                ],
                              );
                            },
                          );
                        },
                      );
                      //   final res = await showDateRangePicker(
                      //       context: context,
                      //       firstDate: DateTime(2010),
                      //       lastDate: DateTime.now());

                      //   if (res != null) {
                      //     selstartDate = res.start;
                      //     selEndDate = res.end;
                      //     customTransactionsStream();
                      //   }
                    }
                  },
                )),
              ),
              if (selectedDropdown == historyDropdownList[3]) ...[
                SizedBox(width: 20),
                InkWell(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Date Range',
                        style: TextStyle(
                            fontSize: 13,
                            fontWeight: FontWeight.w400,
                            color: Color.fromARGB(255, 92, 92, 92)),
                      ),
                      Text(
                        '${selstartDate.goodDayDate()} - ${selEndDate.goodDayDate()}',
                        style: TextStyle(
                            fontSize: 16, fontWeight: FontWeight.w500),
                      )
                    ],
                  ),
                ),
              ]
            ],
          ),
          SizedBox(height: 20),
          Container(
            decoration: BoxDecoration(
                color: dashboardSelectedColor,
                border: Border.all(color: dividerColor),
                borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8), topRight: Radius.circular(8))

                // color: const Color.fromARGB(255, 228, 228, 228),
                // color: themeColor,
                // color: const Color.fromARGB(255, 177, 139, 86),
                // color: tableHeaderColor,
                // borderRadius: BorderRadius.circular(4),
                ),
            child: Row(
              children: [
                const SizedBox(
                  width: 80,
                  child: Text(
                    'Sr No',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      letterSpacing: 1.2,
                    ),
                  ),
                ),
                const SizedBox(width: 5),
                const Expanded(child: TableHeaderText(headerName: 'Date')),
                const SizedBox(width: 5),
                const Expanded(child: TableHeaderText(headerName: 'Amount')),
                const SizedBox(width: 5),
                const Expanded(child: TableHeaderText(headerName: 'Method')),
                const SizedBox(width: 5),
                const Expanded(child: TableHeaderText(headerName: 'Status')),
                const SizedBox(width: 5),
                const Expanded(
                    child: TableHeaderText(headerName: 'Transaction ID')),
                const SizedBox(width: 5),
                const Expanded(child: TableHeaderText(headerName: 'Paid On')),
                Opacity(
                  opacity: 0,
                  child: IgnorePointer(
                    ignoring: true,
                    child: SizedBox(
                      width: 60,
                      child: IconButton(
                        highlightColor: Colors.transparent,
                        hoverColor: Colors.transparent,
                        onPressed: () {},
                        icon: const Icon(
                          Icons.delete,
                          color: themeColor,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          if (filteredTransactionList.isEmpty)
            Container(
              padding: const EdgeInsets.symmetric(vertical: 15),
              decoration: const BoxDecoration(
                  border: Border(
                      bottom: BorderSide(color: dividerColor),
                      left: BorderSide(color: dividerColor),
                      right: BorderSide(color: dividerColor)),
                  borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(8),
                      bottomRight: Radius.circular(8))),
              child: const Center(
                child: Text(
                  'No Data Available',
                  style: TextStyle(
                      color: Color(0xff737373),
                      fontSize: 14.5,
                      fontWeight: FontWeight.w500),
                ),
              ),
            ),
          ...List.generate(
            filteredTransactionList.length,
            (index) {
              final isLast = index == filteredTransactionList.length - 1;
              final transacton = filteredTransactionList[index];
              return Container(
                decoration: BoxDecoration(
                    border: const Border(
                        bottom: BorderSide(color: dividerColor),
                        left: BorderSide(color: dividerColor),
                        right: BorderSide(color: dividerColor)),
                    borderRadius: isLast
                        ? const BorderRadius.only(
                            bottomLeft: Radius.circular(8),
                            bottomRight: Radius.circular(8))
                        : null),
                child: Row(
                  children: [
                    SizedBox(
                      width: 80,
                      child: Text((index + 1).toString(),
                          textAlign: TextAlign.center),
                    ),
                    const SizedBox(width: 5),
                    Expanded(
                        child: Text(
                            '${transacton.createdAt.goodDayDate()}  ${transacton.createdAt.goodTime()}')),

                    const SizedBox(width: 5),
                    Expanded(
                        child: Text(
                            '\$ ${formatPriceWithCommas(transacton.amount)}')),

                    const SizedBox(width: 5),
                    Expanded(
                        child: Text(transacton.method?.trim().isEmpty ?? true
                            ? '-'
                            : transacton.method ?? '-')),
                    const SizedBox(width: 5),
                    Expanded(
                        child: Text(transacton.isPaid ? 'Paid' : 'Requested')),
                    const SizedBox(width: 5),
                    Expanded(
                        child: Text(
                            transacton.transactionId?.trim().isEmpty ?? true
                                ? '-'
                                : transacton.transactionId ?? '-')),
                    const SizedBox(width: 5),
                    Expanded(
                        child: Text(transacton.paymentTime != null
                            ? '${transacton.paymentTime?.goodDayDate()}  ${transacton.paymentTime?.goodTime()}'
                            : '-')),

                    // Opacity(
                    //   opacity: 0,
                    //   child: IgnorePointer(
                    //     ignoring: true,
                    //     child: SizedBox(
                    //       width: 60,
                    //       child: IconButton(
                    //         highlightColor: Colors.transparent,
                    //         hoverColor: Colors.transparent,
                    //         onPressed: () {},
                    //         icon: const Icon(
                    //           Icons.edit,
                    //           size: 22,
                    //         ),
                    //       ),
                    //     ),
                    //   ),
                    // ),
                    Opacity(
                      opacity: 0,
                      child: SizedBox(
                        width: 60,
                        child: IconButton(
                          highlightColor: Colors.transparent,
                          hoverColor: Colors.transparent,
                          onPressed: null,
                          icon: const Icon(
                            Icons.delete,
                            color: themeColor,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          )
        ],
      ),
    );
  }
}

class UserBookingsTabView extends StatefulWidget {
  const UserBookingsTabView({
    super.key,
    required this.userDocId,
  });
  final String userDocId;

  @override
  State<UserBookingsTabView> createState() => _UserBookingsTabViewState();
}

class _UserBookingsTabViewState extends State<UserBookingsTabView> {
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? todaysBookingsStream;
  final searchCtrl = TextEditingController();
  List<BookingsModel> filteredBookingList = [];
  List<BookingsModel> dropdownfilteredBookingList = [];
  String? selectedDropdown;
  DateTime selstartDate = DateTime.now();
  DateTime selEndDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    todaysBookings();
    selectedDropdown = historyDropdownList[0];
  }

  todaysBookings() {
    try {
      selstartDate = DateTime.now();
      selEndDate = DateTime.now();
      final currentStartDate =
          DateTime(selstartDate.year, selstartDate.month, selstartDate.day);
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      todaysBookingsStream?.cancel();
      todaysBookingsStream = FBFireStore.bookings
          .where('uId', isEqualTo: widget.userDocId)
          .where('bookingDate',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('bookingDate',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .snapshots()
          .listen((event) {
        filteredBookingList.clear();
        filteredBookingList
            .addAll(event.docs.map((e) => BookingsModel.fromSnap(e)));
        filteredBookingList
            .sort((a, b) => b.bookingDate.compareTo(a.bookingDate));
        dropdownfilteredBookingList.clear();
        dropdownfilteredBookingList.addAll(filteredBookingList);

        if (mounted) setState(() {});
      });
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  weeklyBookingsStream() async {
    try {
      selEndDate = DateTime.now();
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      DateTime temp = currentEndDate.subtract(Duration(days: 6));
      final currentStartDate = DateTime(temp.year, temp.month, temp.day);
      final weeklyBookingsSnap = await FBFireStore.bookings
          .where('uId', isEqualTo: widget.userDocId)
          .where('bookingDate',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('bookingDate',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .get();
      filteredBookingList.clear();
      filteredBookingList.addAll(
          weeklyBookingsSnap.docs.map((e) => BookingsModel.fromSnap(e)));
      filteredBookingList
          .sort((a, b) => b.bookingDate.compareTo(a.bookingDate));
      dropdownfilteredBookingList.clear();
      dropdownfilteredBookingList.addAll(filteredBookingList);
      if (mounted) setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  monthlyBookingsStream() async {
    try {
      selEndDate = DateTime.now();
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      final currentStartDate = getMonthlyStartDate(currentEndDate);
      final monthlyBookingsSnap = await FBFireStore.bookings
          .where('uId', isEqualTo: widget.userDocId)
          .where('bookingDate',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('bookingDate',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .get();
      filteredBookingList.clear();
      filteredBookingList.addAll(
          monthlyBookingsSnap.docs.map((e) => BookingsModel.fromSnap(e)));
      filteredBookingList
          .sort((a, b) => b.bookingDate.compareTo(a.bookingDate));
      dropdownfilteredBookingList.clear();
      dropdownfilteredBookingList.addAll(filteredBookingList);
      if (mounted) setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  customBookingsStream() async {
    try {
      final currentStartDate =
          DateTime(selstartDate.year, selstartDate.month, selstartDate.day);
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      final customBookingsSnap = await FBFireStore.bookings
          .where('uId', isEqualTo: widget.userDocId)
          .where('bookingDate',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('bookingDate',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .get();

      filteredBookingList.clear();
      filteredBookingList.addAll(
          customBookingsSnap.docs.map((e) => BookingsModel.fromSnap(e)));
      filteredBookingList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      dropdownfilteredBookingList.clear();
      dropdownfilteredBookingList.addAll(filteredBookingList);
      if (mounted) setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  getSearchedData(String str) {
    filteredBookingList.clear();
    if (str.isEmpty) {
      filteredBookingList.addAll(dropdownfilteredBookingList);
    } else {
      // filteredTransactionList
      //     .addAll(dropdownfilteredTransactionList.where((element) {
      // return element.transactionId.contains(str) ||
      //     (widget.allVendors
      //             .firstWhereOrNull((e) => e.docId == element.vendorId)
      //             ?.name
      //             .toLowerCase()
      //             .contains(str) ??
      //         false);
      // }));
    }
    filteredBookingList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    if (mounted) setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Row(
        children: [
          SizedBox(
              width: 400,
              child: SearchField(
                searchController: searchCtrl,
                onChanged: (value) {
                  getSearchedData(value.trim());
                },
              )),
          SizedBox(width: 20),
          SizedBox(
            width: 150,
            child: DropdownButtonHideUnderline(
                child: DropdownButtonFormField(
              value: selectedDropdown,
              decoration: InputDecoration(
                border: OutlineInputBorder(
                    borderSide: const BorderSide(color: Color(0xffede2de)),
                    borderRadius: BorderRadius.circular(7)),
                enabledBorder: OutlineInputBorder(
                    borderSide: const BorderSide(color: Color(0xffede2de)),
                    borderRadius: BorderRadius.circular(7)),
                focusedBorder: OutlineInputBorder(
                    borderSide: const BorderSide(color: Color(0xffede2de)),
                    borderRadius: BorderRadius.circular(7)),
              ),
              items: historyDropdownList
                  .map((e) => DropdownMenuItem(value: e, child: Text(e)))
                  .toList(),
              onChanged: (value) async {
                selectedDropdown = value;
                searchCtrl.clear();
                if (value == historyDropdownList[0]) {
                  todaysBookings();
                } else if (value == historyDropdownList[1]) {
                  weeklyBookingsStream();
                } else if (value == historyDropdownList[2]) {
                  monthlyBookingsStream();
                } else if (value == historyDropdownList[3]) {
                  showDialog(
                    context: context,
                    builder: (context) {
                      DateRange? selectedDateRange;
                      return StatefulBuilder(
                        builder: (context, setState2) {
                          return AlertDialog(
                            backgroundColor: Colors.white,
                            surfaceTintColor: Colors.white,
                            content: SizedBox(
                                height: 400,
                                width: 600,
                                child: DateRangePickerWidget(
                                  minDate: DateTime(2010),
                                  maxDate: DateTime.now(),
                                  onDateRangeChanged: (value) {
                                    selectedDateRange = value;
                                    setState2(() {});
                                  },
                                )),
                            actions: [
                              TextButton(
                                onPressed: () {
                                  // print("Range confirmed: $_selectedRange");
                                  if (selectedDateRange != null) {
                                    selEndDate = selectedDateRange!.end;
                                    selstartDate = selectedDateRange!.start;
                                    customBookingsStream();
                                  }
                                  Navigator.of(context).pop();
                                },
                                child: Text("Confirm"),
                              ),
                            ],
                          );
                        },
                      );
                    },
                  );
                }
              },
            )),
          ),
          if (selectedDropdown == historyDropdownList[3]) ...[
            SizedBox(width: 20),
            InkWell(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Date Range',
                    style: TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.w400,
                        color: Color.fromARGB(255, 92, 92, 92)),
                  ),
                  Text(
                    '${selstartDate.goodDayDate()} - ${selEndDate.goodDayDate()}',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                  )
                ],
              ),
            ),
          ]
        ],
      ),
      SizedBox(height: 20),
      Container(
        decoration: BoxDecoration(
            color: dashboardSelectedColor,
            border: Border.all(color: dividerColor),
            borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8), topRight: Radius.circular(8))

            // color: const Color.fromARGB(255, 228, 228, 228),
            // color: themeColor,
            // color: const Color.fromARGB(255, 177, 139, 86),
            // color: tableHeaderColor,
            // borderRadius: BorderRadius.circular(4),
            ),
        child: Row(
          children: [
            const SizedBox(
              width: 80,
              child: Text(
                'Sr No',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  letterSpacing: 1.2,
                ),
              ),
            ),
            const SizedBox(width: 5),
            const Expanded(child: TableHeaderText(headerName: 'Created At')),
            const SizedBox(width: 5),
            const Expanded(child: TableHeaderText(headerName: 'Booking Date')),
            const SizedBox(width: 5),
            const Expanded(
                child: TableHeaderText(headerName: 'Slot Time & Duration')),
            const SizedBox(width: 5),
            const Expanded(child: TableHeaderText(headerName: 'Consultant')),
            const SizedBox(width: 5),
            const Expanded(child: TableHeaderText(headerName: 'Styler')),
            const SizedBox(width: 5),
            const Expanded(child: TableHeaderText(headerName: 'Status')),
            // const SizedBox(width: 5),
            // const Expanded(child: TableHeaderText(headerName: 'Completed On')),
            Opacity(
              opacity: 0,
              child: IgnorePointer(
                ignoring: true,
                child: SizedBox(
                  width: 60,
                  child: IconButton(
                    highlightColor: Colors.transparent,
                    hoverColor: Colors.transparent,
                    onPressed: () {},
                    icon: const Icon(
                      Icons.delete,
                      color: themeColor,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      if (filteredBookingList.isEmpty)
        Container(
          padding: const EdgeInsets.symmetric(vertical: 15),
          decoration: const BoxDecoration(
              border: Border(
                  bottom: BorderSide(color: dividerColor),
                  left: BorderSide(color: dividerColor),
                  right: BorderSide(color: dividerColor)),
              borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(8),
                  bottomRight: Radius.circular(8))),
          child: const Center(
            child: Text(
              'No Data Available',
              style: TextStyle(
                  color: Color(0xff737373),
                  fontSize: 14.5,
                  fontWeight: FontWeight.w500),
            ),
          ),
        ),
      ...List.generate(
        filteredBookingList.length,
        (index) {
          final isLast = index == filteredBookingList.length - 1;
          final bookingData = filteredBookingList[index];
          final teamCtrl = Get.find<HomeCtrl>().teamMembers;
          final consultant = teamCtrl.firstWhereOrNull(
              (element) => element.docId == bookingData.teamMemberId);
          final styler = teamCtrl.firstWhereOrNull(
              (element) => element.docId == bookingData.stylerId);
          return Container(
            decoration: BoxDecoration(
                border: const Border(
                    bottom: BorderSide(color: dividerColor),
                    left: BorderSide(color: dividerColor),
                    right: BorderSide(color: dividerColor)),
                borderRadius: isLast
                    ? const BorderRadius.only(
                        bottomLeft: Radius.circular(8),
                        bottomRight: Radius.circular(8))
                    : null),
            child: Row(
              children: [
                SizedBox(
                  width: 80,
                  child:
                      Text((index + 1).toString(), textAlign: TextAlign.center),
                ),
                const SizedBox(width: 5),
                Expanded(
                    child: Text(
                        '${bookingData.createdAt.goodDayDate()}  ${bookingData.createdAt.goodTime()}')),

                const SizedBox(width: 5),
                Expanded(child: Text(bookingData.bookingDate.goodDayDate())),

                const SizedBox(width: 5),
                Expanded(
                    child: Text(
                        '${bookingData.bookingstartTime.goodTime()} - ${bookingData.bookingendTime.goodTime()}, ${bookingData.slotgap}')),
                const SizedBox(width: 5),
                Expanded(
                    child:
                        Text(capilatlizeFirstLetter(consultant?.name ?? '-'))),
                const SizedBox(width: 5),
                Expanded(
                    child: Text(capilatlizeFirstLetter(styler?.name ?? '-'))),
                const SizedBox(width: 5),
                Expanded(
                    child: Text(bookingData.cancelledAt != null
                        ? 'Cancelled'
                        : bookingData.completedAt != null
                            ? 'Completed'
                            : 'pending')),
                // const SizedBox(width: 5),
                // Expanded(
                //     child: Text(inquiry.endedOn != null
                //         ? '${inquiry.endedOn?.goodDayDate()}  ${inquiry.endedOn?.goodTime()}'
                //         : '-')),

                // Opacity(
                //   opacity: 0,
                //   child: IgnorePointer(
                //     ignoring: true,
                //     child: SizedBox(
                //       width: 60,
                //       child: IconButton(
                //         highlightColor: Colors.transparent,
                //         hoverColor: Colors.transparent,
                //         onPressed: () {},
                //         icon: const Icon(
                //           Icons.edit,
                //           size: 22,
                //         ),
                //       ),
                //     ),
                //   ),
                // ),
                Opacity(
                  opacity: 0,
                  child: SizedBox(
                    width: 60,
                    child: IconButton(
                      highlightColor: Colors.transparent,
                      hoverColor: Colors.transparent,
                      onPressed: null,
                      icon: const Icon(
                        Icons.delete,
                        color: themeColor,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      )
    ]));
  }
}

class UserInquiriesTabView extends StatefulWidget {
  const UserInquiriesTabView({
    super.key,
    required this.userDocId,
  });
  final String userDocId;

  @override
  State<UserInquiriesTabView> createState() => _UserInquiriesTabViewState();
}

class _UserInquiriesTabViewState extends State<UserInquiriesTabView> {
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>?
      todaysInquiriesStream;
  final searchCtrl = TextEditingController();
  List<SessionModel> filteredInquiriesList = [];
  List<SessionModel> dropdownfilteredInquiriesList = [];
  String? selectedDropdown;
  DateTime selstartDate = DateTime.now();
  DateTime selEndDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    todaysInquiries();
    selectedDropdown = historyDropdownList[0];
  }

  todaysInquiries() {
    try {
      selstartDate = DateTime.now();
      selEndDate = DateTime.now();
      final currentStartDate =
          DateTime(selstartDate.year, selstartDate.month, selstartDate.day);
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      todaysInquiriesStream?.cancel();
      todaysInquiriesStream = FBFireStore.sessions
          .where('uId', isEqualTo: widget.userDocId)
          .where('createdAt',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('createdAt',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .snapshots()
          .listen((event) {
        filteredInquiriesList.clear();
        filteredInquiriesList
            .addAll(event.docs.map((e) => SessionModel.fromSnap(e)));
        filteredInquiriesList
            .sort((a, b) => b.createdAt.compareTo(a.createdAt));
        dropdownfilteredInquiriesList.clear();
        dropdownfilteredInquiriesList.addAll(filteredInquiriesList);

        if (mounted) setState(() {});
      });
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  weeklyInquiriesStream() async {
    try {
      selEndDate = DateTime.now();
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      DateTime temp = currentEndDate.subtract(Duration(days: 6));
      final currentStartDate = DateTime(temp.year, temp.month, temp.day);
      final weeklyInquiriesSnap = await FBFireStore.sessions
          .where('uId', isEqualTo: widget.userDocId)
          .where('createdAt',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('createdAt',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .get();
      filteredInquiriesList.clear();
      filteredInquiriesList.addAll(
          weeklyInquiriesSnap.docs.map((e) => SessionModel.fromSnap(e)));
      filteredInquiriesList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      dropdownfilteredInquiriesList.clear();
      dropdownfilteredInquiriesList.addAll(filteredInquiriesList);
      if (mounted) setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  monthlyInquiriesStream() async {
    try {
      selEndDate = DateTime.now();
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      final currentStartDate = getMonthlyStartDate(currentEndDate);
      final monthlyInquiriesSnap = await FBFireStore.sessions
          .where('uId', isEqualTo: widget.userDocId)
          .where('createdAt',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('createdAt',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .get();
      filteredInquiriesList.clear();
      filteredInquiriesList.addAll(
          monthlyInquiriesSnap.docs.map((e) => SessionModel.fromSnap(e)));
      filteredInquiriesList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      dropdownfilteredInquiriesList.clear();
      dropdownfilteredInquiriesList.addAll(filteredInquiriesList);
      if (mounted) setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  customInquiriesStream() async {
    try {
      final currentStartDate =
          DateTime(selstartDate.year, selstartDate.month, selstartDate.day);
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      final customInquiriesSnap = await FBFireStore.sessions
          .where('uId', isEqualTo: widget.userDocId)
          .where('createdAt',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('createdAt',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .get();
      filteredInquiriesList.clear();
      filteredInquiriesList.addAll(
          customInquiriesSnap.docs.map((e) => SessionModel.fromSnap(e)));
      filteredInquiriesList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      dropdownfilteredInquiriesList.clear();
      dropdownfilteredInquiriesList.addAll(filteredInquiriesList);
      if (mounted) setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  getSearchedData(String str) {
    filteredInquiriesList.clear();
    if (str.isEmpty) {
      filteredInquiriesList.addAll(dropdownfilteredInquiriesList);
    } else {
      // filteredTransactionList
      //     .addAll(dropdownfilteredTransactionList.where((element) {
      // return element.transactionId.contains(str) ||
      //     (widget.allVendors
      //             .firstWhereOrNull((e) => e.docId == element.vendorId)
      //             ?.name
      //             .toLowerCase()
      //             .contains(str) ??
      //         false);
      // }));
    }
    filteredInquiriesList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    if (mounted) setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Row(
        children: [
          SizedBox(
              width: 400,
              child: SearchField(
                searchController: searchCtrl,
                onChanged: (value) {
                  getSearchedData(value.trim());
                },
              )),
          SizedBox(width: 20),
          SizedBox(
            width: 150,
            child: DropdownButtonHideUnderline(
                child: DropdownButtonFormField(
              value: selectedDropdown,
              decoration: InputDecoration(
                border: OutlineInputBorder(
                    borderSide: const BorderSide(color: Color(0xffede2de)),
                    borderRadius: BorderRadius.circular(7)),
                enabledBorder: OutlineInputBorder(
                    borderSide: const BorderSide(color: Color(0xffede2de)),
                    borderRadius: BorderRadius.circular(7)),
                focusedBorder: OutlineInputBorder(
                    borderSide: const BorderSide(color: Color(0xffede2de)),
                    borderRadius: BorderRadius.circular(7)),
              ),
              items: historyDropdownList
                  .map((e) => DropdownMenuItem(value: e, child: Text(e)))
                  .toList(),
              onChanged: (value) async {
                selectedDropdown = value;
                searchCtrl.clear();
                if (value == historyDropdownList[0]) {
                  todaysInquiries();
                } else if (value == historyDropdownList[1]) {
                  weeklyInquiriesStream();
                } else if (value == historyDropdownList[2]) {
                  monthlyInquiriesStream();
                } else if (value == historyDropdownList[3]) {
                  showDialog(
                    context: context,
                    builder: (context) {
                      DateRange? selectedDateRange;
                      return StatefulBuilder(
                        builder: (context, setState2) {
                          return AlertDialog(
                            backgroundColor: Colors.white,
                            surfaceTintColor: Colors.white,
                            content: SizedBox(
                                height: 400,
                                width: 600,
                                child: DateRangePickerWidget(
                                  minDate: DateTime(2010),
                                  maxDate: DateTime.now(),
                                  onDateRangeChanged: (value) {
                                    selectedDateRange = value;
                                    setState2(() {});
                                  },
                                )),
                            actions: [
                              TextButton(
                                onPressed: () {
                                  // print("Range confirmed: $_selectedRange");
                                  if (selectedDateRange != null) {
                                    selEndDate = selectedDateRange!.end;
                                    selstartDate = selectedDateRange!.start;
                                    customInquiriesStream();
                                  }
                                  Navigator.of(context).pop();
                                },
                                child: Text("Confirm"),
                              ),
                            ],
                          );
                        },
                      );
                    },
                  );
                }
              },
            )),
          ),
          if (selectedDropdown == historyDropdownList[3]) ...[
            SizedBox(width: 20),
            InkWell(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Date Range',
                    style: TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.w400,
                        color: Color.fromARGB(255, 92, 92, 92)),
                  ),
                  Text(
                    '${selstartDate.goodDayDate()} - ${selEndDate.goodDayDate()}',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                  )
                ],
              ),
            ),
          ]
        ],
      ),
      SizedBox(height: 20),
      Container(
        decoration: BoxDecoration(
            color: dashboardSelectedColor,
            border: Border.all(color: dividerColor),
            borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8), topRight: Radius.circular(8))

            // color: const Color.fromARGB(255, 228, 228, 228),
            // color: themeColor,
            // color: const Color.fromARGB(255, 177, 139, 86),
            // color: tableHeaderColor,
            // borderRadius: BorderRadius.circular(4),
            ),
        child: Row(
          children: [
            const SizedBox(
              width: 80,
              child: Text(
                'Sr No',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  letterSpacing: 1.2,
                ),
              ),
            ),
            const SizedBox(width: 5),
            const Expanded(child: TableHeaderText(headerName: 'Created At')),
            const SizedBox(width: 5),
            const Expanded(child: TableHeaderText(headerName: 'Inquiry ID')),
            const SizedBox(width: 5),
            const Expanded(child: TableHeaderText(headerName: 'Consultant')),
            const SizedBox(width: 5),
            const Expanded(child: TableHeaderText(headerName: 'Styler')),
            const SizedBox(width: 5),
            const Expanded(child: TableHeaderText(headerName: 'Status')),
            const SizedBox(width: 5),
            const Expanded(child: TableHeaderText(headerName: 'Completed On')),
            Opacity(
              opacity: 0,
              child: IgnorePointer(
                ignoring: true,
                child: SizedBox(
                  width: 60,
                  child: IconButton(
                    highlightColor: Colors.transparent,
                    hoverColor: Colors.transparent,
                    onPressed: () {},
                    icon: const Icon(
                      Icons.delete,
                      color: themeColor,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      if (filteredInquiriesList.isEmpty)
        Container(
          padding: const EdgeInsets.symmetric(vertical: 15),
          decoration: const BoxDecoration(
              border: Border(
                  bottom: BorderSide(color: dividerColor),
                  left: BorderSide(color: dividerColor),
                  right: BorderSide(color: dividerColor)),
              borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(8),
                  bottomRight: Radius.circular(8))),
          child: const Center(
            child: Text(
              'No Data Available',
              style: TextStyle(
                  color: Color(0xff737373),
                  fontSize: 14.5,
                  fontWeight: FontWeight.w500),
            ),
          ),
        ),
      ...List.generate(
        filteredInquiriesList.length,
        (index) {
          final isLast = index == filteredInquiriesList.length - 1;
          final inquiry = filteredInquiriesList[index];
          final teamCtrl = Get.find<HomeCtrl>().teamMembers;
          final teamMember = teamCtrl.firstWhereOrNull(
              (element) => element.docId == inquiry.consultantId);
          final styler = teamCtrl
              .firstWhereOrNull((element) => element.docId == inquiry.stylerId);
          return Container(
            decoration: BoxDecoration(
                border: const Border(
                    bottom: BorderSide(color: dividerColor),
                    left: BorderSide(color: dividerColor),
                    right: BorderSide(color: dividerColor)),
                borderRadius: isLast
                    ? const BorderRadius.only(
                        bottomLeft: Radius.circular(8),
                        bottomRight: Radius.circular(8))
                    : null),
            child: Row(
              children: [
                SizedBox(
                  width: 80,
                  child:
                      Text((index + 1).toString(), textAlign: TextAlign.center),
                ),
                const SizedBox(width: 5),
                Expanded(
                    child: Text(
                        '${inquiry.createdAt.goodDayDate()}  ${inquiry.createdAt.goodTime()}')),

                const SizedBox(width: 5),
                Expanded(child: Text(inquiry.inquiryId)),

                const SizedBox(width: 5),
                Expanded(
                    child:
                        Text(capilatlizeFirstLetter(teamMember?.name ?? '-'))),
                const SizedBox(width: 5),
                Expanded(
                    child: Text(capilatlizeFirstLetter(styler?.name ?? '-'))),
                const SizedBox(width: 5),
                Expanded(
                    child: Text(inquiry.isActive ? 'Pending' : 'Completed')),
                const SizedBox(width: 5),
                Expanded(
                    child: Text(inquiry.endedOn != null
                        ? '${inquiry.endedOn?.goodDayDate()}  ${inquiry.endedOn?.goodTime()}'
                        : '-')),

                // Opacity(
                //   opacity: 0,
                //   child: IgnorePointer(
                //     ignoring: true,
                //     child: SizedBox(
                //       width: 60,
                //       child: IconButton(
                //         highlightColor: Colors.transparent,
                //         hoverColor: Colors.transparent,
                //         onPressed: () {},
                //         icon: const Icon(
                //           Icons.edit,
                //           size: 22,
                //         ),
                //       ),
                //     ),
                //   ),
                // ),
                Opacity(
                  opacity: 0,
                  child: SizedBox(
                    width: 60,
                    child: IconButton(
                      highlightColor: Colors.transparent,
                      hoverColor: Colors.transparent,
                      onPressed: null,
                      icon: const Icon(
                        Icons.delete,
                        color: themeColor,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      )
    ]));
  }
}

class UserOrdersTabView extends StatefulWidget {
  const UserOrdersTabView({
    super.key,
    required this.userDocId,
  });
  final String userDocId;

  @override
  State<UserOrdersTabView> createState() => _UserOrdersTabViewState();
}

class _UserOrdersTabViewState extends State<UserOrdersTabView> {
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? todaysOrdersStream;
  final searchCtrl = TextEditingController();
  List<OrderModel> filteredOrdersList = [];
  List<OrderModel> dropdownfilteredOrdersList = [];
  String? selectedDropdown;
  DateTime selstartDate = DateTime.now();
  DateTime selEndDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    todaysOrders();
    selectedDropdown = historyDropdownList[0];
  }

  todaysOrders() {
    try {
      selstartDate = DateTime.now();
      selEndDate = DateTime.now();
      final currentStartDate =
          DateTime(selstartDate.year, selstartDate.month, selstartDate.day);
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      todaysOrdersStream?.cancel();
      todaysOrdersStream = FBFireStore.orders
          .where('uid', isEqualTo: widget.userDocId)
          .where('createdAt',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('createdAt',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .snapshots()
          .listen((event) {
        filteredOrdersList.clear();
        filteredOrdersList
            .addAll(event.docs.map((e) => OrderModel.fromSnap(e)));
        filteredOrdersList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        dropdownfilteredOrdersList.clear();
        dropdownfilteredOrdersList.addAll(filteredOrdersList);

        if (mounted) setState(() {});
      });
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  weeklyOrdersStream() async {
    try {
      selEndDate = DateTime.now();
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      DateTime temp = currentEndDate.subtract(Duration(days: 6));
      final currentStartDate = DateTime(temp.year, temp.month, temp.day);
      final weeklyOrderSnap = await FBFireStore.orders
          .where('uid', isEqualTo: widget.userDocId)
          .where('createdAt',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('createdAt',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .get();
      filteredOrdersList.clear();
      filteredOrdersList
          .addAll(weeklyOrderSnap.docs.map((e) => OrderModel.fromSnap(e)));
      filteredOrdersList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      dropdownfilteredOrdersList.clear();
      dropdownfilteredOrdersList.addAll(filteredOrdersList);
      if (mounted) setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  monthlyOrdersStream() async {
    try {
      selEndDate = DateTime.now();
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      final currentStartDate = getMonthlyStartDate(currentEndDate);
      final monthlyOrdersSnap = await FBFireStore.orders
          .where('uid', isEqualTo: widget.userDocId)
          .where('createdAt',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('createdAt',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .get();
      filteredOrdersList.clear();
      filteredOrdersList
          .addAll(monthlyOrdersSnap.docs.map((e) => OrderModel.fromSnap(e)));
      filteredOrdersList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      dropdownfilteredOrdersList.clear();
      dropdownfilteredOrdersList.addAll(filteredOrdersList);
      if (mounted) setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  customOrdersStream() async {
    try {
      final currentStartDate =
          DateTime(selstartDate.year, selstartDate.month, selstartDate.day);
      final currentEndDate = DateTime(
          selEndDate.year, selEndDate.month, selEndDate.day, 23, 59, 59);
      final customOrdersSnap = await FBFireStore.orders
          .where('uid', isEqualTo: widget.userDocId)
          .where('createdAt',
              isGreaterThanOrEqualTo: currentStartDate.millisecondsSinceEpoch)
          .where('createdAt',
              isLessThanOrEqualTo: currentEndDate.millisecondsSinceEpoch)
          .get();
      filteredOrdersList.clear();
      filteredOrdersList
          .addAll(customOrdersSnap.docs.map((e) => OrderModel.fromSnap(e)));
      filteredOrdersList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      dropdownfilteredOrdersList.clear();
      dropdownfilteredOrdersList.addAll(filteredOrdersList);
      if (mounted) setState(() {});
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  getSearchedData(String str) {
    filteredOrdersList.clear();
    if (str.isEmpty) {
      filteredOrdersList.addAll(dropdownfilteredOrdersList);
    } else {
      // filteredTransactionList
      //     .addAll(dropdownfilteredTransactionList.where((element) {
      // return element.transactionId.contains(str) ||
      //     (widget.allVendors
      //             .firstWhereOrNull((e) => e.docId == element.vendorId)
      //             ?.name
      //             .toLowerCase()
      //             .contains(str) ??
      //         false);
      // }));
    }
    filteredOrdersList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    if (mounted) setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              SizedBox(
                  width: 400,
                  child: SearchField(
                    searchController: searchCtrl,
                    onChanged: (value) {
                      getSearchedData(value.trim());
                    },
                  )),
              SizedBox(width: 20),
              SizedBox(
                width: 150,
                child: DropdownButtonHideUnderline(
                    child: DropdownButtonFormField(
                  value: selectedDropdown,
                  decoration: InputDecoration(
                    border: OutlineInputBorder(
                        borderSide: const BorderSide(color: Color(0xffede2de)),
                        borderRadius: BorderRadius.circular(7)),
                    enabledBorder: OutlineInputBorder(
                        borderSide: const BorderSide(color: Color(0xffede2de)),
                        borderRadius: BorderRadius.circular(7)),
                    focusedBorder: OutlineInputBorder(
                        borderSide: const BorderSide(color: Color(0xffede2de)),
                        borderRadius: BorderRadius.circular(7)),
                  ),
                  items: historyDropdownList
                      .map((e) => DropdownMenuItem(value: e, child: Text(e)))
                      .toList(),
                  onChanged: (value) async {
                    selectedDropdown = value;
                    searchCtrl.clear();
                    if (value == historyDropdownList[0]) {
                      todaysOrders();
                    } else if (value == historyDropdownList[1]) {
                      weeklyOrdersStream();
                    } else if (value == historyDropdownList[2]) {
                      monthlyOrdersStream();
                    } else if (value == historyDropdownList[3]) {
                      showDialog(
                        context: context,
                        builder: (context) {
                          DateRange? selectedDateRange;
                          return StatefulBuilder(
                            builder: (context, setState2) {
                              return AlertDialog(
                                backgroundColor: Colors.white,
                                surfaceTintColor: Colors.white,
                                content: SizedBox(
                                    height: 400,
                                    width: 600,
                                    child: DateRangePickerWidget(
                                      minDate: DateTime(2010),
                                      maxDate: DateTime.now(),
                                      onDateRangeChanged: (value) {
                                        selectedDateRange = value;
                                        setState2(() {});
                                      },
                                    )),
                                actions: [
                                  TextButton(
                                    onPressed: () {
                                      // print("Range confirmed: $_selectedRange");
                                      if (selectedDateRange != null) {
                                        selEndDate = selectedDateRange!.end;
                                        selstartDate = selectedDateRange!.start;
                                        customOrdersStream();
                                      }
                                      Navigator.of(context).pop();
                                    },
                                    child: Text("Confirm"),
                                  ),
                                ],
                              );
                            },
                          );
                        },
                      );
                    }
                  },
                )),
              ),
              if (selectedDropdown == historyDropdownList[3]) ...[
                SizedBox(width: 20),
                InkWell(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Date Range',
                        style: TextStyle(
                            fontSize: 13,
                            fontWeight: FontWeight.w400,
                            color: Color.fromARGB(255, 92, 92, 92)),
                      ),
                      Text(
                        '${selstartDate.goodDayDate()} - ${selEndDate.goodDayDate()}',
                        style: TextStyle(
                            fontSize: 16, fontWeight: FontWeight.w500),
                      )
                    ],
                  ),
                ),
              ]
            ],
          ),
          SizedBox(height: 20),
          Container(
            decoration: BoxDecoration(
                color: dashboardSelectedColor,
                border: Border.all(color: dividerColor),
                borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8), topRight: Radius.circular(8))

                // color: const Color.fromARGB(255, 228, 228, 228),
                // color: themeColor,
                // color: const Color.fromARGB(255, 177, 139, 86),
                // color: tableHeaderColor,
                // borderRadius: BorderRadius.circular(4),
                ),
            child: Row(
              children: [
                const SizedBox(
                  width: 80,
                  child: Text(
                    'Sr No',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      letterSpacing: 1.2,
                    ),
                  ),
                ),
                const SizedBox(width: 5),
                const Expanded(
                    child: TableHeaderText(headerName: 'Created At')),
                const SizedBox(width: 5),
                const Expanded(child: TableHeaderText(headerName: 'Order ID')),
                const SizedBox(width: 5),
                const Expanded(
                    child: TableHeaderText(headerName: 'Total Amount')),
                const SizedBox(width: 5),
                const Expanded(
                    child: TableHeaderText(headerName: 'Paid Amount')),
                const SizedBox(width: 5),
                const Expanded(child: TableHeaderText(headerName: 'Items')),
                const SizedBox(width: 5),
                const Expanded(child: TableHeaderText(headerName: 'Status')),
                // const SizedBox(width: 5),
                // const Expanded(child: TableHeaderText(headerName: 'Completed On')),
                Opacity(
                  opacity: 0,
                  child: IgnorePointer(
                    ignoring: true,
                    child: SizedBox(
                      width: 60,
                      child: IconButton(
                        highlightColor: Colors.transparent,
                        hoverColor: Colors.transparent,
                        onPressed: () {},
                        icon: const Icon(
                          Icons.delete,
                          color: themeColor,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          if (filteredOrdersList.isEmpty)
            Container(
              padding: const EdgeInsets.symmetric(vertical: 15),
              decoration: const BoxDecoration(
                  border: Border(
                      bottom: BorderSide(color: dividerColor),
                      left: BorderSide(color: dividerColor),
                      right: BorderSide(color: dividerColor)),
                  borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(8),
                      bottomRight: Radius.circular(8))),
              child: const Center(
                child: Text(
                  'No Data Available',
                  style: TextStyle(
                      color: Color(0xff737373),
                      fontSize: 14.5,
                      fontWeight: FontWeight.w500),
                ),
              ),
            ),
          ...List.generate(
            filteredOrdersList.length,
            (index) {
              final isLast = index == filteredOrdersList.length - 1;
              final orderData = filteredOrdersList[index];
              return Container(
                decoration: BoxDecoration(
                    border: const Border(
                        bottom: BorderSide(color: dividerColor),
                        left: BorderSide(color: dividerColor),
                        right: BorderSide(color: dividerColor)),
                    borderRadius: isLast
                        ? const BorderRadius.only(
                            bottomLeft: Radius.circular(8),
                            bottomRight: Radius.circular(8))
                        : null),
                child: Row(
                  children: [
                    SizedBox(
                      width: 80,
                      child: Text((index + 1).toString(),
                          textAlign: TextAlign.center),
                    ),
                    const SizedBox(width: 5),
                    Expanded(
                        child: Text(
                            '${orderData.createdAt.goodDayDate()}  ${orderData.createdAt.goodTime()}')),

                    const SizedBox(width: 5),
                    Expanded(child: Text(orderData.orderId)),

                    const SizedBox(width: 5),
                    Expanded(
                        child: Text(
                            '\$ ${formatPriceWithCommas(orderData.totalAmount)}')),
                    const SizedBox(width: 5),
                    Expanded(
                        child: Text(
                            '\$ ${formatPriceWithCommas(orderData.paidAmount)}')),
                    const SizedBox(width: 5),
                    Expanded(
                        child: Text(capilatlizeFirstLetter(
                            orderData.orderProductData.length.toString()))),
                    const SizedBox(width: 5),
                    Expanded(child: Text(orderData.orderStatus)),
                    // const SizedBox(width: 5),
                    // Expanded(
                    //     child: Text(inquiry.endedOn != null
                    //         ? '${inquiry.endedOn?.goodDayDate()}  ${inquiry.endedOn?.goodTime()}'
                    //         : '-')),

                    // Opacity(
                    //   opacity: 0,
                    //   child: IgnorePointer(
                    //     ignoring: true,
                    //     child: SizedBox(
                    //       width: 60,
                    //       child: IconButton(
                    //         highlightColor: Colors.transparent,
                    //         hoverColor: Colors.transparent,
                    //         onPressed: () {},
                    //         icon: const Icon(
                    //           Icons.edit,
                    //           size: 22,
                    //         ),
                    //       ),
                    //     ),
                    //   ),
                    // ),
                    Opacity(
                      opacity: 0,
                      child: SizedBox(
                        width: 60,
                        child: IconButton(
                          highlightColor: Colors.transparent,
                          hoverColor: Colors.transparent,
                          onPressed: null,
                          icon: const Icon(
                            Icons.delete,
                            color: themeColor,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          )
        ],
      ),
    );
  }
}

class UserDetailsTabView extends StatefulWidget {
  const UserDetailsTabView({super.key, required this.userDocId});
  final String userDocId;
  @override
  State<UserDetailsTabView> createState() => _UserDetailsTabViewState();
}

class _UserDetailsTabViewState extends State<UserDetailsTabView> {
  @override
  Widget build(BuildContext context) {
    return StreamBuilder(
      stream: FBFireStore.users.doc(widget.userDocId).snapshots(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Center(
            child: SizedBox(
                height: 25,
                width: 25,
                child: CircularProgressIndicator(strokeWidth: 3.5)),
          );
        }
        if (snapshot.hasError) {
          debugPrint(snapshot.error.toString());
          return Center(
            child: Text("Unable to load data"),
          );
        }
        if (snapshot.hasData) {
          final userModel = snapshot.data != null
              ? UserModel.fromDocSnap(snapshot.data!)
              : null;

          return userModel != null
              ? SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      StaggeredGrid.extent(
                        maxCrossAxisExtent: 350,
                        crossAxisSpacing: 15,
                        mainAxisSpacing: 15,

                        // crossAxisAlignment: WrapCrossAlignment.start,
                        children: [
                          Container(
                            padding: EdgeInsets.all(10),
                            decoration: BoxDecoration(
                              color: Colors.grey.shade50,
                              border: Border(
                                  bottom: BorderSide(
                                      color: themeColor.withValues(alpha: .8))),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "Name",
                                  style: TextStyle(
                                      // fontWeight: FontWeight.w00,
                                      fontSize: 14.5,
                                      color: Color.fromARGB(255, 93, 93, 93)),
                                ),
                                SizedBox(height: 5),
                                Text(
                                  userModel.name!.trim().isNotEmpty
                                      ? capilatlizeFirstLetter(userModel.name!)
                                      : '-',
                                  style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                      color: Color(0xff3F3F3F)),
                                ),
                              ],
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.all(10),
                            decoration: BoxDecoration(
                              color: Colors.grey.shade50,
                              border: Border(
                                  bottom: BorderSide(
                                      color: themeColor.withValues(alpha: .8))),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "Email",
                                  style: TextStyle(
                                      // fontWeight: FontWeight.w00,
                                      fontSize: 14.5,
                                      color: Color.fromARGB(255, 93, 93, 93)),
                                ),
                                SizedBox(height: 5),
                                Text(
                                  userModel.email.trim().isNotEmpty
                                      ? userModel.email
                                      : '-',
                                  style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                      color: Color(0xff3F3F3F)),
                                ),
                              ],
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.all(10),
                            decoration: BoxDecoration(
                              color: Colors.grey.shade50,
                              border: Border(
                                  bottom: BorderSide(
                                      color: themeColor.withValues(alpha: .8))),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "Mobile",
                                  style: TextStyle(
                                      // fontWeight: FontWeight.w00,
                                      fontSize: 14.5,
                                      color: Color.fromARGB(255, 93, 93, 93)),
                                ),
                                SizedBox(height: 5),
                                Text(
                                  userModel.phone!.trim().isNotEmpty
                                      ? userModel.phone!
                                      : '-',
                                  style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                      color: Color(0xff3F3F3F)),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 25),
                      Text(
                        'Addresses',
                        style:
                            GoogleFonts.mulish(fontSize: 17, color: themeColor),
                      ),
                      SizedBox(height: 13),
                      StaggeredGrid.extent(
                        maxCrossAxisExtent: 350,
                        crossAxisSpacing: 15,
                        mainAxisSpacing: 15,
                        children: [
                          ...List.generate(
                            userModel.addresses.length,
                            (index) {
                              final address = userModel.addresses[index];
                              return Container(
                                padding: EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8),
                                  color: Colors.grey.shade50,
                                  // color: const Color(0xfff8fafc),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      "Address ${index + 1}",
                                      style: TextStyle(
                                          color: Colors.black,
                                          fontSize: 14,
                                          fontWeight: FontWeight.w600),
                                    ),
                                    SizedBox(height: 5),
                                    Text(
                                      '${address.flatHouse}, ${address.area}, ${address.city}, ${address.state}, ${address.pincode}',
                                      style: TextStyle(
                                          color: Color(0xff3C3C3C),
                                          fontSize: 15.5,
                                          fontWeight: FontWeight.w500),
                                    ),
                                  ],
                                ),
                              );
                            },
                          )
                        ],
                      )
                    ],
                  ),
                )
              : Center(
                  child: Text("No data"),
                );
        }
        return Center(
          child: Text("Something went wrong"),
        );
      },
    );
  }
}

class UserPackageView extends StatefulWidget {
  const UserPackageView({super.key, required this.userDocId});
  final String userDocId;

  @override
  State<UserPackageView> createState() => _UserPackageViewState();
}

class _UserPackageViewState extends State<UserPackageView> {
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? userPackagesStream;
  List<PackagePurchaseModel> userPackageList = [];

  @override
  void initState() {
    super.initState();
    userPackages();
  }

  userPackages() {
    try {
      userPackagesStream?.cancel();
      userPackagesStream = FBFireStore.packageOrders
          .where('uid', isEqualTo: widget.userDocId)
          .snapshots()
          .listen((event) {
        userPackageList.clear();
        userPackageList
            .addAll(event.docs.map((e) => PackagePurchaseModel.fromSnap(e)));
        userPackageList.sort((a, b) => b.createdAt.compareTo(a.createdAt));

        if (mounted) setState(() {});
      });
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          ...List.generate(
            userPackageList.length,
            (index) {
              final package = userPackageList[index];
              final isActive = package.isActive;
              final remainingDays = package.validity -
                  DateTime.now().difference(package.purchaseAt).inDays;
              return Padding(
                padding: EdgeInsets.only(bottom: index == 2 ? 0 : 20.0),
                child: Container(
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: Colors.white,
                    border: Border.all(color: Color(0xffe5e7eb), width: 1),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.shade100,
                        blurRadius: 7,
                        offset: Offset(1, 2),
                      ),
                    ],
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  package.packageName,
                                  style: TextStyle(
                                      color: Colors.black,
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600),
                                ),
                                SizedBox(width: 13),
                                Container(
                                  padding: EdgeInsets.symmetric(
                                      vertical: 2, horizontal: 10),
                                  decoration: BoxDecoration(
                                    color: isActive
                                        ? Color(0xffDCFCE7)
                                        : Color(0xffF3F4F6),
                                    borderRadius: BorderRadius.circular(10),
                                    border: Border.all(
                                        color: isActive
                                            ? Color.fromARGB(255, 96, 223, 138)
                                            : Color(0xffF3F4F6),
                                        width: .3),
                                  ),
                                  child: Text(
                                    isActive ? 'Active' : 'Expired',
                                    style: TextStyle(
                                        color: isActive
                                            ? Color(0xff166537)
                                            : Color(0xff4B5536),
                                        fontSize: 12,
                                        fontWeight: FontWeight.w600),
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 13),
                            Row(
                              children: [
                                Text.rich(
                                  TextSpan(children: [
                                    TextSpan(
                                      text: 'Price: ',
                                      style: TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.w600,
                                          color: Color(0xff4B5563)),
                                    ),
                                    TextSpan(
                                      text: '\$',
                                      style: TextStyle(
                                          fontSize: 13.5,
                                          fontWeight: FontWeight.w600,
                                          color: Color(0xff4B5563)),
                                    ),
                                    TextSpan(
                                      text: formatPriceWithCommas(
                                          package.packagePrice),
                                      style: TextStyle(
                                          fontSize: 13.5,
                                          fontWeight: FontWeight.w600,
                                          color: Color(0xff4B5563)),
                                    ),
                                  ]),
                                ),
                                SizedBox(width: 20),
                                Text.rich(
                                  TextSpan(children: [
                                    TextSpan(
                                      text: '+${package.extraServices.length} ',
                                      style: TextStyle(
                                          fontSize: 13,
                                          fontWeight: FontWeight.w600,
                                          letterSpacing: .5,
                                          color: themeColor),
                                    ),
                                    TextSpan(
                                      text: 'Extra Services',
                                      style: TextStyle(
                                          fontSize: 13,
                                          fontWeight: FontWeight.w600,
                                          letterSpacing: .5,
                                          color: themeColor),
                                    ),
                                  ]),
                                ),
                              ],
                            ),
                            SizedBox(height: 13),
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.calendar_today_outlined,
                                  color: Color(0xff6b7280),
                                  size: 17,
                                ),
                                SizedBox(width: 5),
                                Text(
                                  'Purchased: ',
                                  style: TextStyle(
                                      fontSize: 14, color: Color(0xff6b7280)),
                                ),
                                Text(
                                  package.purchaseAt.goodDayDate(),
                                  style: TextStyle(
                                      fontSize: 15, color: Color(0xff6b7280)),
                                ),
                                SizedBox(width: 25),
                                Icon(
                                  CupertinoIcons.clock,
                                  color: Color(0xff6b7280),
                                  size: 17,
                                ),
                                SizedBox(width: 5),
                                if (remainingDays > 0) ...[
                                  Text(
                                    'Remaining days: ',
                                    style: TextStyle(
                                        fontSize: 14, color: Color(0xff6b7280)),
                                  ),
                                  Text(
                                    remainingDays.toString(),
                                    style: TextStyle(
                                        fontSize: 15, color: Color(0xff6b7280)),
                                  ),
                                ]
                              ],
                            ),
                            SizedBox(height: 15),
                            Text(
                              package.description,
                              style: TextStyle(
                                  color: Color.fromARGB(255, 109, 109, 109),
                                  fontSize: 13,
                                  fontWeight: FontWeight.w500),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(width: 30),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text.rich(
                            TextSpan(children: [
                              TextSpan(
                                text: '\$',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w700,
                                  // color: Color(0xff4B5563),
                                ),
                              ),
                              TextSpan(
                                text:
                                    formatPriceWithCommas(package.totalAmount),
                                style: TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.w700,
                                  // color: Color(0xff4B5563),
                                ),
                              ),
                            ]),
                          ),
                          // Text(
                          //   "\$31,000",
                          //   style: TextStyle(
                          //     fontWeight: FontWeight.w700,
                          //     fontSize: 19,
                          //   ),
                          // ),
                          // SizedBox(height: 3),
                          Text(
                            "Total Price",
                            style: TextStyle(
                              color: Color(0xff6B7280),
                              fontSize: 13,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          SizedBox(height: 7),
                          Text(
                            "${package.validity} days validity",
                            style: TextStyle(
                              color: Color.fromARGB(255, 124, 124, 124),
                              fontSize: 12.5,
                              fontWeight: FontWeight.w500,
                            ),
                          )
                        ],
                      ),
                      SizedBox(width: 15),
                      Container(
                        padding: EdgeInsets.all(7.5),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(5),
                          border:
                              Border.all(color: Color(0xffe5e7eb), width: 1),
                        ),
                        child: Icon(
                          CupertinoIcons.eye,
                          size: 16,
                          color: Color(0xff3C3C3C),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          )
        ],
      ),
    );
  }
}

class UserWishlistView extends StatefulWidget {
  const UserWishlistView({super.key, required this.userDocId});
  final String userDocId;

  @override
  State<UserWishlistView> createState() => _UserWishlistViewState();
}

class _UserWishlistViewState extends State<UserWishlistView> {
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? userWishlistStream;
  List<UserWishlistModel> userWishist = [];
  // List<String> images = [];

  @override
  void initState() {
    super.initState();
    userWishlist();
  }

  userWishlist() async {
    try {
      userWishlistStream?.cancel();
      userWishlistStream = FBFireStore.wishlist
          .where('uId', isEqualTo: widget.userDocId)
          .snapshots()
          .listen((event) async {
        userWishist.clear();
        userWishist
            .addAll(event.docs.map((e) => UserWishlistModel.fromSnap(e)));
        userWishist.sort((a, b) => b.createdAt.compareTo(a.createdAt));

        // for (var element in userWishist) {
        //   final res =
        //       await FBFireStore.variants.doc(element.variantIds.first).get();
        //   if (res.exists) {
        //     final variantImages = List<String>.from(res['images']);
        //     images.add(variantImages.isNotEmpty ? variantImages.first : '');
        //   } else {
        //     images.add('');
        //   }
        // }
        if (mounted) setState(() {});
      });
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(horizontal: 5, vertical: 2),
      child: Column(
        children: [
          Row(
            children: [
              Spacer(),
              InkWell(
                onTap: () async {
                  addEditWishlist(context, null);
                },
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 5),
                  decoration: BoxDecoration(
                    border: Border.all(color: Color(0xffe2e8f0)),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Row(
                    children: [
                      const Icon(CupertinoIcons.add, size: 18),
                      SizedBox(width: 4),
                      Text(
                        "Wishlist",
                        style: TextStyle(
                            fontSize: 13.5,
                            letterSpacing: 1,
                            fontWeight: FontWeight.w500),
                      )
                    ],
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 10),
          userWishist.isEmpty
              ? Center(
                  child: Text('No Wishlist Found'),
                )
              : StaggeredGrid.extent(
                  maxCrossAxisExtent: 350,
                  mainAxisSpacing: 15,
                  crossAxisSpacing: 15,
                  children: [
                    ...List.generate(
                      userWishist.length,
                      (index) {
                        final wishlist = userWishist[index];
                        return Stack(
                          children: [
                            InkWell(
                              hoverColor: Colors.transparent,
                              highlightColor: Colors.transparent,
                              splashColor: Colors.transparent,
                              onTap: () {
                                // CRUD ON WISHLIST IN DIALOG
                                addEditWishlist(context, wishlist);
                              },
                              child: Container(
                                // clipBehavior: Clip.antiAlias,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(12),
                                  color: Colors.white,
                                  boxShadow: [
                                    BoxShadow(
                                      color: const Color.fromARGB(18, 0, 0, 0),
                                      blurRadius: 7,
                                      spreadRadius: 1.5,
                                      offset: Offset(1, 2),
                                    ),
                                  ],
                                ),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    ImageNetwork(
                                      key: ValueKey(DateTime.now()),
                                      onTap: null,
                                      image: wishlist.variantIds.first.image,
                                      height: 200,
                                      width: 350,
                                      // width: double.maxFinite,
                                      fitWeb: BoxFitWeb.cover,
                                    ),
                                    SizedBox(height: 12),
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 10.0),
                                      child: Text(
                                        wishlist.title,
                                        style: GoogleFonts.zcoolXiaoWei(
                                          fontSize: 20,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                                    SizedBox(height: 5),
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 10.0),
                                      child: Text(
                                        wishlist.desc,
                                        style: GoogleFonts.livvic(
                                          fontSize: 15,
                                          // fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                    SizedBox(height: 12),
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 10.0),
                                      child: Row(
                                        children: [
                                          Text.rich(
                                            TextSpan(children: [
                                              TextSpan(
                                                text: wishlist.variantIds.length
                                                    .toString(),
                                                style: GoogleFonts.livvic(
                                                    fontSize: 15,
                                                    fontWeight: FontWeight.w500,
                                                    color: const Color(
                                                        0xff3E3E3E)),
                                              ),
                                              TextSpan(
                                                text: ' items',
                                                style: GoogleFonts.livvic(
                                                    fontSize: 15,
                                                    color: const Color(
                                                        0xff3E3E3E)),
                                              ),
                                            ]),
                                          ),
                                        ],
                                      ),
                                    ),
                                    SizedBox(height: 12),
                                  ],
                                ),
                              ),
                            ),
                            Positioned(
                              top: 10,
                              right: 10,
                              child: InkWell(
                                onTap: () async {
                                  showDialog(
                                    context: context,
                                    builder: (context) {
                                      return AlertDialog(
                                        backgroundColor: Colors.white,
                                        surfaceTintColor: Colors.white,
                                        title: const Text("Delete"),
                                        content: const Text(
                                            "Are you sure you want to delete this wishlist?"),
                                        actions: [
                                          TextButton(
                                              onPressed: () async {
                                                await FBFireStore.wishlist
                                                    .doc(wishlist.docId)
                                                    .delete();
                                                Navigator.of(context).pop();
                                              },
                                              child: const Text("Yes")),
                                          TextButton(
                                              onPressed: () {
                                                Navigator.of(context).pop();
                                              },
                                              child: const Text("No")),
                                        ],
                                      );
                                    },
                                  );
                                },
                                child: Container(
                                  padding: const EdgeInsets.all(4),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    shape: BoxShape.circle,
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.grey,
                                        offset: Offset(0, 2),
                                        blurRadius: 5,
                                      ),
                                    ],
                                  ),
                                  child: Center(
                                    child: Icon(
                                      CupertinoIcons.xmark,
                                      size: 16,
                                    ),
                                  ),
                                ),
                              ),
                            )
                          ],
                        );
                      },
                    )
                  ],
                ),
        ],
      ),
    );
  }

  Future<dynamic> addEditWishlist(
      BuildContext context, UserWishlistModel? wishlist) {
    return showDialog(
      context: context,
      builder: (context) {
        // final variantIds =
        //     wishlist?.variantIds.map((e) => e.variantId).toList() ?? [];
        final selectedVariants = <VariantDetails>[];
        // List<String> toRemoveVariantIds = [];
        final nameCtrl = TextEditingController();
        final descCtrl = TextEditingController();
        bool isLoading = false;
        if (wishlist != null) {
          nameCtrl.text = wishlist.title;
          descCtrl.text = wishlist.desc;
          selectedVariants.addAll(wishlist.variantIds);
        }
        return StatefulBuilder(builder: (context, setState2) {
          return AlertDialog(
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            title: Text('Wishlist',
                style:
                    GoogleFonts.zcoolXiaoWei(fontSize: 25, color: themeColor)),
            content: SingleChildScrollView(
              child: Container(
                constraints:
                    const BoxConstraints(maxWidth: 1200, maxHeight: 700),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: nameCtrl,
                            decoration: inpDecor().copyWith(labelText: 'Name'),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 10),
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: descCtrl,
                            decoration:
                                inpDecor().copyWith(labelText: 'Description'),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 10),
                    Row(
                      children: [
                        Text(
                          "Selected Products",
                          style: GoogleFonts.zcoolXiaoWei(
                              fontSize: 17, color: themeColor),
                        ),
                        Spacer(),
                        SizedBox(width: 10),
                        InkWell(
                          hoverColor: Colors.transparent,
                          splashColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          onTap: () async {
                            showDialog(
                              context: context,
                              builder: (context) {
                                Timer? debounce;
                                List<NewVariantModel> searchedList = [];
                                getSearchVariant(String searchedStr) async {
                                  searchedList.clear();
                                  final searchedSnap = await FBFireStore
                                      .variants
                                      .where('lowerName',
                                          isGreaterThanOrEqualTo: searchedStr)
                                      .where('lowerName',
                                          isLessThanOrEqualTo:
                                              "$searchedStr\uf7ff")
                                      .limit(10)
                                      .get();
                                  searchedList.addAll(searchedSnap.docs
                                      .map((e) => NewVariantModel.fromSnap(e)));
                                }

                                return Dialog(
                                  backgroundColor: Colors.white,
                                  surfaceTintColor: Colors.white,
                                  child: StatefulBuilder(
                                      builder: (context, searchSetState) {
                                    return Container(
                                      constraints:
                                          const BoxConstraints(maxWidth: 800),
                                      padding: const EdgeInsets.all(20),
                                      child: Column(
                                        mainAxisSize: MainAxisSize.min,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text('Search Product',
                                              style: GoogleFonts.zcoolXiaoWei(
                                                  fontSize: 25,
                                                  color: themeColor)),
                                          const SizedBox(height: 15),
                                          TextFormField(
                                            decoration: InputDecoration(
                                              hintText: ' search product name',
                                              hintStyle: TextStyle(
                                                  color: Color(0xff737373),
                                                  fontSize: 14.5,
                                                  fontWeight: FontWeight.w500),
                                              border: OutlineInputBorder(
                                                borderRadius:
                                                    BorderRadius.circular(5),
                                                borderSide: BorderSide(
                                                    color:
                                                        Colors.grey.shade400),
                                              ),
                                              enabledBorder: OutlineInputBorder(
                                                borderRadius:
                                                    BorderRadius.circular(5),
                                                borderSide: BorderSide(
                                                    color:
                                                        Colors.grey.shade400),
                                              ),
                                              focusedBorder: OutlineInputBorder(
                                                borderRadius:
                                                    BorderRadius.circular(5),
                                                borderSide: BorderSide(
                                                    color:
                                                        Colors.grey.shade400),
                                              ),
                                            ),
                                            onChanged: (value) async {
                                              if (debounce?.isActive ?? false) {
                                                debounce?.cancel();
                                              }
                                              debounce = Timer(
                                                  const Duration(
                                                      milliseconds: 500),
                                                  () async {
                                                if (value.trim().isNotEmpty) {
                                                  await getSearchVariant(value
                                                      .toLowerCase()
                                                      .trim());
                                                  searchSetState(() {});
                                                } else {
                                                  searchedList.clear();
                                                  searchSetState(() {});
                                                }
                                              });
                                            },
                                          ),
                                          if (searchedList.isNotEmpty) ...[
                                            const SizedBox(height: 10),
                                            const Row(
                                              children: [
                                                SizedBox(
                                                    width: 50,
                                                    child: Text('Sr No')),
                                                SizedBox(width: 10),
                                                SizedBox(
                                                    width: 100,
                                                    child: Center(
                                                        child: Text('Image'))),
                                                SizedBox(width: 10),
                                                Expanded(
                                                    child:
                                                        Text('Product Name')),
                                                SizedBox(width: 10),
                                              ],
                                            ),
                                            const SizedBox(height: 10),
                                            ...List.generate(
                                              searchedList.length,
                                              (index) {
                                                final variant =
                                                    searchedList[index];
                                                return Padding(
                                                  padding: EdgeInsets.only(
                                                      top: index == 0 ? 0 : 8),
                                                  child: InkWell(
                                                    hoverColor:
                                                        Colors.transparent,
                                                    highlightColor:
                                                        Colors.transparent,
                                                    onTap: () {
                                                      if (selectedVariants
                                                          .map((e) =>
                                                              e.variantId)
                                                          .contains(
                                                              variant.docId)) {
                                                        showAppSnackBar(context,
                                                            'In Wishlist');
                                                        return;
                                                      }

                                                      selectedVariants.add(VariantDetails(
                                                          id: '',
                                                          name: variant
                                                              .lowerName,
                                                          description: variant
                                                              .description,
                                                          image: variant
                                                              .images.first,
                                                          variantId: variant
                                                              .docId!,
                                                          productId:
                                                              variant.productId,
                                                          addedOn: DateTime
                                                                  .now()
                                                              .millisecondsSinceEpoch,
                                                          addedBy: FBAuth
                                                                  .auth
                                                                  .currentUser
                                                                  ?.uid ??
                                                              '',
                                                          addedByType: 'Admin',
                                                          price: variant
                                                              .fixedprice));
                                                      // selectedVariantList
                                                      //     .add(variant);
                                                      Navigator.of(context)
                                                          .pop();
                                                      setState2(() {});
                                                    },
                                                    child: Row(
                                                      children: [
                                                        SizedBox(
                                                            width: 50,
                                                            child: Text((index +
                                                                    1)
                                                                .toString())),
                                                        const SizedBox(
                                                            width: 10),
                                                        SizedBox(
                                                          width: 100,
                                                          child: Center(
                                                            child: Container(
                                                                height: 50,
                                                                width: 50,
                                                                clipBehavior: Clip
                                                                    .antiAlias,
                                                                decoration: const BoxDecoration(
                                                                    shape: BoxShape
                                                                        .circle),
                                                                child:
                                                                    ImageNetwork(
                                                                  key: ValueKey(
                                                                      DateTime
                                                                          .now()),
                                                                  image: variant
                                                                      .images
                                                                      .first,
                                                                  height: 50,
                                                                  width: 50,
                                                                  fitWeb:
                                                                      BoxFitWeb
                                                                          .cover,
                                                                )),
                                                          ),
                                                        ),
                                                        const SizedBox(
                                                            width: 10),
                                                        Expanded(
                                                            child: Text(
                                                                capilatlizeFirstLetter(
                                                                    variant
                                                                        .lowerName))),
                                                      ],
                                                    ),
                                                  ),
                                                );
                                              },
                                            )
                                          ],
                                        ],
                                      ),
                                    );
                                  }),
                                );
                              },
                            );
                          },
                          child: Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 8, vertical: 5),
                            decoration: BoxDecoration(
                              border: Border.all(color: Color(0xffe2e8f0)),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Row(
                              children: [
                                const Icon(CupertinoIcons.add, size: 18),
                                SizedBox(width: 4),
                                Text(
                                  "Product",
                                  style: TextStyle(
                                      fontSize: 13.5,
                                      letterSpacing: 1,
                                      fontWeight: FontWeight.w500),
                                )
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 13),
                    FutureBuilder(
                        initialData: <NewVariantModel>[],
                        future: wishlist == null &&
                                selectedVariants.map((e) => e.variantId).isEmpty
                            ? null
                            : FBFireStore.variants
                                .where(FieldPath.documentId,
                                    whereIn: selectedVariants
                                        .map((e) => e.variantId)
                                        .toList())
                                .get()
                                .then((snapshot) {
                                return snapshot.docs
                                    .map((e) => NewVariantModel.fromSnap(e))
                                    .toList();
                              }),
                        builder: (context, snapshot) {
                          if (snapshot.connectionState ==
                              ConnectionState.waiting) {
                            return Center(
                              child: CircularProgressIndicator(),
                            );
                          }
                          if (snapshot.hasError) {
                            debugPrint(snapshot.error.toString());
                            return Center(
                              child: Text(
                                'Something went wrong',
                                style: TextStyle(
                                    fontSize: 13.5,
                                    letterSpacing: 1,
                                    fontWeight: FontWeight.w500),
                              ),
                            );
                          }
                          if (snapshot.data == null || snapshot.data!.isEmpty) {
                            return Center(
                              child: Text(
                                'No data found',
                                style: TextStyle(
                                    fontSize: 13.5,
                                    letterSpacing: 1,
                                    fontWeight: FontWeight.w500),
                              ),
                            );
                          }

                          if (snapshot.hasData) {
                            final dbVariantList = snapshot.data!;
                            // final variantList = wishlist?.variantIds ?? [];
                            // selectedVariants.removeWhere((e) =>
                            //     toRemoveVariantIds.contains(e.variantId));
                            return StaggeredGrid.extent(
                              maxCrossAxisExtent: 200,
                              mainAxisSpacing: 15,
                              crossAxisSpacing: 15,
                              children: [
                                ...List.generate(
                                  selectedVariants.length,
                                  (index) {
                                    return Container(
                                      clipBehavior: Clip.antiAlias,
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(7),
                                        color: Colors.white,
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.black12,
                                            blurRadius: 10,
                                            offset: Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      child: SizedBox(
                                        height: 155,
                                        child: Stack(
                                          children: [
                                            Column(
                                              children: [
                                                ImageNetwork(
                                                  key: ValueKey(DateTime.now()),
                                                  image: selectedVariants[index]
                                                      .image,
                                                  height: 100,
                                                  width: 200,
                                                  fitWeb: BoxFitWeb.cover,
                                                ),
                                                SizedBox(height: 7),
                                                Padding(
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                      horizontal: 3.0),
                                                  child: Text(
                                                    capilatlizeFirstLetter(
                                                        selectedVariants[index]
                                                            .name),
                                                    maxLines: 2,
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                    style: GoogleFonts.mulish(
                                                        fontSize: 14,
                                                        color: const Color(
                                                            0xff3E3E3E)),
                                                  ),
                                                ),
                                                SizedBox(height: 7),
                                                // Text(
                                                //   '₹${selectedVariants[index].price}',
                                                //   style: GoogleFonts.mulish(
                                                //       fontSize: 14,
                                                //       color:
                                                //           const Color(0xff3E3E3E)),
                                                // ),
                                              ],
                                            ),
                                            Padding(
                                              padding:
                                                  const EdgeInsets.all(5.0),
                                              child: Align(
                                                alignment: Alignment.topRight,
                                                child: InkWell(
                                                  onTap: () {
                                                    // toRemoveVariantIds.add(
                                                    //     selectedVariants[index]
                                                    //         .variantId);
                                                    selectedVariants.removeWhere(
                                                        (element) =>
                                                            element.variantId ==
                                                            selectedVariants[
                                                                    index]
                                                                .variantId);
                                                    setState2(() {});
                                                  },
                                                  child: Container(
                                                    padding:
                                                        const EdgeInsets.all(3),
                                                    decoration:
                                                        const BoxDecoration(
                                                      boxShadow: [
                                                        BoxShadow(
                                                          color: Colors.black12,
                                                          blurRadius: 10,
                                                          offset: Offset(0, 2),
                                                        ),
                                                      ],
                                                      color: Colors.white,
                                                      shape: BoxShape.circle,
                                                    ),
                                                    child: const Icon(
                                                      CupertinoIcons.xmark,
                                                      size: 16,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                            if (!(dbVariantList
                                                .map((e) => e.docId)
                                                .contains(
                                                    selectedVariants[index]
                                                        .variantId)))
                                              Container(
                                                // height: 150,
                                                color: const Color.fromARGB(
                                                    15, 0, 0, 0),
                                              ),
                                          ],
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ],
                            );
                          }
                          return Center(
                            child: Text(
                              'No data found',
                              style: TextStyle(
                                  fontSize: 13.5,
                                  letterSpacing: 1,
                                  fontWeight: FontWeight.w500),
                            ),
                          );
                        }),
                  ],
                ),
              ),
            ),
            actions: isLoading
                ? [
                    const SizedBox(
                      height: 28,
                      width: 28,
                      child: Center(
                        child: CircularProgressIndicator(),
                      ),
                    )
                  ]
                : [
                    TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: const Text('Cancel'),
                    ),
                    ElevatedButton(
                      onPressed: () async {
                        // if (toRemoveVariantIds.isEmpty) {
                        //   Navigator.of(context).pop();
                        //   return;
                        // }
                        if (nameCtrl.text.trim().isEmpty) {
                          showErrorAppSnackBar(context, 'Name Required');
                          return;
                        }
                        if (descCtrl.text.trim().isEmpty) {
                          showErrorAppSnackBar(context, 'Description Required');
                          return;
                        }
                        if (selectedVariants.isEmpty) {
                          showErrorAppSnackBar(context, 'Select Products');
                          return;
                        }
                        try {
                          setState2(() {
                            isLoading = true;
                          });
                          final data = {
                            'uId': wishlist?.uId ?? widget.userDocId,
                            'variantIds': selectedVariants
                                .map((e) => e.toJson())
                                .toList(),
                            'createdAt':
                                wishlist?.createdAt.millisecondsSinceEpoch ??
                                    DateTime.now().millisecondsSinceEpoch,
                            'title': nameCtrl.text.trim(),
                            'desc': descCtrl.text.trim(),
                            'createdBy': wishlist?.createdBy ??
                                FBAuth.auth.currentUser?.uid,
                            'createdByType': wishlist?.createdByType ?? 'Admin',
                          };
                          wishlist != null
                              ? await FBFireStore.wishlist
                                  .doc(wishlist.docId)
                                  .update(data)
                              : await FBFireStore.wishlist.add(data);
                          // await FBFireStore.wishlist
                          //     .doc(wishlist.docId)
                          //     .update({
                          //   'variantIds':
                          //       FieldValue.arrayRemove(toRemoveVariantIds),
                          // });
                          setState2(() {
                            isLoading = false;
                          });
                          Navigator.of(context).pop();
                          setState(() {});
                        } on Exception catch (e) {
                          debugPrint(e.toString());
                        }
                      },
                      child: const Text('Save'),
                    ),
                  ],
          );
        });
      },
    );
  }
}
