import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../shared/theme.dart';
import 'tabview_widgets.dart';

class UserDetailsPage extends StatefulWidget {
  const UserDetailsPage({super.key, required this.userDocId});
  final String userDocId;
  @override
  State<UserDetailsPage> createState() => _UserDetailsPageState();
}

class _UserDetailsPageState extends State<UserDetailsPage>
    with TickerProviderStateMixin {
  late final TabController _tabController;
  final searchCtrl = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 8, vsync: this);
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.sizeOf(context).height;
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Row(
                children: [
                  IconButton(
                    onPressed: () {
                      context.pop();
                    },
                    icon: const Icon(
                      CupertinoIcons.arrow_left,
                      color: themeColor,
                    ),
                  ),
                  const SizedBox(width: 10),
                  Text(
                    'User Details',
                    style: GoogleFonts.zcoolXiaoWei(
                      fontSize: 35,
                      color: themeColor,
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 20),
          TabBar(
            isScrollable: true,
            tabAlignment: TabAlignment.start,
            // physics: NeverScrollableScrollPhysics(),
            controller: _tabController,
            tabs: <Widget>[
              Tab(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(width: 10),
                    Icon(CupertinoIcons.person, size: 20),
                    SizedBox(width: 8),
                    Text(
                      "Details",
                      style: GoogleFonts.livvic(
                        fontSize: 16,
                        letterSpacing: .7,
                        wordSpacing: 2,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(width: 10),
                  ],
                ),
              ),
              Tab(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(width: 10),
                    Icon(CupertinoIcons.list_bullet_below_rectangle, size: 20),
                    SizedBox(width: 8),
                    Text(
                      "Orders",
                      style: GoogleFonts.livvic(
                        fontSize: 16,
                        letterSpacing: .7,
                        wordSpacing: 2,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(width: 10),
                  ],
                ),
              ),
              Tab(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(width: 10),
                    Icon(CupertinoIcons.clock, size: 20),
                    SizedBox(width: 8),
                    Text(
                      "Inquiries",
                      style: GoogleFonts.livvic(
                        fontSize: 16,
                        letterSpacing: .7,
                        wordSpacing: 2,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(width: 10),
                  ],
                ),
              ),
              Tab(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(width: 10),
                    Icon(CupertinoIcons.calendar, size: 20),
                    SizedBox(width: 8),
                    Text(
                      "Bookings",
                      style: GoogleFonts.livvic(
                        fontSize: 16,
                        letterSpacing: .7,
                        wordSpacing: 2,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(width: 10),
                  ],
                ),
              ),
              Tab(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(width: 10),
                    Icon(CupertinoIcons.list_bullet, size: 20),
                    SizedBox(width: 8),
                    Text(
                      "Transaction",
                      style: GoogleFonts.livvic(
                        fontSize: 16,
                        letterSpacing: .7,
                        wordSpacing: 2,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(width: 10),
                  ],
                ),
              ),
              Tab(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(width: 10),
                    Icon(CupertinoIcons.cube_box, size: 20),
                    SizedBox(width: 8),
                    Text(
                      "Delivery",
                      style: GoogleFonts.livvic(
                        fontSize: 16,
                        letterSpacing: .7,
                        wordSpacing: 2,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(width: 10),
                  ],
                ),
              ),
              Tab(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(width: 10),
                    Icon(Icons.currency_rupee_rounded, size: 20),
                    SizedBox(width: 8),
                    Text(
                      "Packages",
                      style: GoogleFonts.livvic(
                        fontSize: 16,
                        letterSpacing: .7,
                        wordSpacing: 2,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(width: 10),
                  ],
                ),
              ),
              Tab(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(width: 10),
                    Icon(CupertinoIcons.heart, size: 20),
                    SizedBox(width: 8),
                    Text(
                      "Wishlist",
                      style: GoogleFonts.livvic(
                        fontSize: 16,
                        letterSpacing: .7,
                        wordSpacing: 2,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(width: 10),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 15),
          Container(
            padding: EdgeInsets.only(top: 20),
            constraints: BoxConstraints(maxHeight: screenHeight - 163),
            child: TabBarView(
              physics: NeverScrollableScrollPhysics(),
              controller: _tabController,
              children: [
                UserDetailsTabView(userDocId: widget.userDocId),
                UserOrdersTabView(userDocId: widget.userDocId),
                UserInquiriesTabView(userDocId: widget.userDocId),
                UserBookingsTabView(userDocId: widget.userDocId),
                UserTransactionTabView(userDocId: widget.userDocId),
                UserDeliveryTabView(userDocId: widget.userDocId),
                UserPackageView(userDocId: widget.userDocId),
                UserWishlistView(userDocId: widget.userDocId),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
