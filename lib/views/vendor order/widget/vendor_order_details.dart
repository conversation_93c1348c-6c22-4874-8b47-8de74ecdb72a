import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wedding_super_admin/controller/home_ctrl.dart';
import 'package:wedding_super_admin/models/display_order_details_model.dart';
import 'package:wedding_super_admin/models/order_model.dart';
import 'package:wedding_super_admin/models/teammodel.dart';
import 'package:wedding_super_admin/views/common/page_header.dart';

import '../../../models/product.dart';
import '../../../models/variants.dart';
import '../../../models/vendor.dart';
import '../../../models/vendor_order_model.dart';
import '../../../shared/const.dart';
import '../../../shared/firebase.dart';
import '../../../shared/methods.dart';
import '../../../shared/theme.dart';
import '../../orders/widgets/extracted_widget.dart';

/* class VendorOrderDetails extends StatefulWidget {
  const VendorOrderDetails({super.key, required this.vendorOrderId});
  final String vendorOrderId;
  @override
  State<VendorOrderDetails> createState() => _VendorOrderDetailsState();
}

class _VendorOrderDetailsState extends State<VendorOrderDetails> {
  VendorOrderModel? vendorOrderModel;
  VendorModel? vendorModel;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(
      builder: (hCtrl) {
        vendorOrderModel = hCtrl.allVendorOrderList.firstWhereOrNull(
            (element) => element.docId == widget.vendorOrderId);
        if (vendorOrderModel != null) {
          vendorModel = hCtrl.vendors.firstWhereOrNull(
              (element) => element.docId == vendorOrderModel?.vendorId);
        }
        return VendorOrderDetailsMainScreen(
          vendorOrderId: ,
            // vendor: vendorModel,
            // vendorOrderModel: vendorOrderModel,
            );
      },
    );
  }
} */

class VendorOrderDetails extends StatefulWidget {
  const VendorOrderDetails({
    super.key,
    required this.vendorOrderId,
  });
  final String vendorOrderId;

  @override
  State<VendorOrderDetails> createState() => _VendorOrderDetailsState();
}

class _VendorOrderDetailsState extends State<VendorOrderDetails> {
  List<DisplayProductsDetails> allOrderedProductDetails = [];
  List<TransactionModel> allVendorTransaction = [];
  List<DeliveryDetailsModel> allDeliveryDetails = [];
  VendorOrderModel? vendorOrderModel;
  VendorModel? vendorModel;
  TeamModel? createdByModel;
  TeamModel? statusUpdatedByModel;
  String? currentOrderStatus;

  bool dataLoaded = false;

  getRequiredData() async {
    dataLoaded = false;
    setState(() {});
    try {
      final ctrl = Get.find<HomeCtrl>();
      allOrderedProductDetails.clear();
      final vendorSnap =
          await FBFireStore.vendorOrder.doc(widget.vendorOrderId).get();

      vendorOrderModel = VendorOrderModel.fromDocSnap(vendorSnap);

      createdByModel = ctrl.teamMembers.firstWhereOrNull(
          (element) => element.docId == vendorOrderModel?.createdBy);

      vendorModel = ctrl.vendors.firstWhereOrNull(
          (element) => element.docId == vendorOrderModel?.vendorId);
      // final statusupdatedByTeamSnap =
      //     await FBFireStore.teamMember.doc(widget.vendorOrderModel.statusUpdatedAt).get();
      // statusUpdatedByModel = widget.vendorOrderModel.s != null
      //     ? TeamModel.fromDocSnap(statusupdatedByTeamSnap)
      //     : null;
      currentOrderStatus = vendorOrderModel?.status;
      if (vendorModel != null && vendorOrderModel != null) {
        for (var orderData in vendorOrderModel!.orderProducts) {
          final productSnap =
              await FBFireStore.products.doc(orderData.productId).get();
          final productData = ProductModel.fromDocSnap(productSnap);
          final variantSnap =
              await FBFireStore.variants.doc(orderData.variantId).get();
          final variantData = NewVariantModel.fromDocSnap(variantSnap);

          allOrderedProductDetails.add(DisplayProductsDetails(
              product: productData,
              variant: variantData,
              vendorData: vendorModel!,
              orderData: orderData));
        }
      }
      final transactionSnap = await FBFireStore.vendorOrder
          .doc(vendorOrderModel?.docId)
          .collection('transaction')
          .get();
      allVendorTransaction.clear();
      allVendorTransaction.addAll(transactionSnap.docs
          .map((e) => TransactionModel.fromSnap(e))
          .toList());
      final deliverySnap = await FBFireStore.vendorOrder
          .doc(vendorOrderModel?.docId)
          .collection('deliveries')
          .get();
      allDeliveryDetails.clear();
      allDeliveryDetails.addAll(deliverySnap.docs
          .map((e) => DeliveryDetailsModel.fromSnap(e))
          .toList());
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
    dataLoaded = true;
    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    getRequiredData();
  }

  @override
  Widget build(BuildContext context) {
    return !dataLoaded
        ? const Center(child: CircularProgressIndicator())
        : vendorOrderModel != null
            ? SingleChildScrollView(
                padding:
                    const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    PageHeaderWithTrailingAndBack(
                      title: 'Vendor Order Details',
                      showTrailing: true,
                      trailing: Container(
                        padding:
                            EdgeInsets.symmetric(vertical: 7, horizontal: 12),
                        decoration: BoxDecoration(
                          // color: Color
                          borderRadius: BorderRadius.circular(6),
                          border: Border.all(color: dividerColor, width: 1.2
                              //  const Color.fromARGB(255, 105, 105, 105),
                              ),
                        ),
                        child: Row(
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  capilatlizeFirstLetter('Harsh bhai'),
                                  style: const TextStyle(
                                      color: Color(0xff333333),
                                      fontSize: 13,
                                      fontWeight: FontWeight.w600),
                                ),
                                const SizedBox(height: 1),
                                Text(
                                  '+919639639630',
                                  style: const TextStyle(
                                    color: Color(0xff737373),
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(width: 11),
                            InkWell(
                              hoverColor: Colors.transparent,
                              highlightColor: Colors.transparent,
                              splashColor: Colors.transparent,
                              onTap: () {
                                Clipboard.setData(
                                        ClipboardData(text: vendorModel!.phone))
                                    .then((_) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                          content:
                                              Text("Phone number copied")));
                                });
                              },
                              child: Container(
                                  padding: const EdgeInsets.all(9),
                                  decoration: const BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: Color(0xfffef8f6)),
                                  child: const Icon(
                                    CupertinoIcons.square_on_square,
                                    color: themeColor,
                                    size: 20,
                                  )),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 20),
                    Container(
                      padding: const EdgeInsets.all(18),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                              color: const Color.fromARGB(255, 225, 225, 225))),
                      child: Row(
                        children: [
                          Expanded(
                              child: OrderDetailDataDisply(
                                  title: 'Order ID',
                                  data: vendorOrderModel?.orderId ?? '-')),
                          const SizedBox(width: 10),
                          Expanded(
                              child: OrderDetailDataDisply(
                                  title: 'Created by',
                                  data: createdByModel?.name ?? '-')),
                          const SizedBox(width: 10),
                          Expanded(
                              child: OrderDetailDataDisply(
                                  title: 'Order date',
                                  data:
                                      '${vendorOrderModel?.createdAt.goodDayDate()}   ${vendorOrderModel?.createdAt.goodTime()}')),
                          const SizedBox(width: 10),
                          Expanded(
                              child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Order status',
                                style: TextStyle(
                                    fontSize: 13,
                                    color: Color.fromARGB(255, 128, 128, 128),
                                    fontWeight: FontWeight.w400),
                              ),
                              const SizedBox(height: 5),
                              Row(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 5, horizontal: 10),
                                    decoration: BoxDecoration(
                                      color: const Color(0xffFFEDD5),
                                      borderRadius: BorderRadius.circular(15),
                                    ),
                                    child: Text(
                                      currentOrderStatus!,
                                      style: const TextStyle(
                                          fontSize: 13,
                                          fontWeight: FontWeight.w500,
                                          color: Color(0xff9A3412)),
                                    ),
                                  ),
                                  const SizedBox(width: 10),
                                  currentOrderStatus ==
                                          VendorOrderStatus.orderCompleted
                                      ? const SizedBox()
                                      : InkWell(
                                          hoverColor: Colors.transparent,
                                          highlightColor: Colors.transparent,
                                          splashColor: Colors.transparent,
                                          onTap: () async {
                                            showDialog(
                                              context: context,
                                              builder: (context) {
                                                return AlertDialog(
                                                  backgroundColor: Colors.white,
                                                  surfaceTintColor:
                                                      Colors.white,
                                                  title: Text(
                                                    "Change Status",
                                                    style: GoogleFonts
                                                        .zcoolXiaoWei(
                                                            fontSize: 20),
                                                  ),
                                                  content:
                                                      DropdownButtonHideUnderline(
                                                    child: Theme(
                                                      data: Theme.of(context)
                                                          .copyWith(
                                                        focusColor: Colors
                                                            .grey.shade200,
                                                      ),
                                                      child:
                                                          DropdownButtonFormField(
                                                        dropdownColor:
                                                            Colors.white,
                                                        focusColor:
                                                            Colors.transparent,
                                                        decoration: inpDecor()
                                                            .copyWith(),
                                                        value:
                                                            currentOrderStatus,
                                                        items: [
                                                          DropdownMenuItem(
                                                            value:
                                                                VendorOrderStatus
                                                                    .created,
                                                            child: Text(
                                                              VendorOrderStatus
                                                                  .created,
                                                              style: TextStyle(
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w400,
                                                                  letterSpacing:
                                                                      .7,
                                                                  color: Color
                                                                      .fromARGB(
                                                                          255,
                                                                          255,
                                                                          174,
                                                                          52)),
                                                            ),
                                                          ),
                                                          DropdownMenuItem(
                                                            value: VendorOrderStatus
                                                                .orderCompleted,
                                                            child: Text(
                                                              VendorOrderStatus
                                                                  .orderCompleted,
                                                              style: TextStyle(
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w400,
                                                                  letterSpacing:
                                                                      .7,
                                                                  color: Color
                                                                      .fromARGB(
                                                                          255,
                                                                          111,
                                                                          190,
                                                                          114)),
                                                            ),
                                                          ),
                                                        ],
                                                        onChanged:
                                                            (value) async {
                                                          currentOrderStatus =
                                                              value;
                                                          await FBFireStore
                                                              .vendorOrder
                                                              .doc(
                                                                  vendorOrderModel
                                                                      ?.docId)
                                                              .update({
                                                            'statusUpdatedAt':
                                                                DateTime.now()
                                                                    .millisecondsSinceEpoch,
                                                            'status':
                                                                currentOrderStatus,
                                                            'statusUpdatedBy':
                                                                FBAuth
                                                                    .auth
                                                                    .currentUser
                                                                    ?.uid,
                                                          });

                                                          Navigator.of(context)
                                                              .pop();
                                                          setState(() {});
                                                          context.pop();
                                                        },
                                                      ),
                                                    ),
                                                  ),
                                                  actions: [
                                                    TextButton(
                                                        onPressed: () {
                                                          Navigator.of(context)
                                                              .pop();
                                                        },
                                                        child: const Text(
                                                            "Cancel")),
                                                  ],
                                                );
                                              },
                                            );
                                          },
                                          child: const Icon(Icons.edit,
                                              color: Color(0xff3E3E3E),
                                              size: 13),
                                        )
                                ],
                              )
                            ],
                          )),
                        ],
                      ),
                    ),
                    const SizedBox(height: 20),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          padding: const EdgeInsets.all(18),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                  color: const Color.fromARGB(
                                      255, 225, 225, 225))),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                width: double.maxFinite,
                                decoration: BoxDecoration(
                                    // color: tableHeaderColor,
                                    borderRadius: BorderRadius.circular(5)),
                                child: Row(
                                  children: [
                                    Text(
                                      'Transaction',
                                      style: GoogleFonts.zcoolXiaoWei(
                                          fontSize: 18,
                                          color: themeColor,
                                          fontWeight: FontWeight.w500),
                                    ),
                                    const Spacer(),
                                    InkWell(
                                      hoverColor: Colors.transparent,
                                      splashColor: Colors.transparent,
                                      highlightColor: Colors.transparent,
                                      onTap: () async {
                                        addEditTransactionDialog(context, null);
                                      },
                                      child: Container(
                                        padding: EdgeInsets.symmetric(
                                            horizontal: 8, vertical: 5),
                                        decoration: BoxDecoration(
                                          border: Border.all(
                                              color: Color(0xffe2e8f0)),
                                          borderRadius:
                                              BorderRadius.circular(4),
                                        ),
                                        child: Row(
                                          children: [
                                            const Icon(CupertinoIcons.add,
                                                size: 18),
                                            SizedBox(width: 4),
                                            Text(
                                              "Transaction",
                                              style: TextStyle(
                                                  fontSize: 13.5,
                                                  letterSpacing: 1,
                                                  fontWeight: FontWeight.w500),
                                            )
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              // const Divider(
                              //     color: Color.fromARGB(35, 0, 0, 0),
                              //     thickness: .3),
                              if (allVendorTransaction.isNotEmpty)
                                const SizedBox(height: 18),

                              ...List.generate(
                                allVendorTransaction.length,
                                (index) {
                                  final transactionData =
                                      allVendorTransaction[index];
                                  // final variantProduct = products.firstWhereOrNull(
                                  //     (element) => element.docId == variant.productId);
                                  return TransactionTile(
                                    index: index,
                                    transactionData: transactionData,
                                    onTap: () async {
                                      addEditTransactionDialog(
                                          context, transactionData);
                                    },
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 20),
                        Container(
                          padding: const EdgeInsets.all(18),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                  color: const Color.fromARGB(
                                      255, 225, 225, 225))),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                width: double.maxFinite,
                                decoration: BoxDecoration(
                                    // color: tableHeaderColor,
                                    borderRadius: BorderRadius.circular(5)),
                                child: Row(
                                  children: [
                                    Text(
                                      'Delivery Details',
                                      style: GoogleFonts.zcoolXiaoWei(
                                          fontSize: 18,
                                          color: themeColor,
                                          fontWeight: FontWeight.w500),
                                    ),
                                    const Spacer(),
                                    // InkWell(
                                    //   hoverColor: Colors.transparent,
                                    //   splashColor: Colors.transparent,
                                    //   highlightColor: Colors.transparent,
                                    //   onTap: () async {
                                    //     // await addEditDeliveryDetailsDialog(
                                    //     //     context, null, displayOrderDetails);
                                    //   },
                                    //   child: Container(
                                    //     padding: EdgeInsets.symmetric(
                                    //         horizontal: 8, vertical: 5),
                                    //     decoration: BoxDecoration(
                                    //       border: Border.all(
                                    //           color: Color(0xffe2e8f0)),
                                    //       borderRadius:
                                    //           BorderRadius.circular(4),
                                    //     ),
                                    //     child: Row(
                                    //       children: [
                                    //         const Icon(CupertinoIcons.add,
                                    //             size: 18),
                                    //         SizedBox(width: 4),
                                    //         Text(
                                    //           "Delivery",
                                    //           style: TextStyle(
                                    //               fontSize: 13.5,
                                    //               letterSpacing: 1,
                                    //               fontWeight: FontWeight.w500),
                                    //         )
                                    //       ],
                                    //     ),
                                    //   ),
                                    // ),
                                  ],
                                ),
                              ),
                              // const Divider(
                              //   color: Color.fromARGB(35, 0, 0, 0),
                              //   thickness: .3,
                              // ),
                              if (allDeliveryDetails.isNotEmpty)
                                const SizedBox(height: 18),
                              ...List.generate(
                                allDeliveryDetails.length,
                                (index) {
                                  final deliveryDetail =
                                      allDeliveryDetails[index];
                                  // final variantProduct = products.firstWhereOrNull(
                                  //     (element) => element.docId == variant.productId);
                                  return DeliveryDetailTile(
                                      index: index,
                                      onDelete: () {},
                                      onTap: () async {
                                        await addEditDeliveryDetailsDialog(
                                          context,
                                          deliveryDetail,
                                        );
                                      },
                                      allOrderedProductsLength:
                                          allOrderedProductDetails.length,
                                      deliveryDetail: deliveryDetail);
                                },
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    Container(
                        padding: const EdgeInsets.symmetric(vertical: 18),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                              color: const Color.fromARGB(255, 225, 225, 225)),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 18),
                              width: double.maxFinite,
                              decoration: BoxDecoration(
                                  // color: tableHeaderColor,
                                  borderRadius: BorderRadius.circular(5)),
                              child: Row(
                                children: [
                                  Text(
                                    'Products List',
                                    style: GoogleFonts.zcoolXiaoWei(
                                        fontSize: 18,
                                        color: themeColor,
                                        fontWeight: FontWeight.w500),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 25),
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 18.0),
                              child: Row(
                                children: [
                                  const SizedBox(
                                      width: 55,
                                      child: Text(
                                        'Sr No',
                                        style: TextStyle(
                                          color:
                                              Color.fromARGB(255, 66, 72, 82),
                                          fontWeight: FontWeight.w500,
                                          letterSpacing: .5,
                                          wordSpacing: 1,
                                        ),
                                      )),
                                  const SizedBox(width: 10),
                                  SizedBox(
                                    width: 50,
                                    child: const Text(
                                      'Image',
                                      style: TextStyle(
                                        color: Color.fromARGB(255, 66, 72, 82),
                                        fontWeight: FontWeight.w500,
                                        letterSpacing: .5,
                                        wordSpacing: 1,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 15),
                                  Expanded(
                                    child: Text(
                                      'Product Code',
                                      maxLines: 2,
                                      style: TextStyle(
                                        color: Color.fromARGB(255, 66, 72, 82),
                                        fontWeight: FontWeight.w500,
                                        letterSpacing: .5,
                                        wordSpacing: 1,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 15),
                                  Expanded(
                                    child: Text(
                                      'Product Name',
                                      maxLines: 2,
                                      style: TextStyle(
                                        color: Color.fromARGB(255, 66, 72, 82),
                                        fontWeight: FontWeight.w500,
                                        letterSpacing: .5,
                                        wordSpacing: 1,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 15),
                                  Expanded(
                                    child: Text(
                                      'Vendor Name',
                                      maxLines: 2,
                                      style: TextStyle(
                                        color: Color.fromARGB(255, 66, 72, 82),
                                        fontWeight: FontWeight.w500,
                                        letterSpacing: .5,
                                        wordSpacing: 1,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 15),
                                  Expanded(
                                    child: Text(
                                      'Price (₹)',
                                      maxLines: 2,
                                      style: TextStyle(
                                        color: Color.fromARGB(255, 66, 72, 82),
                                        fontWeight: FontWeight.w500,
                                        letterSpacing: .5,
                                        wordSpacing: 1,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 15),
                                  const Expanded(
                                      child: Text(
                                    'Selling Price (\$)',
                                    style: TextStyle(
                                      color: Color.fromARGB(255, 66, 72, 82),
                                      fontWeight: FontWeight.w500,
                                      letterSpacing: .5,
                                      wordSpacing: 1,
                                    ),
                                  )),
                                  const SizedBox(width: 15),
                                  const Expanded(
                                      child: Text(
                                    'Ordered Qty',
                                    style: TextStyle(
                                      color: Color.fromARGB(255, 66, 72, 82),
                                      fontWeight: FontWeight.w500,
                                      letterSpacing: .5,
                                      wordSpacing: 1,
                                    ),
                                  )),
                                  const SizedBox(width: 15),
                                  const Expanded(
                                      child: Text(
                                    'Current Status',
                                    style: TextStyle(
                                      color: Color.fromARGB(255, 66, 72, 82),
                                      fontWeight: FontWeight.w500,
                                      letterSpacing: .5,
                                      wordSpacing: 1,
                                    ),
                                  )),
                                  const SizedBox(width: 15),
                                  const Expanded(
                                      child: Text(
                                    'User Note',
                                    style: TextStyle(
                                      color: Color.fromARGB(255, 66, 72, 82),
                                      fontWeight: FontWeight.w500,
                                      letterSpacing: .5,
                                      wordSpacing: 1,
                                    ),
                                  )),
                                  const SizedBox(width: 15),
                                  const Expanded(
                                      child: Text(
                                    'Admin Note',
                                    style: TextStyle(
                                      color: Color.fromARGB(255, 66, 72, 82),
                                      fontWeight: FontWeight.w500,
                                      letterSpacing: .5,
                                      wordSpacing: 1,
                                    ),
                                  )),
                                  const SizedBox(width: 15),
                                  const Expanded(
                                      child: Text(
                                    'Delivery Date',
                                    style: TextStyle(
                                      color: Color.fromARGB(255, 66, 72, 82),
                                      fontWeight: FontWeight.w500,
                                      letterSpacing: .5,
                                      wordSpacing: 1,
                                    ),
                                  )),
                                ],
                              ),
                            ),
                            const SizedBox(height: 18),
                            const Divider(
                                height: 0,
                                color: Color.fromARGB(255, 225, 225, 225)),
                            ...List.generate(allOrderedProductDetails.length,
                                (index) {
                              final displayOrderData =
                                  allOrderedProductDetails[index];
                              // final variantProduct = products.firstWhereOrNull(
                              //     (element) => element.docId == variant.productId);
                              return Container(
                                decoration: BoxDecoration(
                                    border: Border(
                                        bottom: BorderSide(
                                            color: Color.fromARGB(
                                                255, 225, 225, 225)))),
                                padding: EdgeInsets.symmetric(
                                  vertical: 10,
                                  horizontal: 18,
                                ),
                                child: Row(
                                  children: [
                                    SizedBox(
                                        width: 55,
                                        child: Text(
                                          '${index + 1}',
                                          style: GoogleFonts.mulish(
                                              fontSize: 14.5,
                                              color: const Color(0xff3E3E3E)),
                                        )),
                                    const SizedBox(width: 10),
                                    Container(
                                      height: 50,
                                      width: 50,
                                      clipBehavior: Clip.antiAlias,
                                      decoration: const BoxDecoration(
                                          shape: BoxShape.circle),
                                      child: Image.network(
                                        displayOrderData.variant.images.first,
                                        fit: BoxFit.cover,
                                        errorBuilder:
                                            (context, error, stackTrace) {
                                          return Container(
                                            height: 50,
                                            width: 50,
                                            clipBehavior: Clip.antiAlias,
                                            decoration: BoxDecoration(
                                                border: Border.all(
                                                    color: Colors.black38),
                                                shape: BoxShape.circle),
                                            child: const Icon(
                                              CupertinoIcons.photo,
                                              size: 17,
                                            ),
                                          );
                                        },
                                      ),
                                    ),
                                    const SizedBox(width: 15),
                                    Expanded(
                                      child: Text(
                                        displayOrderData.product.sku
                                                .trim()
                                                .isNotEmpty
                                            ? displayOrderData.product.sku
                                            : '-',
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                        style: GoogleFonts.mulish(
                                            fontSize: 13.5,
                                            color: const Color(0xff6C6C6C)),
                                      ),
                                    ),
                                    const SizedBox(width: 15),
                                    Expanded(
                                        child: Text(
                                      capilatlizeFirstLetter(
                                          displayOrderData.variant.lowerName),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                      style: GoogleFonts.mulish(
                                          fontSize: 14.5,
                                          color: const Color(0xff3E3E3E)),
                                    )),
                                    const SizedBox(width: 15),
                                    Expanded(
                                        child: Text(
                                      capilatlizeFirstLetter(
                                          displayOrderData.vendorData.name),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                      style: GoogleFonts.mulish(
                                          fontSize: 14.5,
                                          color: const Color(0xff3E3E3E)),
                                    )),
                                    const SizedBox(width: 15),
                                    Expanded(
                                        child: Text(
                                      formatPriceWithCommas(displayOrderData
                                          .orderData.purchasePrice),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                      style: GoogleFonts.mulish(
                                          fontSize: 14.5,
                                          color: const Color(0xff3E3E3E)),
                                    )),
                                    const SizedBox(width: 15),
                                    Expanded(
                                        child: Text(
                                      formatPriceWithCommas(displayOrderData
                                          .orderData.sellingPrice),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                      style: GoogleFonts.mulish(
                                          fontSize: 14.5,
                                          color: const Color(0xff3E3E3E)),
                                    )),
                                    const SizedBox(width: 15),
                                    Expanded(
                                        child: Text(
                                      capilatlizeFirstLetter(displayOrderData
                                          .orderData.qty
                                          .toString()),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                      style: GoogleFonts.mulish(
                                          fontSize: 14.5,
                                          color: const Color(0xff3E3E3E)),
                                    )),
                                    const SizedBox(width: 15),
                                    Expanded(
                                        child: Text(
                                      displayOrderData
                                              .orderData.currentStatus ??
                                          '',
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                      style: GoogleFonts.mulish(
                                          fontSize: 14.5,
                                          color: const Color(0xff3E3E3E)),
                                    )),
                                    const SizedBox(width: 15),
                                    Expanded(
                                        child: Text(
                                      displayOrderData.orderData.userNote,
                                      style: GoogleFonts.mulish(
                                          fontSize: 14.5,
                                          color: const Color(0xff3E3E3E)),
                                    )),
                                    const SizedBox(width: 15),
                                    Expanded(
                                        child: Text(
                                      displayOrderData.orderData.adminNote,
                                      style: GoogleFonts.mulish(
                                          fontSize: 14.5,
                                          color: const Color(0xff3E3E3E)),
                                    )),
                                    const SizedBox(width: 15),
                                    // Expanded(
                                    //     child: Text(
                                    //   displayOrderData.orderData.currentStatus ?? '-',
                                    //   style: GoogleFonts.mulish(
                                    //       fontSize: 14.5, color: const Color(0xff3E3E3E)),
                                    // )),
                                    // const SizedBox(width: 15),

                                    Expanded(
                                      child: Text(
                                        displayOrderData.orderData.deliveryDate
                                                ?.convertToDDMMYY() ??
                                            '-',
                                        style: GoogleFonts.mulish(
                                            fontSize: 14.5,
                                            color: const Color(0xff3E3E3E)),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            }),
                            SizedBox(height: 18),
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 18.0),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  Text.rich(
                                    TextSpan(children: [
                                      const TextSpan(
                                          text: 'Vendor total:  ',
                                          style: TextStyle(
                                              fontWeight: FontWeight.w500)),
                                      const TextSpan(
                                          text: ' ₹ ',
                                          style: TextStyle(
                                              fontSize: 13,
                                              fontWeight: FontWeight.w600)),
                                      TextSpan(
                                          text: formatPriceWithCommas(
                                              allOrderedProductDetails.map((e) {
                                            return (e.orderData.qty *
                                                e.orderData.purchasePrice);
                                          }).fold(
                                                  0,
                                                  (previousValue, element) =>
                                                      previousValue +
                                                      element.toInt())),
                                          style: const TextStyle(
                                              fontWeight: FontWeight.w600)),
                                    ]),
                                  ),
                                  SizedBox(width: 25),
                                  Text.rich(
                                    TextSpan(children: [
                                      const TextSpan(
                                          text: 'Sub total:  ',
                                          style: TextStyle(
                                              fontWeight: FontWeight.w500)),
                                      const TextSpan(
                                          text: ' \$ ',
                                          style: TextStyle(
                                              fontSize: 12.2,
                                              fontWeight: FontWeight.w600)),
                                      TextSpan(
                                          text: formatPriceWithCommas(
                                              allOrderedProductDetails
                                                  .map((e) => (e.orderData.qty *
                                                      e.orderData.sellingPrice))
                                                  .fold(
                                                      0,
                                                      (previousValue,
                                                              element) =>
                                                          previousValue +
                                                          element.toInt())),
                                          style: const TextStyle(
                                              fontWeight: FontWeight.w600)),
                                    ]),
                                  ),
                                ],
                              ),
                            )
                          ],
                        )),
                  ],
                ),
              )
            : const Center(child: Text('No Order Found'));
  }

  Future<dynamic> addEditDeliveryDetailsDialog(
      BuildContext context, DeliveryDetailsModel? deliveryDetails) {
    final deliveryPartnerCtrl = TextEditingController();
    final trackingIdCtrl = TextEditingController();
    final trackingLinkCtrl = TextEditingController();
    final chargeCtrl = TextEditingController();
    final locationCtrl = TextEditingController();
    DateTime? dispatchedOn;
    DateTime? deliveredOn;
    bool isDelivered = false;
    List<DeliveryProductData> selectedDeliveryProducts = [];

    if (deliveryDetails != null) {
      deliveryPartnerCtrl.text = deliveryDetails.deliveryPartner;
      trackingIdCtrl.text = deliveryDetails.trackingId;
      trackingLinkCtrl.text = deliveryDetails.trackingLink;
      chargeCtrl.text = deliveryDetails.charges.toString();
      locationCtrl.text = deliveryDetails.location;
      dispatchedOn = deliveryDetails.dispatchedOn;
      deliveredOn = deliveryDetails.deliveredOn;
      isDelivered = deliveryDetails.isDelivered;
      selectedDeliveryProducts.clear();
      selectedDeliveryProducts.addAll(deliveryDetails.dispatchedProduct);
    }

    return showDialog(
      context: context,
      builder: (context) {
        bool isLoading = false;
        return StatefulBuilder(builder: (context, setState2) {
          return AlertDialog(
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            title: Text('Delivery Details',
                style:
                    GoogleFonts.zcoolXiaoWei(fontSize: 25, color: themeColor)),
            content: Container(
              constraints: const BoxConstraints(maxWidth: 800),
              width: double.maxFinite,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          controller: deliveryPartnerCtrl,
                          decoration: inpDecor()
                              .copyWith(labelText: 'Delivery Partner'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: TextFormField(
                          controller: locationCtrl,
                          decoration: inpDecor()
                              .copyWith(labelText: 'Delivery Location'),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 15),
                  Row(
                    children: [
                      Expanded(
                        child: InkWell(
                          onTap: () async {
                            final res = await showDatePicker(
                                context: context,
                                firstDate: DateTime(DateTime.now().year,
                                    DateTime.now().month - 1),
                                lastDate: DateTime.now());
                            dispatchedOn = res;
                            setState2(() {});
                          },
                          child: TextFormField(
                            enabled: false,
                            controller: TextEditingController(
                                text: dispatchedOn != null
                                    ? dispatchedOn!.goodDayDate()
                                    : '-'),
                            style: const TextStyle(color: Color(0xff3E3E3E)),
                            decoration: inpDecor().copyWith(
                                labelStyle:
                                    const TextStyle(color: Colors.black),
                                labelText: 'Dispatch Date',
                                suffixIcon:
                                    const Icon(CupertinoIcons.calendar)),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: TextFormField(
                          controller: chargeCtrl,
                          decoration: inpDecor().copyWith(labelText: 'Charge'),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 15),
                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          controller: trackingIdCtrl,
                          decoration:
                              inpDecor().copyWith(labelText: 'Tracking Id'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: TextFormField(
                          controller: trackingLinkCtrl,
                          decoration:
                              inpDecor().copyWith(labelText: 'Tracking Link'),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 15),
                  if (deliveryDetails != null)
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            clipBehavior: Clip.antiAlias,
                            height: 48,
                            padding: const EdgeInsets.only(left: 3),
                            decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey.shade400),
                                borderRadius: BorderRadius.circular(7),
                                // color: const Color.fromARGB(9, 0, 0, 0),
                                color: Colors.transparent),
                            child: Row(
                              children: [
                                Checkbox(
                                  side: const BorderSide(color: Colors.grey),
                                  checkColor: Colors.white,
                                  activeColor: themeColor,
                                  hoverColor: Colors.transparent,
                                  // focusColor: Colors.transparent,
                                  overlayColor: const WidgetStatePropertyAll(
                                      Colors.transparent),
                                  value: isDelivered,
                                  onChanged: (value) async {
                                    isDelivered = value!;
                                    setState2(() {});
                                  },
                                ),
                                const SizedBox(width: 5),
                                const Text(
                                  "Delivered",
                                  style: TextStyle(color: Colors.black),
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: InkWell(
                            onTap: () async {
                              final res = await showDatePicker(
                                  context: context,
                                  firstDate: DateTime(DateTime.now().year,
                                      DateTime.now().month - 1),
                                  lastDate: DateTime.now());
                              deliveredOn = res;
                              setState2(() {});
                            },
                            child: TextFormField(
                              enabled: false,
                              controller: TextEditingController(
                                  text: deliveredOn != null
                                      ? deliveredOn!.goodDayDate()
                                      : '-'),
                              style: const TextStyle(color: Color(0xff3E3E3E)),
                              decoration: inpDecor().copyWith(
                                  labelStyle:
                                      const TextStyle(color: Colors.black),
                                  labelText: 'Delivered On',
                                  suffixIcon:
                                      const Icon(CupertinoIcons.calendar)),
                            ),
                          ),
                        ),
                      ],
                    ),
                  const SizedBox(height: 5),
                  const Text(
                    'Delivery Products',
                    style: TextStyle(fontSize: 18),
                  ),
                  const SizedBox(height: 10),
                  Column(
                    children: [
                      const Row(
                        children: [
                          Text('Image'),
                          SizedBox(width: 15),
                          Expanded(
                            child: Text('Product Code'),
                          ),
                          SizedBox(width: 15),
                          Expanded(flex: 2, child: Text('Product Name')),
                          SizedBox(width: 15),
                          Expanded(child: Text('Vendor Name')),
                          SizedBox(width: 15),
                          Expanded(child: Text('Ordered Qty')),
                          SizedBox(width: 15),
                          Expanded(child: Text('Dispatch Qty')),
                          SizedBox(width: 15),
                          Expanded(child: Text('Status')),
                        ],
                      ),
                      const SizedBox(height: 8),
                      const Divider(height: 0),
                      ...List.generate(
                        selectedDeliveryProducts.length,
                        (index) {
                          final displayProductData = allOrderedProductDetails
                              .firstWhereOrNull((element) =>
                                  element.orderData.id ==
                                  selectedDeliveryProducts[index].id);
                          if (deliveryDetails == null &&
                              selectedDeliveryProducts[index].qty != 0) {
                            selectedDeliveryProducts[index].qty =
                                selectedDeliveryProducts[index].qty != 0
                                    ? selectedDeliveryProducts[index].qty
                                    : displayProductData?.orderData.qty ?? 0;
                            for (var deliveryDetail in allDeliveryDetails) {
                              if (deliveryDetail.dispatchedProduct
                                  .map((e) => e.id)
                                  .contains(
                                      selectedDeliveryProducts[index].id)) {
                                for (var element
                                    in deliveryDetail.dispatchedProduct) {
                                  if (element.id ==
                                      selectedDeliveryProducts[index].id) {
                                    selectedDeliveryProducts[index].qty -=
                                        element.qty;
                                  }
                                }
                              }
                            }
                          }
                          return Padding(
                            padding: const EdgeInsets.only(top: 8.0),
                            child: Row(
                              children: [
                                Container(
                                  height: 50,
                                  width: 50,
                                  clipBehavior: Clip.antiAlias,
                                  decoration: const BoxDecoration(
                                      shape: BoxShape.circle),
                                  child: Image.network(
                                    displayProductData?.variant.images.first ??
                                        "",
                                    fit: BoxFit.cover,
                                    errorBuilder: (context, error, stackTrace) {
                                      return const Icon(CupertinoIcons.photo);
                                    },
                                  ),
                                ),
                                const SizedBox(width: 15),
                                Expanded(
                                  child: Text(
                                      displayProductData?.product.sku ?? '-'),
                                ),
                                const SizedBox(width: 15),
                                Expanded(
                                    flex: 2,
                                    child: Text(capilatlizeFirstLetter(
                                        displayProductData?.product.name ??
                                            '-'))),
                                const SizedBox(width: 15),
                                Expanded(
                                    child: Text(capilatlizeFirstLetter(
                                        displayProductData?.vendorData.name ??
                                            '-'))),
                                const SizedBox(width: 15),
                                Expanded(
                                    child: Text(capilatlizeFirstLetter(
                                        displayProductData?.orderData.qty
                                                .toString() ??
                                            '-'))),
                                const SizedBox(width: 15),
                                Expanded(
                                  child: TextFormField(
                                    controller: TextEditingController(
                                        text: selectedDeliveryProducts[index]
                                            .qty
                                            .toString()),
                                    onChanged: (value) {
                                      if (int.tryParse(value) != null) {
                                        selectedDeliveryProducts[index].qty =
                                            int.tryParse(value)!;
                                        // print(
                                        //     'in textfeild after change: ${displayOrderDetails.firstWhereOrNull((element) => element.orderData.id == selectedDeliveryProducts[index].id)?.orderData.qty}');
                                      }
                                    },
                                  ),
                                ),
                                const SizedBox(width: 15),
                                Expanded(
                                  child: Text(
                                    capilatlizeFirstLetter(displayProductData
                                            ?.orderData.currentStatus ??
                                        '-'),
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      )
                    ],
                  )
                ],
              ),
            ),
            actions: isLoading
                ? [
                    const SizedBox(
                      height: 28,
                      width: 28,
                      child: Center(
                        child: CircularProgressIndicator(),
                      ),
                    )
                  ]
                : [
                    TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: const Text('Cancel'),
                    ),
                    ElevatedButton(
                      onPressed: () async {
                        try {
                          if (isLoading) return;

                          isLoading = true;
                          final vendorOrderRef = FBFireStore.vendorOrder
                              .doc(vendorOrderModel?.docId);
                          setState2(() {});

                          final deliveryRef = FBFireStore.vendorOrder
                              .doc(vendorOrderModel?.docId)
                              .collection('deliveries')
                              .doc(deliveryDetails?.docId);

                          final deliveryData = {
                            'deliveryPartner': deliveryPartnerCtrl.text.trim(),
                            'trackingId': trackingIdCtrl.text.trim(),
                            'trackingLink': trackingLinkCtrl.text.trim(),
                            'dispatchedOn':
                                dispatchedOn?.millisecondsSinceEpoch,
                            'dispatchedProduct':
                                selectedDeliveryProducts.map((e) {
                              int allDeliveryDetailsQty = 0;
                              for (var deliveryDetail in allDeliveryDetails) {
                                for (var element
                                    in deliveryDetail.dispatchedProduct) {
                                  if (element.id == e.id) {
                                    allDeliveryDetailsQty += element.qty;
                                  }
                                }
                              }
                              allDeliveryDetailsQty += e.qty;
                              final originalProductQty =
                                  allOrderedProductDetails
                                      .firstWhere((element) =>
                                          element.orderData.id == e.id)
                                      .orderData
                                      .qty;
                              if (deliveryDetails != null) {
                                allDeliveryDetailsQty -
                                    deliveryDetails.dispatchedProduct
                                        .firstWhere((element) =>
                                            element.variantId == e.variantId)
                                        .qty;
                              }
                              print(allDeliveryDetailsQty < originalProductQty);
                              return e.toPartialDeliveredJson(
                                  allDeliveryDetailsQty < originalProductQty
                                      ? OrderProductStatus.partDeliver
                                      : isDelivered
                                          ? OrderProductStatus.delivered
                                          : OrderProductStatus.outfordeliver,
                                  e.qty);
                            }).toList(),
                            'teamMemberDocId': FBAuth.auth.currentUser?.uid,
                            'location': locationCtrl.text.trim(),
                            'orderId': vendorOrderModel?.docId,
                            'inquiryId': null,
                            'messageId': null,
                            'uId': '',
                            'charges':
                                num.tryParse(chargeCtrl.text.trim()) ?? 0,
                            'createdAt': DateTime.now().millisecondsSinceEpoch,
                            'deliveredOn': deliveredOn?.millisecondsSinceEpoch,
                            'isDelivered': isDelivered,
                          };

                          final batch = FBFireStore.fb.batch();
                          batch.update(vendorOrderRef, {
                            'orderProducts': allOrderedProductDetails.map((e) {
                              int allDeliveryDetailsQty = 0;
                              for (var deliveryDetail in allDeliveryDetails) {
                                for (var element
                                    in deliveryDetail.dispatchedProduct) {
                                  if (element.id == e.orderData.id) {
                                    allDeliveryDetailsQty += element.qty;
                                  }
                                }
                              }
                              if (deliveryDetails != null) {
                                allDeliveryDetailsQty -= deliveryDetails
                                    .dispatchedProduct
                                    .firstWhere((element) =>
                                        element.variantId ==
                                        e.orderData.variantId)
                                    .qty;
                              }

                              // allDeliveryDetailsQty += e.qty;
                              return selectedDeliveryProducts
                                      .map((ele) => ele.id)
                                      .contains(e.orderData.id)
                                  ? e.orderData.toPartialDeliveredJson(
                                      (allDeliveryDetailsQty +=
                                                  selectedDeliveryProducts
                                                      .firstWhere((element) =>
                                                          element.id ==
                                                          e.orderData.id)
                                                      .qty) <
                                              e.orderData.qty
                                          ? OrderProductStatus.partDeliver
                                          : isDelivered
                                              ? OrderProductStatus.delivered
                                              : OrderProductStatus
                                                  .outfordeliver,
                                      e.orderData.qty,
                                    )
                                  : e.orderData.toJson();
                            }),
                          });

                          batch.set(deliveryRef, deliveryData);

                          await batch.commit();
                          isLoading = false;
                          setState2(() {});
                          Navigator.of(context).pop();
                          getRequiredData();
                        } on Exception catch (e) {
                          debugPrint(e.toString());
                          isLoading = false;
                          setState2(() {});
                        }
                      },
                      child: const Text('Save'),
                    ),
                  ],
          );
        });
      },
    );
  }

  Future<dynamic> addEditTransactionDialog(
      BuildContext context, TransactionModel? transaction) {
    final amountCtrl = TextEditingController();
    // final paymentLinkCtrl = TextEditingController();
    final noteCtrl = TextEditingController();
    final transactionIdCtrl = TextEditingController();
    final methodCtrl = TextEditingController();
    bool isPaid = false;
    DateTime? paidOn;

    if (transaction != null) {
      amountCtrl.text = transaction.amount.toString();
      // paymentLinkCtrl.text = transaction.paymentLink;
      noteCtrl.text = transaction.note;
      transactionIdCtrl.text = transaction.transactionId ?? '';
      methodCtrl.text = transaction.method ?? '';
      isPaid = transaction.isPaid;
      paidOn = transaction.paymentTime;
    }

    return showDialog(
      context: context,
      builder: (context) {
        bool isLoading = false;
        return StatefulBuilder(builder: (context, setState2) {
          return AlertDialog(
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            title: Text('Transaction',
                style:
                    GoogleFonts.zcoolXiaoWei(fontSize: 25, color: themeColor)),
            content: Container(
              constraints: const BoxConstraints(maxWidth: 800),
              width: double.maxFinite,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          controller: amountCtrl,
                          decoration: inpDecor().copyWith(
                              labelText: 'Amount(₹)', hintText: ' in Rs.'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: TextFormField(
                          controller: noteCtrl,
                          decoration: inpDecor().copyWith(labelText: 'Note'),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 15),
                  // TextFormField(
                  //   controller: paymentLinkCtrl,
                  //   decoration: inpDecor().copyWith(labelText: 'Payment Link'),
                  // ),

                  // const SizedBox(height: 15),
                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          controller: transactionIdCtrl,
                          decoration:
                              inpDecor().copyWith(labelText: 'Transaction Id'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: TextFormField(
                          controller: methodCtrl,
                          decoration:
                              inpDecor().copyWith(labelText: 'Payment Mode'),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 15),
                  Row(
                    children: [
                      Expanded(
                        child: Container(
                          clipBehavior: Clip.antiAlias,
                          height: 48,
                          padding: const EdgeInsets.only(left: 3),
                          decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey.shade400),
                              borderRadius: BorderRadius.circular(7),
                              // color: const Color.fromARGB(9, 0, 0, 0),
                              color: Colors.transparent),
                          child: Row(
                            children: [
                              Checkbox(
                                side: const BorderSide(color: Colors.grey),
                                checkColor: Colors.white,
                                activeColor: themeColor,
                                hoverColor: Colors.transparent,
                                // focusColor: Colors.transparent,
                                overlayColor: const WidgetStatePropertyAll(
                                    Colors.transparent),
                                value: isPaid,
                                onChanged: (value) async {
                                  isPaid = value!;
                                  setState2(() {});
                                },
                              ),
                              const SizedBox(width: 5),
                              const Text(
                                "Payment Paid",
                                style: TextStyle(color: Colors.black),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: InkWell(
                          onTap: () async {
                            final res = await showDatePicker(
                                context: context,
                                firstDate: DateTime(2000),
                                lastDate: DateTime.now());
                            paidOn = res;
                            setState2(() {});
                          },
                          child: TextFormField(
                            controller: TextEditingController(
                                text: paidOn?.convertToDDMMYY() ?? ''),
                            enabled: false,
                            style: const TextStyle(color: Colors.black),
                            decoration: inpDecor().copyWith(
                                labelText: 'paid on',
                                labelStyle:
                                    const TextStyle(color: Colors.black),
                                suffixIcon: const Icon(
                                  CupertinoIcons.calendar,
                                  color: Colors.black,
                                )),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            actions: isLoading
                ? [
                    const SizedBox(
                      height: 28,
                      width: 28,
                      child: Center(
                        child: CircularProgressIndicator(),
                      ),
                    )
                  ]
                : [
                    TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: const Text('Cancel'),
                    ),
                    ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        elevation: 0,
                        backgroundColor: themeColor,
                        foregroundColor: Colors.white,
                        surfaceTintColor: themeColor,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(5),
                        ),
                      ),
                      onPressed: () async {
                        try {
                          if (amountCtrl.text.trim().isEmpty) {
                            showErrorAppSnackBar(context, 'Amount Missing');
                            return;
                          }
                          if (num.tryParse(amountCtrl.text.trim()) == null) {
                            showErrorAppSnackBar(
                                context, 'Amount in Numbers only');
                            return;
                          }
                          if (isLoading) return;

                          isLoading = true;
                          setState2(() {});

                          final transactionRef = transaction != null
                              ? FBFireStore.vendorOrder
                                  .doc(vendorOrderModel?.docId)
                                  .collection('transaction')
                                  .doc(transaction.docId)
                              : FBFireStore.vendorOrder
                                  .doc(vendorOrderModel?.docId)
                                  .collection('transaction')
                                  .doc();

                          final transactionData = {
                            'amount': num.tryParse(amountCtrl.text),
                            'orderId': vendorOrderModel?.orderId,
                            'isDebit': true,
                            'paymentTime': paidOn?.millisecondsSinceEpoch,
                            'note': noteCtrl.text,
                            'uId': '',
                            'isPaid': isPaid,
                            'paymentLink': '',
                            'transactionId': transaction != null
                                ? transactionIdCtrl.text.trim()
                                : null,
                            'method': transaction != null
                                ? methodCtrl.text.trim()
                                : null,
                            'createdAt':
                                transaction?.createdAt.millisecondsSinceEpoch ??
                                    DateTime.now().millisecondsSinceEpoch,
                            'inquiryId': '',
                            'messageId': '',
                          };

                          final batch = FBFireStore.fb.batch();
                          batch.set(transactionRef, transactionData);

                          await batch.commit();
                          isLoading = false;
                          setState2(() {});
                          Navigator.of(context).pop();
                          getRequiredData();
                        } on Exception catch (e) {
                          debugPrint(e.toString());
                          isLoading = false;
                          setState2(() {});
                        }
                      },
                      child: const Text('Add'),
                    ),
                  ],
          );
        });
      },
    );
  }
}
