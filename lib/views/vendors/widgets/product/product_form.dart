import 'package:cached_network_image/cached_network_image.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:multi_dropdown/multiselect_dropdown.dart';
import 'package:pluto_grid/pluto_grid.dart';
import 'package:wedding_super_admin/controller/home_ctrl.dart';
import 'package:wedding_super_admin/shared/const.dart';
import 'package:wedding_super_admin/views/vendors/widgets/product/product_tile.dart';
import '../../../../models/product.dart';
import '../../../../models/subcategories.dart';
import '../../../../models/variants.dart';
import '../../../../services/image_picker.dart';
import '../../../../shared/firebase.dart';
import '../../../../shared/methods.dart';
import '../../../../shared/theme.dart';
import '../../../common/form_header_tile.dart';
import '../../../common/table_header.dart';

// IMP: extraIds extraids[0] = vendor Id and extraids[1] = subcat id

class ProductForm extends StatefulWidget {
  const ProductForm({super.key, this.productId, required this.extraIds});
  final String? productId;
  final List<String?> extraIds;
  @override
  State<ProductForm> createState() => _ProductFormState();
}

class _ProductFormState extends State<ProductForm>
    with TickerProviderStateMixin {
  late final TabController _tabController;
  ProductModel? product;

  final productNameCtrl = TextEditingController();
  final productSkuCtrl = TextEditingController();
  final productDescCtrl = TextEditingController();
  bool topSelling = false;
  bool onSubmitLoad = false;
  bool productshow = false;
  String? defaultProductImage;
  NewVariantModel? radioSelectedVariant;
  List<SubCategory> selSubCats = [];
  List<SubCategory> extraSubCatsList = [];
  List<NewVariantModel> variantsInputList = [];
  List<SubCategory> vendorSubCategories = <SubCategory>[];
  SubCategory? selDefaultSubCategory;
  bool dataLoaded = false;
  List<Map<String, dynamic>> subCatDetailTypes = [];
  List<String> selDetailTypes = [];
  List<String> tags = [];
  TextEditingController tagCtrl = TextEditingController();
  FocusNode myFocusNode = FocusNode();
  List<PlutoColumn> columns = [];
  List<PlutoRow> rows = [];
  Key gridKey = UniqueKey();
  int? selectedRowKey;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    setSubCategoryData();
    getProductData();
  }

  void updateGrid() {
    columns = [
      PlutoColumn(
        title: 'Default',
        field: 'defaultt',
        type: PlutoColumnType.text(),
        enableEditingMode: false,
        renderer: (ctx) {
          // final isSelected = ctx.row.key.toString() == selectedRowKey;
          return Radio(
            value: ctx.rowIdx,
            groupValue: selectedRowKey,
            onChanged: (value) {
              radioSelectedVariant = variantsInputList[ctx.rowIdx];
              selectedRowKey = value;
              setState(() {});
              // setState(() {

              //   // Update all rows to reflect the new selection
              //   for (final row in ctx.stateManager.refRows) {
              //     row.cells['selected']!.value =
              //         row.key.toString() == selectedRowKey ? 'true' : 'false';
              //   }

              //   ctx.stateManager.notifyListeners();
              // });
            },
          );
        },
      ),
      PlutoColumn(
        title: 'Sr No',
        field: 'srNo',
        type: PlutoColumnType.number(),
        enableSorting: false,
      ),
      PlutoColumn(
        title: 'Product Image\'s',
        field: 'images',
        enableEditingMode: false,
        enableSorting: false,
        type: PlutoColumnType.text(),
        renderer: (rendererContext) {
          final rowIdx = rendererContext.rowIdx;
          // variantsInputList[rowIdx].defaultt = true;
          final List<DisplayImageModel> images =
              List<DisplayImageModel>.from(rendererContext.cell.value ?? []);
          return GestureDetector(
            onTap: () {
              addEditVariantImages(images, rowIdx);
            },
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: images.isNotEmpty
                    ? images
                        .map(
                          (displayImage) => Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 4),
                            child: displayImage.selectedImage != null
                                ? Image.memory(
                                    displayImage.selectedImage!.uInt8List,
                                    width: 40,
                                    height: 40,
                                    fit: BoxFit.cover,
                                    errorBuilder: (_, __, ___) =>
                                        const Icon(Icons.error),
                                  )
                                : Image.network(
                                    displayImage.url ?? '',
                                    width: 40,
                                    height: 40,
                                    fit: BoxFit.cover,
                                    errorBuilder: (_, __, ___) =>
                                        const Icon(Icons.error),
                                  ),
                          ),
                        )
                        .toList()
                    : [
                        const Padding(
                          padding: EdgeInsets.all(8.0),
                          child: Icon(Icons.add_a_photo,
                              size: 24, color: Colors.grey),
                        ),
                      ],
              ),
            ),
          );
        },
      ),
      ...selDetailTypes.map((field) {
        return PlutoColumn(
          title: capilatlizeFirstLetter(field),
          field: field,
          type: PlutoColumnType.text(),
          enableSorting: false,
        );
      }),
      PlutoColumn(
        title: 'Available',
        field: 'show',
        enableSorting: false,
        type: PlutoColumnType.select(
          ['Available', 'Not Available'],
          defaultValue: 'Available',
        ),
      ),
      PlutoColumn(
        title: 'Description',
        field: 'description',
        type: PlutoColumnType.text(),
        enableSorting: false,
      ),
      PlutoColumn(
        enableSorting: false,
        title: 'Purchase Price (₹)',
        field: 'fixedprice',
        type: PlutoColumnType.currency(symbol: '₹'),
      ),
      PlutoColumn(
        enableSorting: false,
        title: 'Original Price (₹)',
        field: 'originalprice',
        type: PlutoColumnType.currency(symbol: '₹'),
      ),
      PlutoColumn(
        enableSorting: false,
        title: 'Selling Price (\$)',
        field: 'sellprice',
        type: PlutoColumnType.currency(symbol: '\$'),
      ),
      PlutoColumn(
        enableSorting: false,
        title: 'Price Type',
        field: 'priceType',
        type: PlutoColumnType.select(priceTypes.map((e) => e).toList()),
      ),
      PlutoColumn(
        title: 'Delete',
        field: 'delete',
        type: PlutoColumnType.text(),
        renderer: (rendererContext) {
          return IconButton(
            icon: Icon(Icons.delete, color: Colors.red),
            onPressed: () {
              final rowIdx = rendererContext.rowIdx;
              // setState(() {
              //   myList.removeAt(index);
              //   rows.removeAt(index);
              // });
              showDialog(
                context: context,
                builder: (context) {
                  bool loader = false;
                  return StatefulBuilder(
                    builder: (context, setState2) {
                      return AlertDialog(
                        title: Text("Delete"),
                        content:
                            Text("Are you sure you want to delete variant?"),
                        actions: loader
                            ? [
                                SizedBox(
                                  height: 25,
                                  width: 25,
                                  child: Center(
                                    child: CircularProgressIndicator(
                                      strokeWidth: 3.5,
                                    ),
                                  ),
                                )
                              ]
                            : [
                                TextButton(
                                    onPressed: () {
                                      setState2(() {
                                        loader = true;
                                      });
                                      variantsInputList.removeAt(rowIdx);
                                      setState2(() {
                                        loader = false;
                                      });
                                      Navigator.of(context).pop();
                                      gridKey = UniqueKey();
                                      updateGrid();
                                      setState(() {});
                                    },
                                    child: Text('Yes')),
                                TextButton(
                                    onPressed: () {
                                      Navigator.of(context).pop();
                                    },
                                    child: Text('No')),
                              ],
                      );
                    },
                  );
                },
              );
            },
          );
        },
      ),
    ];
    rows = List.generate(
      variantsInputList.length,
      (index) {
        final displayImages = <DisplayImageModel>[];
        displayImages.addAll(variantsInputList[index]
            .images
            .map((e) => DisplayImageModel(url: e, selectedImage: null)));
        displayImages.addAll(variantsInputList[index]
            .newImages
            .map((e) => DisplayImageModel(url: null, selectedImage: e)));
        return PlutoRow(
          cells: {
            'defaultt': PlutoCell(
                value: variantsInputList[index].id == radioSelectedVariant?.id),
            'srNo': PlutoCell(value: '${index + 1}'),
            for (var key in selDetailTypes)
              key: PlutoCell(
                  value: variantsInputList[index].detailTypes[key] ?? ''),
            'show': PlutoCell(
                value: variantsInputList[index].show
                    ? 'Available'
                    : 'Not Available'),
            'description':
                PlutoCell(value: variantsInputList[index].description),
            'fixedprice': PlutoCell(value: variantsInputList[index].fixedprice),
            'originalprice':
                PlutoCell(value: variantsInputList[index].originalPrice),
            'sellprice':
                PlutoCell(value: variantsInputList[index].sellingPrice),
            'priceType': PlutoCell(value: variantsInputList[index].priceType),
            'images': PlutoCell(value: displayImages),
            'delete': PlutoCell(value: ''),
          },
        );
      },
    );
    selectedRowKey = variantsInputList
        .indexWhere((element) => element.id == radioSelectedVariant?.id);
  }

  Future<dynamic> addEditVariantImages(
      List<DisplayImageModel> images, int rowIdx) {
    bool loading = false;
    return showDialog(
      context: context,
      builder: (context) {
        final tempImages = <DisplayImageModel>[];
        tempImages.addAll(images);
        return StatefulBuilder(builder: (context, setState2) {
          return Dialog(
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            child: Container(
              padding: const EdgeInsets.all(15),
              constraints: const BoxConstraints(maxHeight: 800, maxWidth: 1000),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        'Images',
                        style: GoogleFonts.zcoolXiaoWei(
                            fontSize: 23, color: themeColor),
                      ),
                      const Spacer(),
                      ElevatedButton.icon(
                        icon: const Icon(CupertinoIcons.add),
                        onPressed: () async {
                          final result = await ImagePickerService()
                              .pickImageAndCrop(context, useCompressor: true);
                          tempImages.addAll(result.map((e) =>
                              DisplayImageModel(url: null, selectedImage: e)));
                          setState2(() {});
                        },
                        label: const Text('Add New'),
                      )
                    ],
                  ),
                  const SizedBox(height: 15),
                  StaggeredGrid.extent(
                    mainAxisSpacing: 15,
                    maxCrossAxisExtent: 150,
                    crossAxisSpacing: 15,
                    children: tempImages
                        .map(
                          (displayImage) => displayImage.selectedImage != null
                              ? Stack(
                                  children: [
                                    Container(
                                      decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(7)),
                                      clipBehavior: Clip.antiAlias,
                                      child: Image.memory(
                                        displayImage.selectedImage!.uInt8List,
                                        width: double.maxFinite,
                                        height: 250,
                                        fit: BoxFit.cover,
                                        errorBuilder: (_, __, ___) =>
                                            const Icon(Icons.error),
                                      ),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: Align(
                                        alignment: Alignment.topRight,
                                        child: InkWell(
                                          onTap: () {
                                            tempImages.remove(displayImage);
                                            setState2(() {});
                                          },
                                          child: Container(
                                            decoration: const BoxDecoration(
                                              shape: BoxShape.circle,
                                              color: Colors.white,
                                            ),
                                            padding: const EdgeInsets.all(2),
                                            child: const Icon(
                                              CupertinoIcons.xmark,
                                              size: 15,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                )
                              : Stack(
                                  children: [
                                    Container(
                                      decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(7)),
                                      clipBehavior: Clip.antiAlias,
                                      child: Image.network(
                                        displayImage.url ?? '',
                                        width: double.maxFinite,
                                        height: 250,
                                        fit: BoxFit.cover,
                                        errorBuilder: (_, __, ___) =>
                                            const Icon(Icons.error),
                                      ),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: Align(
                                        alignment: Alignment.topRight,
                                        child: InkWell(
                                          onTap: () {
                                            tempImages.remove(displayImage);
                                            setState2(() {});
                                          },
                                          child: Container(
                                            decoration: const BoxDecoration(
                                              shape: BoxShape.circle,
                                              color: Colors.white,
                                            ),
                                            padding: const EdgeInsets.all(2),
                                            child: const Icon(
                                              CupertinoIcons.xmark,
                                              size: 15,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                        )
                        .toList(),
                  ),
                  const SizedBox(height: 15),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: loading
                        ? [
                            const SizedBox(
                              height: 25,
                              width: 25,
                              child: Center(
                                child:
                                    CircularProgressIndicator(strokeWidth: 3.5),
                              ),
                            )
                          ]
                        : [
                            ElevatedButton(
                                onPressed: () async {
                                  if (loading) return;
                                  setState2(() {
                                    loading = true;
                                  });
                                  variantsInputList[rowIdx].images.clear();
                                  variantsInputList[rowIdx].newImages.clear();
                                  variantsInputList[rowIdx].images.addAll(
                                      tempImages
                                          .where(
                                              (element) => element.url != null)
                                          .map((e) => e.url!)
                                          .toList());
                                  variantsInputList[rowIdx].newImages.addAll(
                                      tempImages
                                          .where((element) =>
                                              element.selectedImage != null)
                                          .map((e) => e.selectedImage!)
                                          .toList());
                                  setState2(() {
                                    loading = false;
                                  });
                                  Navigator.of(context).pop();
                                  gridKey = UniqueKey();
                                  updateGrid();
                                  setState(() {});
                                },
                                child: const Text("Save"))
                          ],
                  ),
                ],
              ),
            ),
          );
        });
      },
    );
  }

  setSubCategoryData() {
    final hctrl = Get.find<HomeCtrl>();
    vendorSubCategories = hctrl.subcategories.where(
      (subcat) {
        final vendorSubCatList = hctrl.vendors
                .firstWhereOrNull(
                    (element) => element.docId == widget.extraIds[0])
                ?.subcatids ??
            [];
        return vendorSubCatList.contains(subcat.docId);
      },
    ).toList();
    selDefaultSubCategory = vendorSubCategories
        .firstWhereOrNull((element) => element.docId == widget.extraIds[1]);
    subCatDetailTypes = selDefaultSubCategory?.detailTypes ?? [];
    subCatDetailTypes.sort((a, b) =>
        (a['detailType'] as String).compareTo(b['detailType'] as String));
  }

  getProductData() async {
    if (widget.productId == null) {
      updateGrid();

      dataLoaded = true;
      setState(() {});
      return;
    }

    final productSnap = await FBFireStore.products.doc(widget.productId).get();

    product = productSnap.data() != null
        ? ProductModel.fromDocSnap(productSnap)
        : null;

    if (product != null) {
      productSkuCtrl.text = product?.sku ?? "";
      productNameCtrl.text = product?.name ?? "";
      productDescCtrl.text = product?.description ?? "";
      productshow = product?.show ?? true;
      topSelling = product?.topSelling ?? false;
      selDefaultSubCategory = vendorSubCategories.firstWhereOrNull(
          (element) => element.docId == product?.defaultSubCatDocId);
      extraSubCatsList.clear();
      extraSubCatsList.addAll(vendorSubCategories);
      extraSubCatsList.removeWhere(
          (element) => element.docId == selDefaultSubCategory?.docId);
      selSubCats.clear();
      selSubCats.addAll(extraSubCatsList.where(
          (element) => product?.subcatIds.contains(element.docId) ?? false));
      selDetailTypes.clear();
      selDetailTypes.addAll(product?.detailsList ?? []);
      tags.clear();
      tags.addAll(product?.tags ?? []);
      final variants = await getVaiantData();

      variantsInputList.clear();
      variantsInputList.addAll(variants);
      radioSelectedVariant = variantsInputList
          .firstWhereOrNull((element) => element.defaultt && element.show);
      gridKey = UniqueKey();
      updateGrid();
    }
    dataLoaded = true;
    setState(() {});
  }

  getVaiantData() async {
    final variantSnap = await FBFireStore.variants
        .where('productId', isEqualTo: widget.productId)
        .get();

    List<NewVariantModel> variants =
        variantSnap.docs.map((e) => NewVariantModel.fromDocSnap(e)).toList();

    return variants;
  }

  final scrCtrl = ScrollController();

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.sizeOf(context).height;
    extraSubCatsList.clear();
    extraSubCatsList.addAll(vendorSubCategories);
    extraSubCatsList.removeWhere(
        (element) => element.docId == selDefaultSubCategory?.docId);
    return dataLoaded
        ? true
            ? SingleChildScrollView(
                padding:
                    const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            IconButton(
                                onPressed: () {
                                  context.pop();
                                },
                                icon: const Icon(
                                  CupertinoIcons.arrow_left,
                                  color: themeColor,
                                )),
                            const SizedBox(width: 10),
                            Text('Product Form',
                                style: GoogleFonts.zcoolXiaoWei(
                                  fontSize: 35,
                                  color: themeColor,
                                )),
                          ],
                        ),
                        onSubmitLoad
                            ? const Center(
                                child: SizedBox(
                                    height: 25,
                                    width: 25,
                                    child: CircularProgressIndicator(
                                        strokeWidth: 2.5, color: themeColor)),
                              )
                            : ElevatedButton.icon(
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: themeColor,
                                  elevation: 0,
                                  foregroundColor: Colors.white,
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(4)),
                                ),
                                onPressed: onSubmit,
                                icon: const Icon(
                                  CupertinoIcons.checkmark_alt,
                                  size: 20,
                                ),
                                label: Padding(
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 8.0),
                                  child: Text("Save",
                                      style: GoogleFonts.livvic(
                                          letterSpacing: 1.3,
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500)),
                                ),
                              ),
                      ],
                    ),
                    if (widget.productId != null) ...[
                      const SizedBox(height: 20),
                      Row(
                        children: [
                          Container(
                            height: 35,
                            padding: const EdgeInsets.symmetric(horizontal: 10),
                            decoration: BoxDecoration(
                                color: dashboardColor,
                                borderRadius: BorderRadius.circular(8)),
                            child: Row(
                              children: [
                                // const Icon(
                                //   CupertinoIcons.pen,
                                //   color: themeColor,
                                //   size: 20,
                                // ),
                                // const SizedBox(width: 10),
                                Text(Get.find<HomeCtrl>()
                                        .offerings
                                        .firstWhereOrNull((element) =>
                                            element.docId ==
                                            product?.mainCatDocId)
                                        ?.name ??
                                    ""),
                              ],
                            ),
                          ),
                          const SizedBox(width: 15),
                          Container(
                            height: 35,
                            padding: const EdgeInsets.symmetric(horizontal: 10),
                            decoration: BoxDecoration(
                                color: dashboardColor,
                                borderRadius: BorderRadius.circular(8)),
                            child: Row(
                              children: [
                                // const Icon(
                                //   CupertinoIcons.pen,
                                //   color: themeColor,
                                //   size: 20,
                                // ),
                                // const SizedBox(width: 10),
                                Text(Get.find<HomeCtrl>()
                                        .subcategories
                                        .firstWhereOrNull((element) =>
                                            element.docId ==
                                            product?.defaultSubCatDocId)
                                        ?.name ??
                                    ""),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                    const SizedBox(height: 20),
                    Align(
                      alignment: Alignment.centerLeft,
                      child: SizedBox(
                        width: 400,
                        child: TabBar(
                          isScrollable: false,
                          indicatorSize: TabBarIndicatorSize.tab,
                          physics: NeverScrollableScrollPhysics(),
                          controller: _tabController,
                          tabs: <Widget>[
                            Tab(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(CupertinoIcons.person, size: 20),
                                  SizedBox(width: 8),
                                  Text(
                                    "Details",
                                    style: GoogleFonts.livvic(
                                      fontSize: 16,
                                      letterSpacing: .7,
                                      wordSpacing: 2,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Tab(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                      CupertinoIcons
                                          .list_bullet_below_rectangle,
                                      size: 20),
                                  SizedBox(width: 8),
                                  Text(
                                    "Variants",
                                    style: GoogleFonts.livvic(
                                      fontSize: 16,
                                      letterSpacing: .7,
                                      wordSpacing: 2,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 15),
                    Container(
                      padding: EdgeInsets.only(top: 20),
                      constraints:
                          BoxConstraints(maxHeight: screenHeight - 163),
                      child: TabBarView(
                        physics: NeverScrollableScrollPhysics(),
                        controller: _tabController,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const FormHeaderTile(title: 'Product Details'),
                              const SizedBox(height: 25),
                              Row(
                                children: [
                                  Expanded(
                                    child: TextFormField(
                                      controller: productSkuCtrl,
                                      cursorHeight: 20,
                                      decoration: inpDecor()
                                          .copyWith(labelText: 'Product SKU*'),
                                    ),
                                  ),
                                  const SizedBox(width: 15),
                                  Expanded(
                                    child: TextFormField(
                                      controller: productNameCtrl,
                                      cursorHeight: 20,
                                      decoration: inpDecor()
                                          .copyWith(labelText: 'Product Name*'),
                                    ),
                                  ),
                                  const SizedBox(width: 15),
                                  Expanded(
                                    flex: 2,
                                    child: TextFormField(
                                      controller: productDescCtrl,
                                      cursorHeight: 20,
                                      decoration: inpDecor().copyWith(
                                          labelText: 'Product Description*'),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 20),
                              Row(
                                children: [
                                  Expanded(
                                    child: Container(
                                      clipBehavior: Clip.antiAlias,
                                      height: 48,
                                      padding: const EdgeInsets.only(left: 3),
                                      decoration: BoxDecoration(
                                          border: Border.all(
                                              color: Colors.grey.shade400),
                                          borderRadius:
                                              BorderRadius.circular(7),
                                          color: Colors.transparent),
                                      child: Row(
                                        children: [
                                          Checkbox(
                                            side: const BorderSide(
                                                color: Colors.grey),
                                            checkColor: Colors.white,
                                            activeColor: themeColor,
                                            hoverColor: Colors.transparent,
                                            overlayColor:
                                                const WidgetStatePropertyAll(
                                                    Colors.transparent),
                                            value: topSelling,
                                            onChanged: (value) async {
                                              topSelling = value!;
                                              setState(() {});
                                            },
                                          ),
                                          const SizedBox(width: 5),
                                          const Text(
                                            "Top Selling",
                                            style:
                                                TextStyle(color: Colors.black),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 15),
                                  Expanded(
                                    child: Container(
                                      clipBehavior: Clip.antiAlias,
                                      height: 48,
                                      padding: const EdgeInsets.only(left: 3),
                                      decoration: BoxDecoration(
                                          border: Border.all(
                                              color: Colors.grey.shade400),
                                          borderRadius:
                                              BorderRadius.circular(7),
                                          color: Colors.transparent),
                                      child: Row(
                                        children: [
                                          Checkbox(
                                            side: const BorderSide(
                                                color: Colors.grey),
                                            checkColor: Colors.white,
                                            activeColor: themeColor,
                                            hoverColor: Colors.transparent,
                                            overlayColor:
                                                const WidgetStatePropertyAll(
                                                    Colors.transparent),
                                            value: productshow,
                                            onChanged: (value) async {
                                              productshow = value!;
                                              setState(() {});
                                            },
                                          ),
                                          const SizedBox(width: 5),
                                          const Text(
                                            "Available",
                                            style:
                                                TextStyle(color: Colors.black),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 15),
                                  Expanded(
                                    child: DropdownButtonHideUnderline(
                                        child: DropdownButtonFormField<
                                            SubCategory>(
                                      decoration: inpDecor()
                                          .copyWith(labelText: 'Sub-category'),
                                      hint: const Text("Select sub-category"),
                                      value: selDefaultSubCategory,
                                      items: [
                                        ...List.generate(
                                          vendorSubCategories.length,
                                          (index) {
                                            return DropdownMenuItem<
                                                    SubCategory>(
                                                value:
                                                    vendorSubCategories[index],
                                                child: Text(
                                                    capilatlizeFirstLetter(
                                                        vendorSubCategories[
                                                                index]
                                                            .name)));
                                          },
                                        )
                                      ],
                                      onChanged: (value) {
                                        selDefaultSubCategory = value;
                                        setState(() {});
                                      },
                                    )),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 20),
                              Row(
                                children: [
                                  Expanded(
                                    child: MultiSelectDropDown<SubCategory>(
                                      hint: 'Extra Sub-category',
                                      inputDecoration: BoxDecoration(
                                        color: Colors.transparent,
                                        border: Border.all(
                                            color: Colors.grey.shade400),
                                        borderRadius: BorderRadius.circular(7),
                                      ),
                                      selectedOptions: selSubCats
                                          .map((e) => ValueItem(
                                              label: e.name, value: e))
                                          .toList(),
                                      borderRadius: 6,
                                      searchEnabled: true,
                                      onOptionSelected: (options) {
                                        selSubCats.clear();
                                        for (var element in options) {
                                          selSubCats.addIf(
                                              element.value != null,
                                              element.value!);
                                        }
                                      },
                                      options: List.generate(
                                        extraSubCatsList.length,
                                        (index) {
                                          return ValueItem(
                                            label: extraSubCatsList[index].name,
                                            value: extraSubCatsList[index],
                                          );
                                        },
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 15),
                                  Expanded(
                                    child: MultiSelectDropDown<String>(
                                      hint: 'Detail Type',
                                      inputDecoration: BoxDecoration(
                                        color: Colors.transparent,
                                        border: Border.all(
                                            color: Colors.grey.shade400),
                                        borderRadius: BorderRadius.circular(7),
                                      ),
                                      selectedOptions: selDetailTypes
                                          .map((e) =>
                                              ValueItem(label: e, value: e))
                                          .toList(),
                                      borderRadius: 6,
                                      searchEnabled: true,
                                      onOptionSelected: (options) {
                                        selDetailTypes.clear();
                                        for (var element in options) {
                                          selDetailTypes.addIf(
                                              element.value != null,
                                              element.value!);
                                        }
                                        updateGrid();
                                        gridKey = UniqueKey();
                                        setState(() {});
                                      },
                                      options: List.generate(
                                        subCatDetailTypes.length,
                                        (index) {
                                          return ValueItem(
                                            label: subCatDetailTypes[index]
                                                ['detailType'],
                                            value: subCatDetailTypes[index]
                                                ['detailType'],
                                          );
                                        },
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 40),
                              const FormHeaderTile(
                                title: 'Tags',
                                button: false,
                              ),
                              const SizedBox(height: 25),
                              Row(
                                children: [
                                  Expanded(
                                    child: TextFormField(
                                      focusNode: myFocusNode,
                                      controller: tagCtrl,
                                      onFieldSubmitted: (value) async {
                                        if (value.trim().isEmpty) {
                                          return;
                                        }
                                        tags.add(value.toLowerCase().trim());
                                        tagCtrl.clear();
                                        myFocusNode.requestFocus();
                                        setState(() {});
                                      },
                                      decoration: inpDecor().copyWith(
                                          labelText: 'Enter tag name*',
                                          hintText: ' Hit enter once written'),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 15),
                              Wrap(
                                spacing: 10,
                                runSpacing: 10,
                                alignment: WrapAlignment.start,
                                runAlignment: WrapAlignment.start,
                                children: [
                                  ...List.generate(tags.length, (index) {
                                    return Chip(
                                      label: Text(
                                          capilatlizeFirstLetter(tags[index])),
                                      onDeleted: () async {
                                        tags.removeAt(index);
                                        setState(() {});
                                      },
                                    );
                                  })
                                ],
                              ),
                            ],
                          ),

                          SingleChildScrollView(
                            physics: ClampingScrollPhysics(),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                ElevatedButton.icon(
                                    style: ElevatedButton.styleFrom(
                                      elevation: 0,
                                      backgroundColor: themeColor,
                                      surfaceTintColor: themeColor,
                                      foregroundColor: Colors.white,
                                      shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(4)),
                                    ),
                                    onPressed: () async {
                                      List<Map<String, dynamic>>
                                          existingCombinations = [];
                                      for (var exiVariant
                                          in variantsInputList) {
                                        existingCombinations.add(
                                            Map<String, dynamic>.from(
                                                exiVariant.detailTypes));
                                      }

                                      Map<String, String> currentMap = {};
                                      currentMap.addEntries([
                                        ...selDetailTypes
                                            .map((e) => MapEntry(e, ''))
                                      ]);

                                      generateVariantDialog(context, currentMap,
                                          existingCombinations);
                                    },
                                    icon: const Icon(
                                      CupertinoIcons.add,
                                      size: 20,
                                    ),
                                    label: Padding(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 8.0),
                                      child: Text(
                                        "Generate",
                                        style: GoogleFonts.livvic(
                                            letterSpacing: 1.3,
                                            fontSize: 14,
                                            fontWeight: FontWeight.w500),
                                      ),
                                    )),
                                SizedBox(height: 15),
                                FormHeaderTile(
                                  title: 'Variants',
                                  button: true,
                                  buttonName: 'Add',
                                  icon: CupertinoIcons.add,
                                  onPressed: () async {
                                    if (selDetailTypes.isEmpty) {
                                      showErrorAppSnackBar(
                                          context, 'Detail type not selected');
                                    }
                                    if (selDetailTypes.isNotEmpty) {
                                      variantsInputList.add(
                                        NewVariantModel(
                                          originalPrice: 0,
                                          sellingPrice: 0,
                                          aqpiId: '',
                                          lowerName: '',
                                          docId: null,
                                          mainCatId: selDefaultSubCategory
                                                  ?.offeringId ??
                                              '',
                                          subCatId:
                                              selDefaultSubCategory?.docId ??
                                                  "",
                                          productId: widget.productId ?? "",
                                          id: getRandomId(8),
                                          newImages: [],
                                          images: [],
                                          description: '',
                                          defaultt: false,
                                          show: true,
                                          fixedprice: 0,
                                          priceType: '',
                                          priceRange: [],
                                          detailTypes: {},
                                        ),
                                      );
                                    }
                                    radioSelectedVariant =
                                        (variantsInputList.isNotEmpty &&
                                                variantsInputList.length == 1)
                                            ? variantsInputList[0]
                                            : radioSelectedVariant;
                                    updateGrid();
                                    gridKey = UniqueKey();
                                    setState(() {});
                                    await Future.delayed(
                                        const Duration(milliseconds: 200));
                                    scrCtrl.animateTo(
                                        scrCtrl.position.maxScrollExtent,
                                        duration: Durations.medium1,
                                        curve: Curves.linear);
                                  },
                                ),
                                const SizedBox(height: 25),
                                _productVariantsDetails(),
                              ],
                            ),
                          ),
                          // UserDetailsTabView(userDocId: widget.userDocId),
                          // UserOrdersTabView(userDocId: widget.userDocId),
                        ],
                      ),
                    ),
                  ],
                ),
              )
            : SingleChildScrollView(
                controller: scrCtrl,
                padding:
                    const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            IconButton(
                                onPressed: () {
                                  context.pop();
                                },
                                icon: const Icon(
                                  CupertinoIcons.arrow_left,
                                  color: themeColor,
                                )),
                            const SizedBox(width: 10),
                            Text('Product Form',
                                style: GoogleFonts.zcoolXiaoWei(
                                  fontSize: 35,
                                  color: themeColor,
                                )),
                          ],
                        ),
                        onSubmitLoad
                            ? const Center(
                                child: SizedBox(
                                    height: 25,
                                    width: 25,
                                    child: CircularProgressIndicator(
                                        strokeWidth: 2.5, color: themeColor)),
                              )
                            : ElevatedButton.icon(
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: themeColor,
                                  elevation: 0,
                                  foregroundColor: Colors.white,
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(4)),
                                ),
                                onPressed: onSubmit,
                                icon: const Icon(
                                  CupertinoIcons.checkmark_alt,
                                  size: 20,
                                ),
                                label: Padding(
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 8.0),
                                  child: Text("Save",
                                      style: GoogleFonts.livvic(
                                          letterSpacing: 1.3,
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500)),
                                ),
                              ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    Row(
                      children: [
                        Container(
                          height: 35,
                          padding: const EdgeInsets.symmetric(horizontal: 10),
                          decoration: BoxDecoration(
                              color: dashboardColor,
                              borderRadius: BorderRadius.circular(8)),
                          child: Row(
                            children: [
                              const Icon(
                                CupertinoIcons.pen,
                                color: themeColor,
                                size: 20,
                              ),
                              const SizedBox(width: 10),
                              Text(Get.find<HomeCtrl>()
                                      .offerings
                                      .firstWhereOrNull((element) =>
                                          element.docId ==
                                          product?.mainCatDocId)
                                      ?.name ??
                                  ""),
                            ],
                          ),
                        ),
                        const SizedBox(width: 15),
                        Container(
                          height: 35,
                          padding: const EdgeInsets.symmetric(horizontal: 10),
                          decoration: BoxDecoration(
                              color: dashboardColor,
                              borderRadius: BorderRadius.circular(8)),
                          child: Row(
                            children: [
                              const Icon(
                                CupertinoIcons.pen,
                                color: themeColor,
                                size: 20,
                              ),
                              const SizedBox(width: 10),
                              Text(Get.find<HomeCtrl>()
                                      .subcategories
                                      .firstWhereOrNull((element) =>
                                          element.docId ==
                                          product?.defaultSubCatDocId)
                                      ?.name ??
                                  ""),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Column(
                          children: [
                            const FormHeaderTile(title: 'Product Details'),
                            const SizedBox(height: 25),
                            Row(
                              children: [
                                Expanded(
                                  child: TextFormField(
                                    controller: productSkuCtrl,
                                    cursorHeight: 20,
                                    decoration: inpDecor()
                                        .copyWith(labelText: 'Product SKU*'),
                                  ),
                                ),
                                const SizedBox(width: 15),
                                Expanded(
                                  child: TextFormField(
                                    controller: productNameCtrl,
                                    cursorHeight: 20,
                                    decoration: inpDecor()
                                        .copyWith(labelText: 'Product Name*'),
                                  ),
                                ),
                                const SizedBox(width: 15),
                                Expanded(
                                  flex: 2,
                                  child: TextFormField(
                                    controller: productDescCtrl,
                                    cursorHeight: 20,
                                    decoration: inpDecor().copyWith(
                                        labelText: 'Product Description*'),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 20),
                            Row(
                              children: [
                                Expanded(
                                  child: Container(
                                    clipBehavior: Clip.antiAlias,
                                    height: 48,
                                    padding: const EdgeInsets.only(left: 3),
                                    decoration: BoxDecoration(
                                        border: Border.all(
                                            color: Colors.grey.shade400),
                                        borderRadius: BorderRadius.circular(7),
                                        color: Colors.transparent),
                                    child: Row(
                                      children: [
                                        Checkbox(
                                          side: const BorderSide(
                                              color: Colors.grey),
                                          checkColor: Colors.white,
                                          activeColor: themeColor,
                                          hoverColor: Colors.transparent,
                                          overlayColor:
                                              const WidgetStatePropertyAll(
                                                  Colors.transparent),
                                          value: topSelling,
                                          onChanged: (value) async {
                                            topSelling = value!;
                                            setState(() {});
                                          },
                                        ),
                                        const SizedBox(width: 5),
                                        const Text(
                                          "Top Selling",
                                          style: TextStyle(color: Colors.black),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 15),
                                Expanded(
                                  child: Container(
                                    clipBehavior: Clip.antiAlias,
                                    height: 48,
                                    padding: const EdgeInsets.only(left: 3),
                                    decoration: BoxDecoration(
                                        border: Border.all(
                                            color: Colors.grey.shade400),
                                        borderRadius: BorderRadius.circular(7),
                                        color: Colors.transparent),
                                    child: Row(
                                      children: [
                                        Checkbox(
                                          side: const BorderSide(
                                              color: Colors.grey),
                                          checkColor: Colors.white,
                                          activeColor: themeColor,
                                          hoverColor: Colors.transparent,
                                          overlayColor:
                                              const WidgetStatePropertyAll(
                                                  Colors.transparent),
                                          value: productshow,
                                          onChanged: (value) async {
                                            productshow = value!;
                                            setState(() {});
                                          },
                                        ),
                                        const SizedBox(width: 5),
                                        const Text(
                                          "Available",
                                          style: TextStyle(color: Colors.black),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 15),
                                Expanded(
                                  child: DropdownButtonHideUnderline(
                                      child:
                                          DropdownButtonFormField<SubCategory>(
                                    decoration: inpDecor()
                                        .copyWith(labelText: 'Sub-category'),
                                    hint: const Text("Select sub-category"),
                                    value: selDefaultSubCategory,
                                    items: [
                                      ...List.generate(
                                        vendorSubCategories.length,
                                        (index) {
                                          return DropdownMenuItem<SubCategory>(
                                              value: vendorSubCategories[index],
                                              child: Text(
                                                  capilatlizeFirstLetter(
                                                      vendorSubCategories[index]
                                                          .name)));
                                        },
                                      )
                                    ],
                                    onChanged: (value) {
                                      selDefaultSubCategory = value;
                                      setState(() {});
                                    },
                                  )),
                                ),
                              ],
                            ),
                            const SizedBox(height: 20),
                            Row(
                              children: [
                                Expanded(
                                  child: MultiSelectDropDown<SubCategory>(
                                    hint: 'Extra Sub-category',
                                    inputDecoration: BoxDecoration(
                                      color: Colors.transparent,
                                      border: Border.all(
                                          color: Colors.grey.shade400),
                                      borderRadius: BorderRadius.circular(7),
                                    ),
                                    selectedOptions: selSubCats
                                        .map((e) =>
                                            ValueItem(label: e.name, value: e))
                                        .toList(),
                                    borderRadius: 6,
                                    searchEnabled: true,
                                    onOptionSelected: (options) {
                                      selSubCats.clear();
                                      for (var element in options) {
                                        selSubCats.addIf(element.value != null,
                                            element.value!);
                                      }
                                    },
                                    options: List.generate(
                                      extraSubCatsList.length,
                                      (index) {
                                        return ValueItem(
                                          label: extraSubCatsList[index].name,
                                          value: extraSubCatsList[index],
                                        );
                                      },
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 15),
                                Expanded(
                                  child: MultiSelectDropDown<String>(
                                    hint: 'Detail Type',
                                    inputDecoration: BoxDecoration(
                                      color: Colors.transparent,
                                      border: Border.all(
                                          color: Colors.grey.shade400),
                                      borderRadius: BorderRadius.circular(7),
                                    ),
                                    selectedOptions: selDetailTypes
                                        .map((e) =>
                                            ValueItem(label: e, value: e))
                                        .toList(),
                                    borderRadius: 6,
                                    searchEnabled: true,
                                    onOptionSelected: (options) {
                                      selDetailTypes.clear();
                                      for (var element in options) {
                                        selDetailTypes.addIf(
                                            element.value != null,
                                            element.value!);
                                      }
                                      updateGrid();
                                      gridKey = UniqueKey();
                                      setState(() {});
                                    },
                                    options: List.generate(
                                      subCatDetailTypes.length,
                                      (index) {
                                        return ValueItem(
                                          label: subCatDetailTypes[index]
                                              ['detailType'],
                                          value: subCatDetailTypes[index]
                                              ['detailType'],
                                        );
                                      },
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                        const SizedBox(height: 40),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const FormHeaderTile(
                              title: 'Tags',
                              button: false,
                            ),
                            const SizedBox(height: 25),
                            TextFormField(
                              focusNode: myFocusNode,
                              controller: tagCtrl,
                              onFieldSubmitted: (value) async {
                                if (value.trim().isEmpty) {
                                  return;
                                }
                                tags.add(value.toLowerCase().trim());
                                tagCtrl.clear();
                                myFocusNode.requestFocus();
                                setState(() {});
                              },
                              decoration: inpDecor().copyWith(
                                  labelText: 'Enter tag name*',
                                  hintText: ' Hit enter once written'),
                            ),
                            const SizedBox(height: 20),
                            Wrap(
                              spacing: 10,
                              runSpacing: 10,
                              alignment: WrapAlignment.start,
                              runAlignment: WrapAlignment.start,
                              children: [
                                ...List.generate(tags.length, (index) {
                                  return Chip(
                                    label: Text(
                                        capilatlizeFirstLetter(tags[index])),
                                    onDeleted: () async {
                                      tags.removeAt(index);
                                      setState(() {});
                                    },
                                  );
                                })
                              ],
                            ),
                          ],
                        ),
                        const SizedBox(height: 50),
                        Column(
                          children: [
                            FormHeaderTile(
                              title: 'Variants',
                              button: true,
                              buttonName: 'Add',
                              icon: CupertinoIcons.add,
                              onPressed: () async {
                                if (selDetailTypes.isEmpty) {
                                  showErrorAppSnackBar(
                                      context, 'Detail type not selected');
                                }
                                if (selDetailTypes.isNotEmpty) {
                                  variantsInputList.add(
                                    NewVariantModel(
                                      aqpiId: '',
                                      originalPrice: 0,
                                      sellingPrice: 0,
                                      lowerName: '',
                                      docId: null,
                                      mainCatId:
                                          selDefaultSubCategory?.offeringId ??
                                              '',
                                      subCatId:
                                          selDefaultSubCategory?.docId ?? "",
                                      productId: widget.productId ?? "",
                                      id: getRandomId(8),
                                      newImages: [],
                                      images: [],
                                      description: '',
                                      defaultt: false,
                                      show: true,
                                      fixedprice: 0,
                                      priceType: '',
                                      priceRange: [],
                                      detailTypes: {},
                                    ),
                                  );
                                }
                                radioSelectedVariant =
                                    (variantsInputList.isNotEmpty &&
                                            variantsInputList.length == 1)
                                        ? variantsInputList[0]
                                        : radioSelectedVariant;
                                updateGrid();
                                gridKey = UniqueKey();
                                setState(() {});
                                await Future.delayed(
                                    const Duration(milliseconds: 200));
                                scrCtrl.animateTo(
                                    scrCtrl.position.maxScrollExtent,
                                    duration: Durations.medium1,
                                    curve: Curves.linear);
                              },
                            ),
                            const SizedBox(height: 25),
                            _productVariantsDetails(),
                          ],
                        ),
                        const SizedBox(height: 20),
                      ],
                    )
                  ],
                ),
              )
        : const Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Center(child: CircularProgressIndicator()),
            ],
          );
  }

  Future<dynamic> generateVariantDialog(
      BuildContext context,
      Map<String, String> currentMap,
      List<Map<String, dynamic>> existingCombinations) {
    return showDialog(
      context: context,
      builder: (context) {
        bool combinationGenerated = false;
        bool onSubmitLoad = false;
        List<Map<String, String>> generatedCombination = [];
        List<Map<String, String>> selectedCombination = [];
        return Dialog(
          backgroundColor: Colors.white,
          surfaceTintColor: Colors.white,
          child: StatefulBuilder(
            builder: (context, setState2) {
              return Container(
                constraints: BoxConstraints(
                  maxHeight: 600,
                  maxWidth: 1200,
                ),
                child: SingleChildScrollView(
                  padding: EdgeInsets.symmetric(vertical: 20, horizontal: 25),
                  child: Column(
                    // spacing: 13,
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Generate Combination',
                        style: GoogleFonts.zcoolXiaoWei(
                          color: themeColor,
                          fontSize: 23,
                        ),
                      ),
                      SizedBox(height: 13),
                      ...selDetailTypes.map(
                        (e) => Padding(
                          padding: const EdgeInsets.only(top: 8.0),
                          child: Row(
                            children: [
                              Expanded(
                                child: TextFormField(
                                  onChanged: (value) {
                                    currentMap[e] = value;
                                  },
                                  decoration: inpDecor().copyWith(
                                      labelText:
                                          '${capilatlizeFirstLetter(e)}'),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      if (!onSubmitLoad) ...[
                        SizedBox(height: 12),
                        ElevatedButton(
                          onPressed: () {
                            if (currentMap.values
                                .every((value) => value.trim().isEmpty)) {
                              showErrorAppSnackBar(
                                  context, 'All fields are empty');
                              return;
                            }
                            final res = getNewVariantCombinations(
                                existingCombinations: existingCombinations,
                                userInput: currentMap);

                            generatedCombination.clear();
                            selectedCombination.clear();
                            generatedCombination.addAll(res);
                            selectedCombination.addAll(res);
                            combinationGenerated = true;
                            setState2(() {});
                          },
                          child: Text("Create"),
                        ),
                      ],
                      SizedBox(height: 15),
                      if (combinationGenerated) ...[
                        Container(
                          decoration: BoxDecoration(
                              color: dashboardSelectedColor,
                              border: Border.all(color: dividerColor),
                              borderRadius: const BorderRadius.only(
                                  topLeft: Radius.circular(8),
                                  topRight: Radius.circular(8))
                              // color: const Color.fromARGB(255, 228, 228, 228),
                              // color: themeColor,
                              // color: const Color.fromARGB(255, 177, 139, 86),
                              // color: tableHeaderColor,
                              // borderRadius: BorderRadius.circular(4),
                              ),
                          child: Row(
                            children: [
                              Opacity(
                                opacity: 0,
                                child: IgnorePointer(
                                  ignoring: true,
                                  child: Checkbox(
                                    value: true,
                                    onChanged: (value) {},
                                  ),
                                ),
                              ),
                              SizedBox(width: 10),
                              ...selDetailTypes.map((e) => Expanded(
                                  child: TableHeaderText(
                                      headerName: capilatlizeFirstLetter(e))))
                            ],
                          ),
                        ),
                        generatedCombination.isEmpty
                            ? Container(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 15),
                                decoration: const BoxDecoration(
                                    border: Border(
                                        bottom: BorderSide(color: dividerColor),
                                        left: BorderSide(color: dividerColor),
                                        right: BorderSide(color: dividerColor)),
                                    borderRadius: BorderRadius.only(
                                        bottomLeft: Radius.circular(8),
                                        bottomRight: Radius.circular(8))),
                                child: Center(
                                    child: Text(
                                  'No Data Available',
                                  style: TextStyle(
                                      color: Color(0xff737373),
                                      fontSize: 14.5,
                                      fontWeight: FontWeight.w500),
                                )),
                              )
                            : Column(
                                children: [
                                  ...List.generate(
                                    generatedCombination.length,
                                    (index) {
                                      final isSelected =
                                          selectedCombination.any((map) =>
                                              map.length ==
                                                  generatedCombination[index]
                                                      .length &&
                                              map.keys.every((key) =>
                                                  map[key] ==
                                                  generatedCombination[index]
                                                      [key]));
                                      return Container(
                                        decoration: BoxDecoration(
                                            border: const Border(
                                                bottom: BorderSide(
                                                    color: dividerColor),
                                                left: BorderSide(
                                                    color: dividerColor),
                                                right: BorderSide(
                                                    color: dividerColor)),
                                            borderRadius: index ==
                                                    (generatedCombination
                                                            .length -
                                                        1)
                                                ? const BorderRadius.only(
                                                    bottomLeft:
                                                        Radius.circular(8),
                                                    bottomRight:
                                                        Radius.circular(8))
                                                : null),
                                        child: Row(
                                          children: [
                                            Checkbox(
                                              value: isSelected,
                                              onChanged: (value) {
                                                if (value == true) {
                                                  selectedCombination.add(
                                                      generatedCombination[
                                                          index]);
                                                } else if (value == false) {
                                                  selectedCombination.remove(
                                                      generatedCombination[
                                                          index]);
                                                }
                                                setState2(() {});
                                              },
                                            ),
                                            SizedBox(width: 10),
                                            ...selDetailTypes.map((e) =>
                                                Expanded(
                                                    child: Text(
                                                        capilatlizeFirstLetter(
                                                            generatedCombination[
                                                                index][e]!))))
                                          ],
                                        ),
                                      );
                                    },
                                  ),
                                  SizedBox(height: 15),
                                  onSubmitLoad
                                      ? Center(
                                          child: SizedBox(
                                            height: 28,
                                            width: 28,
                                            child: CircularProgressIndicator(
                                                strokeWidth: 3),
                                          ),
                                        )
                                      : Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            ElevatedButton(
                                                onPressed: () {
                                                  Navigator.of(context).pop();
                                                },
                                                child: Text('Cancel')),
                                            SizedBox(width: 12),
                                            ElevatedButton.icon(
                                              style: ElevatedButton.styleFrom(
                                                backgroundColor: themeColor,
                                                elevation: 0,
                                                foregroundColor: Colors.white,
                                                shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            4)),
                                              ),
                                              onPressed: () async {
                                                if (selectedCombination
                                                    .isEmpty) {
                                                  return;
                                                }
                                                onSubmitLoad = true;
                                                setState2(() {});
                                                variantsInputList.addAll(
                                                    selectedCombination.map(
                                                  (e) => NewVariantModel(
                                                      originalPrice: 0,
                                                      sellingPrice: 0,
                                                      aqpiId: '',
                                                      id: getRandomId(8),
                                                      newImages: [],
                                                      images: [],
                                                      description: '',
                                                      defaultt: false,
                                                      show: true,
                                                      lowerName: '',
                                                      fixedprice: 0,
                                                      priceType: PriceTypeModel
                                                          .fixedPrice,
                                                      priceRange: [],
                                                      detailTypes: e,
                                                      productId:
                                                          widget.productId ??
                                                              '',
                                                      subCatId:
                                                          selDefaultSubCategory
                                                                  ?.docId ??
                                                              '',
                                                      mainCatId:
                                                          selDefaultSubCategory
                                                                  ?.offeringId ??
                                                              ''),
                                                ));
                                                await Future.delayed(
                                                    Duration(seconds: 2));
                                                onSubmitLoad = false;
                                                setState2(() {});
                                                Navigator.of(context).pop();
                                                gridKey = UniqueKey();
                                                updateGrid();
                                                setState(() {});
                                              },
                                              icon: const Icon(
                                                CupertinoIcons.checkmark_alt,
                                                size: 20,
                                              ),
                                              label: Padding(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        vertical: 8.0),
                                                child: Text("Add",
                                                    style: GoogleFonts.livvic(
                                                        letterSpacing: 1.3,
                                                        fontSize: 14,
                                                        fontWeight:
                                                            FontWeight.w500)),
                                              ),
                                            ),
                                          ],
                                        )
                                ],
                              )
                      ],
                    ],
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  void onSubmit() async {
    if (onSubmitLoad) return;

    if (productSkuCtrl.text.trim().isEmpty) {
      showErrorAppSnackBar(context, "Product sku cannot be empty");
      return;
    }
    if (productNameCtrl.text.trim().isEmpty) {
      showErrorAppSnackBar(context, "Product name cannot be empty");
      return;
    }
    if (productDescCtrl.text.trim().isEmpty) {
      showErrorAppSnackBar(context, "Product description cannot be empty");
      return;
    }
    if (selDefaultSubCategory == null) {
      showErrorAppSnackBar(context, "Product sub-category not selected");
      return;
    }
    if (selDetailTypes.isEmpty) {
      showErrorAppSnackBar(context, "Select detail type");
      return;
    }
    if (tags.isEmpty) {
      showErrorAppSnackBar(context, "Tags cannot be empty");
      return;
    }
    if (variantsInputList.isEmpty) {
      showErrorAppSnackBar(context, "Product variant empty");
      return;
    }
    if (variantsInputList.isNotEmpty && radioSelectedVariant == null) {
      showErrorAppSnackBar(context, "Product default variant not selected");
      return;
    }
    final variantsImagesBool = variantsInputList
        .map((e) => e.newImages.isEmpty && e.images.isEmpty)
        .toList();
    if (variantsImagesBool.contains(true)) {
      showErrorAppSnackBar(context, "Variant image cannot be empty");
      return;
    }

    try {
      onSubmitLoad = true;
      setState(() {});

      final productDocRef = widget.productId != null
          ? FBFireStore.products.doc(widget.productId)
          : FBFireStore.products.doc();

      final subCatRef =
          FBFireStore.subCategories.doc(selDefaultSubCategory!.docId);

      final combinationNames =
          generateCombinations(productNameCtrl.text.toLowerCase().trim());
      num subCatminprice = 0;
      num subCatmaxprice = 0;

      num productminprice = 0;
      num productmaxprice = 0;
      List<String> subCatDetailType =
          subCatDetailTypes.map((e) => e['detailType'] as String).toList();

      subCatDetailType.sort((a, b) => a.compareTo(b));
      selDetailTypes.sort((a, b) => a.compareTo(b));

      await FBFireStore.fb.runTransaction(
        (transaction) async {
          try {
            print("object=============1");
            final subcatData = await transaction.get(subCatRef);
            Map<String, List<String>> subCatDataList =
                subcatData.data()?.containsKey('allData') ?? false
                    ? (subcatData['allData'] as Map<String, dynamic>).map(
                        (key, value) =>
                            MapEntry(key, List<String>.from(value ?? [])),
                      )
                    : {};
            print("object=============2");

            for (var variant in variantsInputList) {
              if ((variant.priceType == PriceTypeModel.fixedPrice) &&
                  variant.fixedprice <= 0) {
                showErrorAppSnackBar(context, "Variant Price cannot be empty");
                onSubmitLoad = false;
                setState(() {});
                return;
              }
              print("object=============3");

              final variantDocRef = variant.docId != null
                  ? FBFireStore.variants.doc(variant.docId)
                  : FBFireStore.variants.doc();
              NewVariantModel? oldVariant;
              Map<String, String> toChangeList = {};
              Map<String, String> finalToChangeListinSubCat = {};
              print("object=============4");
              if (variant.docId != null) {
                final currentVariantSnap =
                    await FBFireStore.variants.doc(variant.docId).get();
                print("object=============4.1");
                oldVariant = NewVariantModel.fromDocSnap(currentVariantSnap);
                print("object=============4.2");
                for (var key in oldVariant.detailTypes.keys) {
                  toChangeList[key] = "";
                }

                print("object=============4.3 ${variant.docId}");
                for (var key in variant.detailTypes.keys) {
                  print("New Key -------------$key");
                  tags.addIf(!tags.contains(variant.detailTypes[key]),
                      variant.detailTypes[key]);
                  print("object=============4.4");
                  if (!toChangeList.containsKey(key)) {
                    toChangeList[key] = "";
                  }
                }
                print("object=============5");
                for (var element in toChangeList.keys) {
                  if (oldVariant.detailTypes.containsKey(element) &&
                      variant.detailTypes.containsKey(element)) {
                    if ((variant.detailTypes[element] !=
                            oldVariant.detailTypes[element]) &&
                        (variantsInputList
                            .where((e) =>
                                (e.detailTypes[element] as String)
                                    .toLowerCase() ==
                                (oldVariant?.detailTypes[element] as String)
                                    .toLowerCase())
                            .toList()
                            .isEmpty)) {
                      tags.removeWhere((tag) =>
                          tag.toLowerCase() ==
                          oldVariant?.detailTypes[element].toLowerCase());
                      finalToChangeListinSubCat[element] =
                          oldVariant.detailTypes[element];
                    }
                  } else if (oldVariant.detailTypes.containsKey(element) &&
                      !(variant.detailTypes.containsKey(element))) {
                    // finalToChangeList
                    tags.removeWhere((tag) =>
                        tag.toLowerCase() ==
                        oldVariant?.detailTypes[element].toLowerCase());
                    finalToChangeListinSubCat[element] =
                        oldVariant.detailTypes[element];
                  }
                }
                print("object=============6");
                for (var detailKey in finalToChangeListinSubCat.keys) {
                  final allVarinatCount = await FBFireStore.variants
                      .where(FieldPath.documentId, isNotEqualTo: variant.docId)
                      .where('subCatId',
                          isEqualTo: selDefaultSubCategory?.docId)
                      .where('detailTypes.$detailKey',
                          isEqualTo: finalToChangeListinSubCat[detailKey])
                      .count()
                      .get();
                  if (allVarinatCount.count == 0) {
                    subCatDataList[detailKey]
                        ?.remove(finalToChangeListinSubCat[detailKey]);
                  }
                }
              }
              print("object=============7");
              final variantDetailType = variant.detailTypes;

              List<String> toUploadImages = variant.images;
              for (var image in variant.newImages) {
                final imgUrl = await uploadProductFile(image, productDocRef.id);
                toUploadImages.addIf(imgUrl != null, imgUrl!);
              }
              print("object=============8");
              // FOR FIXED PRICE

              if (variant.priceType == PriceTypeModel.fixedPrice) {
                final variantfixedPrice = variant.fixedprice;
                productminprice =
                    productminprice == 0 ? variantfixedPrice : productminprice;
                if (variantfixedPrice < productminprice) {
                  productminprice = variantfixedPrice;
                } else if (variantfixedPrice > productmaxprice) {
                  productmaxprice = variantfixedPrice;
                }
              }
              print("object=============9");
              for (var element in subCatDetailType) {
                final res = (variantDetailType[element] as String?)
                    ?.toLowerCase()
                    .trim();
                if (res != null &&
                    subCatDataList.containsKey(element) &&
                    !(subCatDataList[element]!.contains(res))) {
                  subCatDataList[element]!.add(res);
                }
              }
              print("object=============10");
              Map<String, dynamic> variantData = {
                'id': variant.id,
                'images': toUploadImages,
                'description': variant.description,
                'show': variant.show,
                'fixedprice': variant.fixedprice,
                'originalprice': variant.originalPrice,
                'sellingprice': variant.sellingPrice,
                'priceType': variant.priceType,
                'lowerName': productNameCtrl.text.trim().toLowerCase(),
                'defaultt': radioSelectedVariant?.id == variant.id,
                'priceRange':
                    variant.priceRange.map((range) => range.toJson()).toList(),
                'detailTypes': variant.detailTypes,
                'productId': productDocRef.id,
                'mainCatId': selDefaultSubCategory?.offeringId,
                'subCatId': selDefaultSubCategory?.docId,
              };

              if (radioSelectedVariant?.id == variant.id) {
                defaultProductImage =
                    (variantData['images'] as List<String>).first;
              }
              transaction.set(variantDocRef, variantData);
            }
            print("object=============11");

            subCatminprice = subcatData['minPrice'] == 0
                ? productminprice
                : subcatData['minPrice'];

            subCatmaxprice = subcatData['maxPrice'];

            if (productminprice < subCatminprice) {
              subCatminprice = productminprice;
            }

            if (productmaxprice > subCatmaxprice) {
              subCatmaxprice = productmaxprice;
            }
            print("object=============12");

            final data = {
              'name': productNameCtrl.text.trim(),
              'lowerName': productNameCtrl.text.toLowerCase().trim(),
              'combinationNames': combinationNames,
              'mainCatDocId': selDefaultSubCategory?.offeringId,
              'subCatDocId': selDefaultSubCategory?.docId,
              'subcatIds': selSubCats.map((e) => e.docId).toList(),
              'vendorDocId': widget.extraIds[0]!,
              'show': product != null ? productshow : true,
              'description': productDescCtrl.text,
              'userType': UserTypes.admin,
              'sku': productSkuCtrl.text.trim(),
              'createdAt': product != null
                  ? product?.createdAt
                  : FieldValue.serverTimestamp(),
              'topSelling': topSelling,
              'minPrice': productminprice,
              'defaultImage': defaultProductImage,
              'maxPrice': productmaxprice,
              'quantitySold': product != null ? product?.quantitySold : 0,
              'totalSalesAmount':
                  product != null ? product?.totalSalesAmount : 0,
              'tags': tags,
              'detailsList': selDetailTypes,
              'priorityNo': 0,
              'rating': 4.5,
            };
            print("object=============13");

            transaction.update(subCatRef, {
              'allData': subCatDataList,
              'maxPrice': subCatmaxprice,
              'minPrice': subCatminprice,
            });
            print("object=============14");

            transaction.set(productDocRef, data);
            print("object=============14.5");

            for (var element in variantsInputList) {
              element.newImages.clear();
            }
            print("object=============15");
            onSubmitLoad = false;
            print("object=============16");
            setState(() {});
            showAppSnackBar(
                context,
                widget.productId != null
                    ? "Product Updated Successfully"
                    : "Product Added Successfully");
          } on Exception catch (e) {
            debugPrint(e.toString());
            showAppSnackBar(context, "Something went wrong");
          }
        },
      );
      if (mounted) {
        context.pop(ProductModel(
          docId: productDocRef.id,
          defaultImage: defaultProductImage!,
          name: productNameCtrl.text.trim(),
          sku: productSkuCtrl.text.trim(),
          lowerName: productNameCtrl.text.toLowerCase().trim(),
          combinationNames: combinationNames,
          mainCatDocId: selDefaultSubCategory!.offeringId,
          defaultSubCatDocId: selDefaultSubCategory!.docId,
          vendorDocId: widget.extraIds[0]!,
          show: productshow,
          userType: UserTypes.admin,
          createdAt: product?.createdAt ?? Timestamp.now(),
          description: productDescCtrl.text,
          topSelling: topSelling,
          subcatIds: selSubCats.map((e) => e.docId).toList(),
          detailsList: selDetailTypes,
          maxPrice: productmaxprice,
          minPrice: productminprice,
          quantitySold: product?.quantitySold ?? 0,
          tags: tags,
          totalSalesAmount: product?.totalSalesAmount ?? 0,
          priorityNo: product?.priorityNo ?? 0,
          rating: product?.rating ?? 0,
        ));
      }
    } catch (e) {
      onSubmitLoad = false;
      setState(() {});
      showAppSnackBar(context, "Something went wrong");
      debugPrint(e.toString());
    }
  }

  Widget _productVariantsDetails() {
    return true
        ? Container(
            constraints: const BoxConstraints(maxHeight: 720),
            child: PlutoGrid(
              key: gridKey,
              mode: PlutoGridMode.normal,
              onChanged: (event) {
                if (event.column.field != 'images') {
                  event.oldValue;
                  final keyChanged = event.column.field;
                  variantsInputList[event.rowIdx]
                      .updateValue(keyChanged, event.value);
                }
              },
              columns: columns,
              rows: rows,
            ),
          )
        : ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            separatorBuilder: (context, index) {
              return const SizedBox(height: 30);
            },
            itemCount: variantsInputList.length,
            itemBuilder: (context, index) {
              final variant = variantsInputList[index];

              return ExpansionTile(
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(5),
                    side: const BorderSide(color: themeColor)),
                collapsedShape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(5),
                    side: BorderSide(color: Colors.grey.shade300)),
                childrenPadding: const EdgeInsets.all(15),
                leading: Radio<NewVariantModel>(
                  value: variant,
                  groupValue: radioSelectedVariant,
                  onChanged: (value) {
                    radioSelectedVariant = value;
                    variant.defaultt = true;
                    setState(() {});
                  },
                ),
                title: Text.rich(TextSpan(children: [
                  ...List.generate(
                    selDetailTypes.length,
                    (index) {
                      return TextSpan(
                          text:
                              '${capilatlizeFirstLetter(variant.detailTypes[selDetailTypes[index]] ?? "-")} ');
                    },
                  )
                ])),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                        onPressed: () {
                          variantsInputList.removeAt(index);
                          setState(() {});
                        },
                        icon: const Icon(CupertinoIcons.delete)),
                    const SizedBox(width: 10),
                    Tooltip(
                      message: 'Duplicate',
                      child: IconButton(
                          onPressed: () async {
                            if (variantsInputList.isEmpty) return;
                            final toDuplicateVariant = variant;
                            variantsInputList.insert(
                                index + 1,
                                NewVariantModel(
                                  aqpiId: '',
                                  docId: null,
                                  originalPrice: 0,
                                  sellingPrice: 0,
                                  id: getRandomId(8),
                                  lowerName: toDuplicateVariant.lowerName,
                                  mainCatId: toDuplicateVariant.mainCatId,
                                  subCatId: toDuplicateVariant.subCatId,
                                  productId: toDuplicateVariant.productId,
                                  newImages: toDuplicateVariant.newImages,
                                  images: toDuplicateVariant.images,
                                  description: toDuplicateVariant.description,
                                  defaultt: toDuplicateVariant.defaultt,
                                  show: toDuplicateVariant.show,
                                  fixedprice: toDuplicateVariant.fixedprice,
                                  priceType: toDuplicateVariant.priceType,
                                  priceRange: toDuplicateVariant.priceRange,
                                  detailTypes: toDuplicateVariant.detailTypes,
                                ));

                            setState(() {});
                            await Future.delayed(
                                const Duration(milliseconds: 200));
                            scrCtrl.animateTo(scrCtrl.initialScrollOffset + 100,
                                duration: Durations.medium1,
                                curve: Curves.linear);
                          },
                          icon: const Icon(
                              CupertinoIcons.plus_rectangle_on_rectangle)),
                    ),
                    const SizedBox(width: 10),
                    const Icon(CupertinoIcons.chevron_down)
                  ],
                ),
                children: [
                  const Divider(color: themeColor, thickness: 1),
                  const SizedBox(height: 7),
                  Row(
                    children: [
                      const SizedBox(height: 20),
                      variantDetails(variant),
                      const SizedBox(height: 20),
                    ],
                  ),
                ],
              );
            },
          );
  }

  Expanded variantDetails(NewVariantModel variant) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 5.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            StaggeredGrid.extent(
              maxCrossAxisExtent: 200,
              mainAxisSpacing: 15,
              crossAxisSpacing: 15,
              children: [
                ...List.generate(
                  selDetailTypes.length,
                  (index) {
                    return TextFormField(
                      onChanged: (value) {
                        variant.detailTypes[selDetailTypes[index]] =
                            value.toLowerCase();
                      },
                      controller: TextEditingController(
                          text: variant.detailTypes[selDetailTypes[index]]),
                      decoration: inpDecor().copyWith(
                          labelText:
                              capilatlizeFirstLetter(selDetailTypes[index])),
                    );
                  },
                ),
                Container(
                  clipBehavior: Clip.antiAlias,
                  height: 48,
                  padding: const EdgeInsets.only(left: 3),
                  decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade400),
                      borderRadius: BorderRadius.circular(7),
                      color: Colors.transparent),
                  child: Row(
                    children: [
                      Checkbox(
                        side: const BorderSide(color: Colors.grey),
                        checkColor: Colors.white,
                        activeColor: themeColor,
                        hoverColor: Colors.transparent,
                        overlayColor:
                            const WidgetStatePropertyAll(Colors.transparent),
                        value: variant.show,
                        onChanged: (value) async {
                          variant.show = value!;
                          setState(() {});
                        },
                      ),
                      const SizedBox(width: 5),
                      const Text(
                        "Available",
                        style: TextStyle(color: Colors.black),
                      ),
                    ],
                  ),
                )
              ],
            ),
            const SizedBox(height: 15),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller:
                        TextEditingController(text: variant.description),
                    onChanged: (value) {
                      variant.description = value;
                    },
                    decoration: inpDecor().copyWith(labelText: ' Description'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                const Text(
                  "Images*",
                  style: TextStyle(
                    color: Color(0xff4F4F4F),
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(width: 5),
                IconButton(
                    style: IconButton.styleFrom(
                        padding: EdgeInsets.zero,
                        hoverColor: Colors.transparent,
                        highlightColor: Colors.transparent),
                    onPressed: () async {
                      final result = await ImagePickerService()
                          .pickImageAndCrop(context, useCompressor: true);
                      variant.newImages.addAll(result);
                      setState(() {});
                    },
                    icon: const Icon(
                      CupertinoIcons.photo_on_rectangle,
                      size: 20,
                    ))
              ],
            ),
            const SizedBox(height: 5),
            Wrap(
              spacing: 10,
              runSpacing: 10,
              alignment: WrapAlignment.start,
              runAlignment: WrapAlignment.start,
              children: [
                ...List.generate(
                  variant.images.length,
                  (uImageindex) {
                    return InkWell(
                      onTap: () async {
                        await showDialog(
                          context: context,
                          builder: (context) {
                            return AlertDialog(
                                backgroundColor: Colors.white,
                                surfaceTintColor: Colors.white,
                                content: Container(
                                  height: 500,
                                  width: 500,
                                  clipBehavior: Clip.antiAlias,
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(10)),
                                  child: Stack(
                                    fit: StackFit.expand,
                                    children: [
                                      InteractiveViewer(
                                        maxScale: 1.5,
                                        minScale: .9,
                                        child: CachedNetworkImage(
                                          imageUrl: variant.images[uImageindex],
                                          width: double.maxFinite,
                                          placeholder: (context, url) {
                                            return const Center(
                                                child: Icon(
                                                    CupertinoIcons.camera));
                                          },
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                      Align(
                                        alignment: Alignment.topRight,
                                        child: Tooltip(
                                          message: 'Close',
                                          child: Padding(
                                            padding: const EdgeInsets.only(
                                                right: 8.0, top: 8),
                                            child: InkWell(
                                              onTap: () {
                                                Navigator.of(context).pop();
                                              },
                                              child: Container(
                                                padding:
                                                    const EdgeInsets.all(3),
                                                decoration: const BoxDecoration(
                                                    color: Colors.white,
                                                    shape: BoxShape.circle,
                                                    boxShadow: [
                                                      BoxShadow(
                                                          color: Colors.black26,
                                                          blurRadius: 2,
                                                          spreadRadius: 1,
                                                          offset: Offset(0, 2)),
                                                    ]),
                                                child: const Icon(
                                                  CupertinoIcons.xmark,
                                                  size: 18,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                actions: [
                                  TextButton(
                                      onPressed: () {
                                        variant.images.removeAt(uImageindex);
                                        Navigator.of(context).pop();
                                      },
                                      child: const Text("Delete"))
                                ]);
                          },
                        );
                        setState(() {});
                      },
                      child: Container(
                        clipBehavior: Clip.antiAlias,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(3)),
                        child: Image.network(
                          variant.images[uImageindex],
                          height: 80,
                          width: 80,
                          fit: BoxFit.cover,
                        ),
                      ),
                    );
                  },
                ),
                ...List.generate(
                  variant.newImages.length,
                  (nimageindex) {
                    return InkWell(
                      onTap: () async {
                        await showDialog(
                          context: context,
                          builder: (context) {
                            return AlertDialog(
                              backgroundColor: Colors.white,
                              surfaceTintColor: Colors.white,
                              content: Container(
                                height: 500,
                                width: 500,
                                clipBehavior: Clip.antiAlias,
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(10)),
                                child: Stack(
                                  fit: StackFit.expand,
                                  children: [
                                    InteractiveViewer(
                                      maxScale: 1.5,
                                      minScale: .9,
                                      child: Image.memory(
                                        variant
                                            .newImages[nimageindex].uInt8List,
                                        width: double.maxFinite,
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                    Align(
                                      alignment: Alignment.topRight,
                                      child: Tooltip(
                                        message: 'Close',
                                        child: Padding(
                                          padding: const EdgeInsets.only(
                                              right: 8.0, top: 8),
                                          child: InkWell(
                                            onTap: () {
                                              Navigator.of(context).pop();
                                            },
                                            child: Container(
                                              padding: const EdgeInsets.all(3),
                                              decoration: const BoxDecoration(
                                                  color: Colors.white,
                                                  shape: BoxShape.circle,
                                                  boxShadow: [
                                                    BoxShadow(
                                                        color: Colors.black26,
                                                        blurRadius: 2,
                                                        spreadRadius: 1,
                                                        offset: Offset(0, 2)),
                                                  ]),
                                              child: const Icon(
                                                CupertinoIcons.xmark,
                                                size: 18,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              actions: [
                                TextButton(
                                    onPressed: () {
                                      variant.newImages.removeAt(nimageindex);
                                      Navigator.of(context).pop();
                                    },
                                    child: const Text("Delete"))
                              ],
                            );
                          },
                        );
                        setState(() {});
                      },
                      child: Container(
                        clipBehavior: Clip.antiAlias,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(3)),
                        child: Image.memory(
                          variant.newImages[nimageindex].uInt8List,
                          height: 80,
                          width: 80,
                          fit: BoxFit.cover,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
            const SizedBox(height: 15),
            Row(
              children: [
                const Text(
                  "Price Type :",
                  style: TextStyle(
                    color: Color(0xff4F4F4F),
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                ...List.generate(
                  priceTypes.length,
                  (index) {
                    return Padding(
                      padding: const EdgeInsets.only(left: 8.0),
                      child: Row(
                        children: [
                          Radio<String>(
                            value: priceTypes[index],
                            groupValue: variant.priceType,
                            onChanged: (value) {
                              variant.fixedprice = 0;
                              variant.priceRange = [];
                              variant.priceType = value!;
                              setState(() {});
                            },
                          ),
                          const SizedBox(width: 8),
                          Text(priceTypes[index])
                        ],
                      ),
                    );
                  },
                ),
                const SizedBox(width: 15),
                if (variant.priceType == PriceTypeModel.fixedPrice)
                  Expanded(
                      child: TextFormField(
                    controller: TextEditingController(
                        text: variant.fixedprice.toString()),
                    onChanged: (value) {
                      variant.fixedprice = num.tryParse(value) ?? 0;
                    },
                    decoration:
                        inpDecor().copyWith(labelText: ' Enter Fixed Price'),
                  )),
                if (variant.priceType == PriceTypeModel.priceRange)
                  ElevatedButton.icon(
                      onPressed: () {
                        variant.priceRange.add(PriceRangeModel(
                            startQty: '', endQty: '', price: 0));
                        setState(() {});
                      },
                      icon: const Icon(CupertinoIcons.add, size: 20),
                      label: const Text("Range")),
              ],
            ),
            ...[
              const SizedBox(height: 25),
              StaggeredGrid.extent(
                maxCrossAxisExtent: 500,
                crossAxisSpacing: 20,
                mainAxisSpacing: 20,
                children: [
                  ...List.generate(
                    variant.priceRange.length,
                    (pindex) {
                      final priceRange = variant.priceRange[pindex];
                      return Row(
                        children: [
                          Container(
                              padding: const EdgeInsets.all(10),
                              decoration: BoxDecoration(
                                color: themeColor.withOpacity(.1),
                                shape: BoxShape.circle,
                              ),
                              child: Text('${pindex + 1}')),
                          const SizedBox(width: 10),
                          Expanded(
                            child: TextFormField(
                              controller: TextEditingController(
                                  text: priceRange.startQty),
                              onChanged: (value) {
                                priceRange.startQty = value;
                              },
                              decoration:
                                  inpDecor().copyWith(labelText: 'Start Qty'),
                            ),
                          ),
                          const SizedBox(width: 7),
                          Expanded(
                            child: TextFormField(
                              controller: TextEditingController(
                                  text: priceRange.endQty),
                              onChanged: (value) {
                                priceRange.endQty = value;
                              },
                              decoration:
                                  inpDecor().copyWith(labelText: 'End Qty'),
                            ),
                          ),
                          const SizedBox(width: 7),
                          Expanded(
                            child: TextFormField(
                              controller: TextEditingController(
                                  text: priceRange.price.toString()),
                              onChanged: (value) {
                                priceRange.price = num.tryParse(value) ?? 0;
                              },
                              decoration:
                                  inpDecor().copyWith(labelText: 'Price / pc'),
                            ),
                          ),
                          const SizedBox(width: 7),
                          IconButton(
                              onPressed: () {
                                variant.priceRange.removeAt(pindex);
                                setState(() {});
                              },
                              icon: const Icon(CupertinoIcons.delete))
                        ],
                      );
                    },
                  ),
                ],
              )
            ]
          ],
        ),
      ),
    );
  }
}
