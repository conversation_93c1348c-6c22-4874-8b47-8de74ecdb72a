import 'package:cached_network_image/cached_network_image.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wedding_super_admin/models/vendor.dart';
import 'package:wedding_super_admin/shared/const.dart';
import 'package:wedding_super_admin/shared/router.dart';
import 'package:wedding_super_admin/views/vendors/widgets/subcatcard.dart';
import '../../../controller/home_ctrl.dart';
import '../../../models/subcategories.dart';
import '../../../shared/firebase.dart';
import '../../../shared/methods.dart';
import '../../../shared/theme.dart';
import '../vendors.dart';

class VendorForm extends StatefulWidget {
  const VendorForm({super.key, this.vendorId});
  final String? vendorId;
  @override
  State<VendorForm> createState() => _VendorFormState();
}

class _VendorFormState extends State<VendorForm> {
  TextEditingController searchController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (hctrl) {
      VendorModel? vendor = hctrl.vendors
          .firstWhereOrNull((element) => element.docId == widget.vendorId);
      final subCategories = hctrl.subcategories
          .where(
              (element) => vendor?.subcatids.contains(element.docId) ?? false)
          .toList();
      return SingleChildScrollView(
        padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    if (hctrl.currentUserType != UserTypes.vendor) ...[
                      IconButton(
                          onPressed: () {
                            context.pop();
                          },
                          icon: const Icon(
                            CupertinoIcons.arrow_left,
                            color: themeColor,
                          )),
                      const SizedBox(width: 10),
                    ],
                    Tooltip(
                      message: 'Vendor',
                      child: Text(
                          capilatlizeFirstLetter(
                              vendor?.name.toLowerCase() ?? ""),
                          style: GoogleFonts.zcoolXiaoWei(
                              fontSize: 28, color: themeColor)),
                    ),
                    const SizedBox(width: 50),

                    // Tooltip(
                    //   message: vendor?.email,
                    //   child: IconButton(
                    //     style: TextButton.styleFrom(
                    //       shadowColor: Colors.transparent,
                    //       overlayColor: Colors.transparent,
                    //       surfaceTintColor: Colors.transparent,
                    //       padding: const EdgeInsets.all(0),
                    //       backgroundColor: themeColor.withOpacity(.1),
                    //     ),
                    //     onPressed: () {},
                    //     icon: const Icon(CupertinoIcons.mail_solid,
                    //         size: 20, color: themeColor),
                    //   ),
                    // ),
                    // const SizedBox(width: 15),
                    // Tooltip(
                    //   message: vendor?.phone,
                    //   child: IconButton(
                    //     style: TextButton.styleFrom(
                    //       shadowColor: Colors.transparent,
                    //       overlayColor: Colors.transparent,
                    //       surfaceTintColor: Colors.transparent,
                    //       padding: const EdgeInsets.all(0),
                    //       backgroundColor: themeColor.withOpacity(.1),
                    //     ),
                    //     onPressed: () {},
                    //     icon: const Icon(CupertinoIcons.phone_fill,
                    //         size: 20, color: themeColor),
                    //   ),
                    // ),
                    // const SizedBox(width: 15),
                    // Tooltip(
                    //   message: 'Edit',
                    //   child: IconButton(
                    //     style: TextButton.styleFrom(
                    //       shadowColor: Colors.transparent,
                    //       overlayColor: Colors.transparent,
                    //       surfaceTintColor: Colors.transparent,
                    //       padding: const EdgeInsets.all(0),
                    //       backgroundColor: themeColor.withOpacity(.1),
                    //     ),
                    //     onPressed: () async {
                    //       newVendorForm(context, vendor);
                    //     },
                    //     icon:
                    //         const Icon(Icons.edit, size: 20, color: themeColor),
                    //   ),
                    // ),
                  ],
                ),
                Row(
                  children: [
                    OutlinedButton.icon(
                        style: OutlinedButton.styleFrom(
                          backgroundColor: Colors.transparent,
                          elevation: 0,
                          foregroundColor: themeColor,
                          side: const BorderSide(color: themeColor),
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(4)),
                          // padding: const EdgeInsets.fromLTRB(5, 15, 10, 15),
                        ),
                        icon: const Icon(Icons.edit, size: 20),
                        onPressed: () async {
                          addEditVendorForm(context, vendor);
                        },
                        label: const Padding(
                          padding: EdgeInsets.symmetric(vertical: 8.0),
                          child: Text(
                            'Profile',
                            style: TextStyle(fontSize: 14, letterSpacing: 1),
                          ),
                        )),
                    if (hctrl.currentUserType != UserTypes.vendor) ...[
                      const SizedBox(width: 10),
                      ElevatedButton.icon(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: themeColor,
                            elevation: 0,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(4)),
                            // padding: const EdgeInsets.fromLTRB(5, 15, 10, 15),
                          ),
                          icon: const Icon(CupertinoIcons.add, size: 20),
                          onPressed: () {
                            subCatAddEditDialog(context, hctrl.subcategories,
                                subCategories.map((e) => e.docId).toList());
                          },
                          label: const Padding(
                            padding: EdgeInsets.symmetric(vertical: 8.0),
                            child: Text(
                              'Sub-Category',
                              style: TextStyle(fontSize: 14, letterSpacing: 1),
                            ),
                          )),
                    ]
                  ],
                )
              ],
            ),
            vendor != null
                ? Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 20),
                      Row(
                        children: [
                          Container(
                            height: 35,
                            padding: const EdgeInsets.symmetric(horizontal: 10),
                            decoration: BoxDecoration(
                                color: dashboardColor,
                                borderRadius: BorderRadius.circular(8)),
                            child: Row(
                              children: [
                                // const Icon(
                                //   CupertinoIcons.pen,
                                //   color: themeColor,
                                //   size: 20,
                                // ),
                                // const SizedBox(width: 10),
                                Text(vendor.createdAt
                                    .toDate()
                                    .convertToDDMMYY()),
                              ],
                            ),
                          ),
                          Spacer(),
                          ElevatedButton.icon(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: themeColor,
                              elevation: 0,
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(4)),
                              // padding: const EdgeInsets.fromLTRB(5, 15, 10, 15),
                            ),
                            // onPressed: () async {
                            //   try {
                            //     ShopifyStore shopifyStore = ShopifyStore.instance;

                            //     final resp = await shopifyStore.getAllProducts();
                            //     print(resp.length);
                            //     print(resp.first.toJson());

                            //     return;
                            //     var headers = {
                            //       // 'Content-Type': 'text/plain',
                            //       'Access-Control-Allow-Origin': '*',
                            //       'Access-Control-Allow-Methods': 'GET, POST',
                            //       "Access-Control-Allow-Headers": "X-Requested-With",

                            //       'Content-Type': 'application/json',
                            //       'Content': '<>',
                            //       'X-Shopify-Access-Token':
                            //           '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',
                            //       'Authorization':
                            //           'Bearer 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'
                            //     };
                            //     var request = await http.get(
                            //         Uri.parse(
                            //             'http://c031a7-d8.myshopify.com/admin/products/count.json'),
                            //         headers: headers);

                            //     print(request.body);
                            //   } catch (e) {
                            //     // TODO
                            //     debugPrint("Error fetching products/collections:");
                            //     debugPrint(e.toString());
                            //   }
                            // },
                            onPressed: () => context.push(Routes.vendorapi,
                                extra: hctrl.vendors.firstWhereOrNull(
                                    (element) =>
                                        element.docId == widget.vendorId)),
                            icon: Icon(
                              CupertinoIcons.doc,
                              size: 20,
                            ),
                            label: Padding(
                              padding:
                                  const EdgeInsets.symmetric(vertical: 8.0),
                              child: Text('Data Import',
                                  style: GoogleFonts.livvic(
                                      letterSpacing: 1.3,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500)),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 13),
                      Row(
                        children: [
                          Container(
                            height: 35,
                            padding: const EdgeInsets.symmetric(horizontal: 10),
                            decoration: BoxDecoration(
                                color: dashboardColor,
                                borderRadius: BorderRadius.circular(8)),
                            child: Row(
                              children: [
                                const Icon(
                                  CupertinoIcons.mail_solid,
                                  color: themeColor,
                                  size: 20,
                                ),
                                const SizedBox(width: 10),
                                Text(vendor.email),
                              ],
                            ),
                          ),
                          const SizedBox(width: 15),
                          Container(
                            height: 35,
                            padding: const EdgeInsets.symmetric(horizontal: 10),
                            decoration: BoxDecoration(
                                color: dashboardColor,
                                borderRadius: BorderRadius.circular(8)),
                            child: Row(
                              children: [
                                const Icon(
                                  CupertinoIcons.phone_fill,
                                  color: themeColor,
                                  size: 20,
                                ),
                                const SizedBox(width: 10),
                                Text(vendor.phone),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      subCategories.isNotEmpty
                          ? StaggeredGrid.extent(
                              maxCrossAxisExtent: 300,
                              mainAxisSpacing: 20,
                              crossAxisSpacing: 20,
                              children: [
                                ...List.generate(
                                  subCategories.length,
                                  (index) {
                                    final purSellData = vendor.purSalePerData
                                        .firstWhereOrNull((element) =>
                                            element.subCatId ==
                                            subCategories[index].docId);
                                    return InkWell(
                                      hoverColor: Colors.transparent,
                                      highlightColor: Colors.transparent,
                                      overlayColor:
                                          const WidgetStatePropertyAll(
                                              Colors.transparent),
                                      onTap: () {
                                        context.push(
                                            '${Routes.subcategory}/${subCategories[index].docId}',
                                            extra: widget.vendorId);
                                      },
                                      child: SubCatCard(
                                          vendorEdit: true,
                                          onTap: () async {
                                            showDialog(
                                              context: context,
                                              builder: (context) {
                                                bool loading = false;
                                                final purCtrl =
                                                    TextEditingController(
                                                        text: purSellData
                                                                ?.purchasePer
                                                                .toString() ??
                                                            '');
                                                final sellCtrl =
                                                    TextEditingController(
                                                        text: purSellData
                                                                ?.sellingPer
                                                                .toString() ??
                                                            '');
                                                return StatefulBuilder(builder:
                                                    (context, setState2) {
                                                  return AlertDialog(
                                                    backgroundColor:
                                                        Colors.white,
                                                    surfaceTintColor:
                                                        Colors.white,
                                                    title: const Text('Edit'),
                                                    content: Column(
                                                      mainAxisSize:
                                                          MainAxisSize.min,
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        Text(
                                                            "Sub-category: ${subCategories[index].name}",
                                                            style: GoogleFonts.livvic(
                                                                fontSize: 16,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w600)),
                                                        const SizedBox(
                                                            height: 15),
                                                        TextFormField(
                                                          controller: purCtrl,
                                                          decoration: inpDecor()
                                                              .copyWith(
                                                                  labelText:
                                                                      'Purchase Percentage'),
                                                        ),
                                                        const SizedBox(
                                                            height: 10),
                                                        TextFormField(
                                                          controller: sellCtrl,
                                                          decoration: inpDecor()
                                                              .copyWith(
                                                                  labelText:
                                                                      'Selling Percentage'),
                                                        ),
                                                      ],
                                                    ),
                                                    actions: loading
                                                        ? [
                                                            const Center(
                                                              child: SizedBox(
                                                                height: 25,
                                                                width: 25,
                                                                child:
                                                                    CircularProgressIndicator(
                                                                  strokeWidth:
                                                                      3.5,
                                                                ),
                                                              ),
                                                            )
                                                          ]
                                                        : [
                                                            TextButton(
                                                              onPressed:
                                                                  () async {
                                                                Navigator.of(
                                                                        context)
                                                                    .pop();
                                                              },
                                                              child: const Text(
                                                                  "Cancel"),
                                                            ),
                                                            ElevatedButton(
                                                              onPressed:
                                                                  () async {
                                                                if (purCtrl.text
                                                                        .trim()
                                                                        .isEmpty ||
                                                                    sellCtrl
                                                                        .text
                                                                        .trim()
                                                                        .isEmpty) {
                                                                  showErrorAppSnackBar(
                                                                      context,
                                                                      'Enter both percentage');
                                                                  setState2(() {
                                                                    loading =
                                                                        false;
                                                                  });
                                                                  return;
                                                                }
                                                                setState2(() {
                                                                  loading =
                                                                      true;
                                                                });
                                                                try {
                                                                  final data = {
                                                                    'purPercentage':
                                                                        num.tryParse(purCtrl
                                                                            .text
                                                                            .trim()),
                                                                    'sellPercentage':
                                                                        num.tryParse(sellCtrl
                                                                            .text
                                                                            .trim()),
                                                                  };
                                                                  await FBFireStore
                                                                      .vendors
                                                                      .doc(widget
                                                                          .vendorId)
                                                                      .update({
                                                                    'purSalePerData.${subCategories[index].docId}':
                                                                        data,
                                                                  });
                                                                  setState2(() {
                                                                    loading =
                                                                        false;
                                                                  });
                                                                  Navigator.of(
                                                                          context)
                                                                      .pop();
                                                                } on Exception catch (e) {
                                                                  debugPrint(e
                                                                      .toString());
                                                                  setState2(() {
                                                                    loading =
                                                                        false;
                                                                  });
                                                                }
                                                              },
                                                              child: const Text(
                                                                  "Save"),
                                                            ),
                                                          ],
                                                  );
                                                });
                                              },
                                            );
                                          },
                                          vendorDocId: vendor.docId,
                                          onSwitch: (value) async {
                                            final batch =
                                                FBFireStore.fb.batch();
                                            final productSnap =
                                                await FBFireStore.products
                                                    .where('vendorDocId',
                                                        isEqualTo:
                                                            widget.vendorId)
                                                    .where('subCatDocId',
                                                        isEqualTo:
                                                            subCategories[index]
                                                                .docId)
                                                    .get();
                                            for (var productData
                                                in productSnap.docs) {
                                              // final product =
                                              //     productData.data();
                                              batch.update(
                                                  FBFireStore.products
                                                      .doc(productData.id),
                                                  {'show': value});
                                            }
                                            batch.update(
                                                FBFireStore.subCategories.doc(
                                                    subCategories[index].docId),
                                                {'isActive': value});
                                            batch.commit();
                                            // setState(() {});
                                          },
                                          subCategory: subCategories[index],
                                          onRemove: () async {
                                            subCatRemoveDialog(
                                                context,
                                                subCategories,
                                                index,
                                                vendor.purSalePerData);
                                          }),
                                    );
                                  },
                                )
                              ],
                            )
                          : const Text('Sub-category not added')
                    ],
                  )
                : const Text("Vendor does not exist")
          ],
        ),
      );
    });
  }

  Future<dynamic> subCatAddEditDialog(BuildContext context,
      List<SubCategory> allsubCategories, List<String> subCategories) {
    bool loading = false;

    List<String> selectedSubCats = [];
    selectedSubCats.clear();
    selectedSubCats.addAll(subCategories);
    return showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(builder: (context, setState2) {
          return Dialog(
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            child: Container(
              padding: const EdgeInsets.all(15),
              constraints: const BoxConstraints(maxWidth: 500, maxHeight: 1000),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Text(
                      "Select Sub-category",
                      style: TextStyle(fontSize: 20),
                    ),
                    const SizedBox(height: 15),
                    SingleChildScrollView(
                      child: StaggeredGrid.extent(
                        maxCrossAxisExtent: 300,
                        mainAxisSpacing: 10,
                        crossAxisSpacing: 10,
                        children: [
                          ...List.generate(
                            allsubCategories.length,
                            (index) {
                              bool containIds =
                                  selectedSubCats.firstWhereOrNull((element) =>
                                          element ==
                                          allsubCategories[index].docId) !=
                                      null;
                              return Padding(
                                padding: EdgeInsets.only(
                                    bottom: index == allsubCategories.length - 1
                                        ? 0
                                        : 10.0),
                                child: ListTile(
                                  onTap: () {
                                    if (containIds) {
                                      selectedSubCats.removeWhere((element) =>
                                          element ==
                                          allsubCategories[index].docId);
                                    } else {
                                      selectedSubCats
                                          .add(allsubCategories[index].docId);
                                    }
                                    setState2(() {});
                                  },
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                      side: BorderSide(
                                          color: Colors.grey.shade400)),
                                  trailing: Checkbox(
                                      value: containIds,
                                      onChanged: (value) async {
                                        if (value == true) {
                                          selectedSubCats.add(
                                              allsubCategories[index].docId);
                                          setState2(() {});
                                        }
                                        if (value == false) {
                                          selectedSubCats.removeWhere(
                                              (element) =>
                                                  element ==
                                                  allsubCategories[index]
                                                      .docId);

                                          setState2(() {});
                                        }
                                      }),
                                  contentPadding:
                                      const EdgeInsets.symmetric(horizontal: 8),
                                  minVerticalPadding: 15,
                                  leading: Container(
                                    height: 43,
                                    width: 43,
                                    clipBehavior: Clip.antiAlias,
                                    decoration: const BoxDecoration(
                                        shape: BoxShape.circle),
                                    child: CachedNetworkImage(
                                      imageUrl: allsubCategories[index].image,
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                  title: Text(capilatlizeFirstLetter(
                                      allsubCategories[index].name)),
                                ),
                              );
                            },
                          )
                        ],
                      ),
                    ),
                    const SizedBox(height: 15),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: loading
                          ? [
                              const Center(
                                child: SizedBox(
                                  height: 25,
                                  width: 25,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2.5,
                                    color: themeColor,
                                  ),
                                ),
                              )
                            ]
                          : [
                              ElevatedButton(
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: themeColor,
                                    foregroundColor: Colors.white,
                                  ),
                                  onPressed: () async {
                                    if (loading) return;

                                    try {
                                      setState2(() {
                                        loading = true;
                                      });

                                      await FBFireStore.vendors
                                          .doc(widget.vendorId)
                                          .update({
                                        'subcatids': selectedSubCats,
                                      });

                                      setState2(() {
                                        loading = false;
                                      });
                                      if (context.mounted) {
                                        Navigator.of(context).pop();
                                        showAppSnackBar(
                                            context, 'Sub-categories updated');
                                        // ScaffoldMessenger.of(context).showSnackBar(snackBar);
                                      }
                                    } catch (e) {
                                      debugPrint(e.toString());
                                      showAppSnackBar(context, e.toString());
                                    }
                                  },
                                  child: const Text("Save")),
                              const SizedBox(width: 10),
                              ElevatedButton(
                                  onPressed: () {
                                    Navigator.of(context).pop();
                                  },
                                  child: const Text("Cancel"))
                            ],
                    )
                  ],
                ),
              ),
            ),
          );
        });
      },
    );
  }

  Future<dynamic> subCatRemoveDialog(
    BuildContext context,
    List<SubCategory> subCategories,
    int index,
    List<PurSellPerModel> purSellPerData,
  ) {
    return showDialog(
      context: context,
      builder: (context) {
        bool loading = false;
        return StatefulBuilder(builder: (context, setState2) {
          return AlertDialog(
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            title: const Text('Remove'),
            content: const Text("Are you sure you want to remove?"),
            actions:
                // loading
                //     ? [
                //         const Center(
                //           child: CircularProgressIndicator(
                //             strokeWidth: 2.5,
                //             color: themeColor,
                //           ),
                //         )
                //       ]
                //     :
                [
              TextButton(
                  onPressed: () async {
                    if (loading) return;

                    try {
                      setState2(() {
                        loading = true;
                      });
                      final tempList = purSellPerData
                          .where((element) =>
                              element.subCatId != subCategories[index].docId)
                          .toList();
                      final tempMap = <String, dynamic>{};
                      for (var element in tempList) {
                        tempMap[element.subCatId] = {
                          'purPercentage': element.purchasePer,
                          'sellPercentage': element.sellingPer
                        };
                      }
                      await FBFireStore.vendors.doc(widget.vendorId).update({
                        'subcatids':
                            FieldValue.arrayRemove([subCategories[index].docId])
                      });
                      await FBFireStore.vendors.doc(widget.vendorId).update({
                        'purSalePerData': tempMap,
                      });
                      setState2(() {
                        loading = false;
                      });
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                                content:
                                    Text("sub-category removed Successfully")));
                        Navigator.of(context).pop();
                      }
                    } on Exception catch (e) {
                      setState2(() {
                        loading = false;
                      });
                      debugPrint(e.toString());
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text("Facing Some Issue")));
                      }
                    }
                  },
                  child: const Text("Yes")),
              TextButton(
                  onPressed: () async {
                    Navigator.of(context).pop();
                  },
                  child: const Text("No")),
            ],
          );
        });
      },
    );
  }
}

/* 
class VendorForm extends StatefulWidget {
  const VendorForm({super.key, this.vendorId});
  final String? vendorId;
  @override
  State<VendorForm> createState() => _VendorFormState();
}

class _VendorFormState extends State<VendorForm> {
  VendorModel? vendor;
  List<SubCategory> subCategories = <SubCategory>[];
  List<ProductModel> filteredProductList = <ProductModel>[];
  List<ProductModel> allproducts = <ProductModel>[];
  TextEditingController searchController = TextEditingController();
  bool dataLoaded = false;
  SubCategory? selectedSubCategory;
  @override
  void initState() {
    super.initState();
    getAllProducts();
  }

  // getVendorData() async {
  //   final vendorSnap = await FBFireStore.vendors.doc(widget.vendorId).get();
  //   vendor = VendorModel.fromDocSnap(vendorSnap);
  //   setState(() {});
  // }

  getAllProducts() async {
    final allproductsSnap = await FBFireStore.products
        .where('vendorDocId', isEqualTo: widget.vendorId)
        .get();
    allproducts =
        allproductsSnap.docs.map((e) => ProductModel.fromSnap(e)).toList();
    filteredProductList.clear();
    filteredProductList.addAll(allproducts);
    dataLoaded = true;
    setState(() {});
  }

  getSearchData() {
    filteredProductList.clear();
    if (allproducts.isNotEmpty) {
      if (searchController.text.trim().isEmpty) {
        filteredProductList.addAll(allproducts);
      } else {
        filteredProductList.addAll(allproducts.where((product) => product.name
            .toLowerCase()
            .contains(searchController.text.toLowerCase().trim())));
      }
      setState(() {});
    }
  }

  getSubcategoryProduct() {
    filteredProductList.clear();
    if (selectedSubCategory == null) {
      filteredProductList.addAll(allproducts);
    }

    if (selectedSubCategory != null) {
      final filterList = allproducts
          .where((element) => element.subCatDocId == selectedSubCategory?.docId)
          .toList();
      filteredProductList.addAll(filterList);
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return dataLoaded
        ? GetBuilder<HomeCtrl>(
            builder: (hctrl) {
              vendor = hctrl.vendors.firstWhereOrNull(
                  (element) => element.docId == widget.vendorId);
              subCategories = hctrl.subcategories
                  .where((element) =>
                      vendor?.subcatids.contains(element.docId) ?? false)
                  .toList();
              return SingleChildScrollView(
                padding:
                    const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            IconButton(
                                onPressed: () {
                                  context.pop();
                                },
                                icon: const Icon(
                                  CupertinoIcons.arrow_left,
                                  color: themeColor,
                                )),
                            const SizedBox(width: 10),
                            Tooltip(
                              message: 'Vendor',
                              child: Text(
                                  capilatlizeFirstLetter(
                                      vendor?.name.toLowerCase() ?? ""),
                                  style: const TextStyle(
                                      fontSize: 28, color: themeColor)),
                            ),
                            const SizedBox(width: 50),
                            Tooltip(
                              message: vendor?.email,
                              child: IconButton(
                                style: TextButton.styleFrom(
                                  shadowColor: Colors.transparent,
                                  overlayColor: Colors.transparent,
                                  surfaceTintColor: Colors.transparent,
                                  padding: const EdgeInsets.all(0),
                                  backgroundColor: themeColor.withOpacity(.1),
                                ),
                                onPressed: () {},
                                icon: const Icon(CupertinoIcons.mail_solid,
                                    size: 20, color: themeColor),
                              ),
                            ),
                            const SizedBox(width: 15),
                            Tooltip(
                              message: vendor?.phone,
                              child: IconButton(
                                style: TextButton.styleFrom(
                                  shadowColor: Colors.transparent,
                                  overlayColor: Colors.transparent,
                                  surfaceTintColor: Colors.transparent,
                                  padding: const EdgeInsets.all(0),
                                  backgroundColor: themeColor.withOpacity(.1),
                                ),
                                onPressed: () {},
                                icon: const Icon(CupertinoIcons.phone_fill,
                                    size: 20, color: themeColor),
                              ),
                            ),
                            const SizedBox(width: 15),
                            Tooltip(
                              message: 'Edit',
                              child: IconButton(
                                style: TextButton.styleFrom(
                                  shadowColor: Colors.transparent,
                                  overlayColor: Colors.transparent,
                                  surfaceTintColor: Colors.transparent,
                                  padding: const EdgeInsets.all(0),
                                  backgroundColor: themeColor.withOpacity(.1),
                                ),
                                onPressed: () async {
                                  newVendorForm(context, vendor);
                                },
                                icon: const Icon(Icons.edit,
                                    size: 20, color: themeColor),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    vendor != null
                        ? Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const SizedBox(height: 20),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  SearchField(
                                    searchController: searchController,
                                    onChanged: (p0) async {
                                      getSearchData();
                                      // getSearchData();
                                    },
                                  ),
                                  const SizedBox(width: 15),
                                  // const Spacer(),
                                  Row(
                                    children: [
                                      ElevatedButton.icon(
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: themeColor,
                                          elevation: 0,
                                          foregroundColor: Colors.white,
                                          shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(4)),
                                          // padding: const EdgeInsets.fromLTRB(5, 15, 10, 15),
                                        ),
                                        onPressed: () async {
                                          ProductModel? res = await context
                                              .push('${Routes.product}/null',
                                                  extra: [
                                                widget.vendorId,
                                                selectedSubCategory?.docId
                                              ]);
                                          selectedSubCategory = null;
                                          filteredProductList.clear();
                                          filteredProductList
                                              .addAll(allproducts);
                                          if (res != null) {
                                            filteredProductList.add(res);
                                            setState(() {});
                                          }
                                        },
                                        icon: const Icon(
                                          CupertinoIcons.add,
                                          size: 20,
                                        ),
                                        label: Padding(
                                          padding: const EdgeInsets.symmetric(
                                              vertical: 8.0),
                                          child: Text(
                                              selectedSubCategory != null
                                                  ? 'Add ${capilatlizeFirstLetter(selectedSubCategory?.name ?? "")}'
                                                  : 'Add',
                                              style: const TextStyle(
                                                  fontSize: 14)),
                                        ),
                                      ),
                                      const SizedBox(width: 15),
                                      Container(
                                        constraints:
                                            const BoxConstraints(maxWidth: 300),
                                        child: DropdownButtonHideUnderline(
                                            child: DropdownButtonFormField<
                                                SubCategory>(
                                          decoration: inpDecor(),
                                          hint:
                                              const Text("Select sub-category"),
                                          value: selectedSubCategory,
                                          items: [
                                            ...List.generate(
                                              subCategories.length,
                                              (index) {
                                                return DropdownMenuItem<
                                                        SubCategory>(
                                                    value: subCategories[index],
                                                    child: Text(
                                                        capilatlizeFirstLetter(
                                                            subCategories[index]
                                                                .name)));
                                              },
                                            )
                                          ],
                                          onChanged: (value) {
                                            selectedSubCategory = value;
                                            getSubcategoryProduct();
                                            setState(() {});
                                          },
                                        )),
                                      ),
                                      if (selectedSubCategory != null)
                                        TextButton(
                                            onPressed: () {
                                              selectedSubCategory = null;
                                              getSubcategoryProduct();
                                              setState(() {});
                                            },
                                            child: const Text("Clear"))
                                    ],
                                  ),
                                ],
                              ),
                              const SizedBox(height: 25),
                              const ProductTableHeader(),
                              if (filteredProductList.isNotEmpty)
                                ...List.generate(
                                  filteredProductList.length,
                                  (index) {
                                    return InkWell(
                                      highlightColor: Colors.transparent,
                                      overlayColor:
                                          const WidgetStatePropertyAll(
                                              Colors.transparent),
                                      hoverColor: Colors.transparent,
                                      onTap: () async {
                                        ProductModel? res = await context.push(
                                            '${Routes.product}/${filteredProductList[index].docId}',
                                            extra: [
                                              widget.vendorId,
                                              selectedSubCategory?.docId
                                            ]);

                                        if (res != null) {
                                          filteredProductList
                                              .removeWhere((element) {
                                            return element.docId == res.docId;
                                          });
                                          filteredProductList.insert(
                                              index, res);
                                        }
                                        setState(() {});
                                      },
                                      child: ProductTile(
                                          onCheckbox: (p0) async {
                                            await FBFireStore.products
                                                .doc(filteredProductList[index]
                                                    .docId)
                                                .update({
                                              'topSelling': p0,
                                            });
                                            filteredProductList[index]
                                                .topSelling = p0;
                                            setState(() {});
                                          },
                                          onSwitch: (p0) async {
                                            await FBFireStore.products
                                                .doc(filteredProductList[index]
                                                    .docId)
                                                .update({
                                              'available': p0,
                                            });
                                            filteredProductList[index]
                                                .available = p0;
                                            setState(() {});
                                          },
                                          onDelete: () async {
                                            bool res = await showDialog(
                                              context: context,
                                              builder: (context) {
                                                bool loading = false;
                                                return StatefulBuilder(
                                                  builder:
                                                      (context, setState2) {
                                                    return AlertDialog(
                                                      title:
                                                          const Text("Alert"),
                                                      content: const Text(
                                                          "Are you sure you want to delete"),
                                                      actions: loading
                                                          ? [
                                                              const Center(
                                                                child: SizedBox(
                                                                  height: 25,
                                                                  width: 25,
                                                                  child:
                                                                      CircularProgressIndicator(
                                                                    strokeWidth:
                                                                        2.5,
                                                                  ),
                                                                ),
                                                              )
                                                            ]
                                                          : [
                                                              TextButton(
                                                                  onPressed:
                                                                      () async {
                                                                    try {
                                                                      setState2(
                                                                          () {
                                                                        loading =
                                                                            true;
                                                                      });
                                                                      try {
                                                                        await FBFireStore
                                                                            .products
                                                                            .doc(filteredProductList[index].docId)
                                                                            .delete();
                                                                        filteredProductList
                                                                            .removeAt(index);
                                                                      } on Exception catch (e) {
                                                                        debugPrint(
                                                                            e.toString());
                                                                      }

                                                                      if (context
                                                                          .mounted) {
                                                                        Navigator.of(context)
                                                                            .pop(true);
                                                                        setState2(
                                                                            () {
                                                                          loading =
                                                                              false;
                                                                        });
                                                                      }
                                                                    } catch (e) {
                                                                      debugPrint(
                                                                          e.toString());

                                                                      if (context
                                                                          .mounted) {
                                                                        setState2(
                                                                            () {
                                                                          loading =
                                                                              false;
                                                                        });
                                                                        Navigator.of(context)
                                                                            .pop(false);
                                                                      }
                                                                    }
                                                                  },
                                                                  child:
                                                                      const Text(
                                                                          'Yes')),
                                                              TextButton(
                                                                  onPressed:
                                                                      () {
                                                                    Navigator.of(
                                                                            context)
                                                                        .pop();
                                                                  },
                                                                  child:
                                                                      const Text(
                                                                          'No')),
                                                            ],
                                                    );
                                                  },
                                                );
                                              },
                                            );
                                            setState(() {});
                                            if (res && context.mounted) {
                                              const snackBar = SnackBar(
                                                  content: Text(
                                                      "Product deleted successfully"));
                                              ScaffoldMessenger.of(context)
                                                  .showSnackBar(snackBar);
                                            } else if (!res &&
                                                context.mounted) {
                                              const snackBar = SnackBar(
                                                  content: Text(
                                                      "Error deleting product"));
                                              ScaffoldMessenger.of(context)
                                                  .showSnackBar(snackBar);
                                            }
                                          },
                                          index: index,
                                          productModel:
                                              filteredProductList[index]),
                                    );
                                  },
                                ),
                              if (filteredProductList.isEmpty)
                                const Padding(
                                  padding: EdgeInsets.only(top: 20.0),
                                  child: Center(
                                      child: Text("No Products to Display")),
                                )
                            ],
                          )
                        : const Text("No Vendor to Display"),

                    // Column(
                    //   children: [
                    //     const FormHeaderTile(
                    //       title: 'Tags',
                    //     ),
                    //     const SizedBox(height: 8),
                    //     _tagsDetails(),
                    //   ],
                    // ),
                    // const SizedBox(height: 20),
                    // _subCat(),
                  ],
                ),
              );
            },
          )
        : const Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Center(child: CircularProgressIndicator()),
            ],
          );
  }

  // Future<dynamic> _editVendorDialog(BuildContext context) {
  //   return showDialog(
  //     context: context,
  //     builder: (context) {
  //       return AlertDialog(
  //         title: Text("Edit vendor"),
  //         content: Column(
  //           children: [],
  //         ),
  //       );
  //     },
  //   );
  // }

  Column _subCat(DocumentReference<Map<String, dynamic>> vendorDocRef,
      List<SubCategory> allsubCategories) {
    List<SubCategory> vendorSubCats = allsubCategories
        .where((element) => vendor?.subcatids.contains(element.docId) ?? false)
        .toList();
    return Column(
      children: [
        const FormHeaderTile(title: 'Choose Sub Categories'),
        const SizedBox(height: 20),

        Autocomplete<SubCategory>(
          displayStringForOption: (option) =>
              capilatlizeFirstLetter(option.name),
          fieldViewBuilder:
              (context, textEditingController, focusNode, onFieldSubmitted) {
            return TextFormField(
              controller: textEditingController,
              onFieldSubmitted: (value) {
                onFieldSubmitted();
                textEditingController.clear();
              },
              focusNode: focusNode,
              decoration:
                  inpDecor().copyWith(labelText: ' Enter sub-category name'),
            );
          },
          onSelected: (option) async {
            await vendorDocRef.update({
              'subCatIds': FieldValue.arrayUnion([option.docId])
            });
            // textEditingController.clear();
          },
          optionsBuilder: (textEditingValue) {
            final subcategories = allsubCategories
                .where((element) => element.name
                    .toLowerCase()
                    .contains(textEditingValue.text.toLowerCase()))
                .toList();
            return subcategories;
          },
        ),
        const SizedBox(height: 25),

        StaggeredGrid.extent(
          maxCrossAxisExtent: 300,
          mainAxisSpacing: 20,
          crossAxisSpacing: 20,
          children: [
            ...List.generate(
              vendorSubCats.length,
              (index) {
                // print(vendorSubCats[index].name);
                return Stack(
                  children: [
                    InkWell(
                      onTap: () {
                        context.push(Routes.products, extra: [
                          vendorSubCats[index].docId,
                          widget.vendorId
                        ]);
                      },
                      hoverColor: Colors.transparent,
                      highlightColor: Colors.transparent,
                      splashColor: Colors.transparent,
                      child: Container(
                        clipBehavior: Clip.antiAlias,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(7),
                          color: Colors.white,
                          boxShadow: const [
                            BoxShadow(
                              color: Colors.black26,
                              offset: Offset(0, 2),
                              blurRadius: 5,
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CachedNetworkImage(
                              imageUrl: vendorSubCats[index].image,
                              height: 200,
                              width: double.maxFinite,
                              fit: BoxFit.cover,
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    capilatlizeFirstLetter(
                                        vendorSubCats[index].name),
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                    style: const TextStyle(
                                        fontWeight: FontWeight.w600,
                                        fontSize: 15.5),
                                  ),
                                ),
                                const SizedBox(width: 10),
                              ],
                            ),
                            const SizedBox(height: 8),
                          ],
                        ),
                      ),
                    ),
                    Align(
                      alignment: Alignment.topRight,
                      child: Padding(
                        padding: const EdgeInsets.only(right: 8.0, top: 8),
                        child: InkWell(
                          onTap: () async {
                            showDialog(
                              context: context,
                              builder: (context) {
                                return AlertDialog(
                                  title: const Text('Remove'),
                                  content: const Text(
                                      "Are you sure you want to remove?"),
                                  actions: [
                                    TextButton(
                                        onPressed: () async {
                                          try {
                                            await vendorDocRef.update({
                                              'subCatIds':
                                                  FieldValue.arrayRemove([
                                                vendorSubCats[index].docId
                                              ])
                                            });
                                            if (context.mounted) {
                                              ScaffoldMessenger.of(context)
                                                  .showSnackBar(const SnackBar(
                                                      content: Text(
                                                          "Sub-Category Removed Successfully")));
                                              Navigator.of(context).pop();
                                            }
                                          } on Exception catch (e) {
                                            debugPrint(e.toString());
                                            if (context.mounted) {
                                              ScaffoldMessenger.of(context)
                                                  .showSnackBar(const SnackBar(
                                                      content: Text(
                                                          "Facing Some Issue")));
                                            }
                                          }
                                        },
                                        child: const Text("Yes")),
                                    TextButton(
                                        onPressed: () async {
                                          Navigator.of(context).pop();
                                        },
                                        child: const Text("No")),
                                  ],
                                );
                              },
                            );
                          },
                          child: Container(
                            padding: const EdgeInsets.all(3),
                            decoration: const BoxDecoration(
                                color: Colors.white,
                                shape: BoxShape.circle,
                                boxShadow: [
                                  BoxShadow(
                                      color: Colors.black26,
                                      blurRadius: 2,
                                      // spreadRadius: 0,
                                      spreadRadius: 1,
                                      offset: Offset(0, 2)),
                                ]),
                            child: const Icon(
                              CupertinoIcons.xmark,
                              size: 18,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ],
        ),

        // Autocomplete<SubCategory>(
        //   optionsBuilder: (TextEditingValue textEditingValue) {
        //     if (textEditingValue.text == '') {
        //       return const Iterable<SubCategory>.empty();
        //     }
        //     return HomeCtrl().subcategories.where((element) {
        //       return element.name.contains(textEditingValue.text);
        //     });
        //   },
        //   onSelected: (option) =>
        //       {_updateSubCategory(vendorDocRef, option.docId)},
        // ),
        // getVendorData()
      ],
    );
  }

  // _updateSubCategory(DocumentReference<Map<String, dynamic>> vendorDocRef,
  //     String SubCatId) async {
  //   List<String> SubCatIds = vendor!.subCatIds;
  //   if (!SubCatIds.contains(SubCatId) || SubCatIds.isEmpty) {
  //     SubCatIds.add(SubCatId);
  //     vendorDocRef.update({'subCatIds': SubCatIds});
  //   }
  // }

  Column _vendorDetails(DocumentReference<Map<String, dynamic>> vendorDocRef) {
    final vendorNameCtrl = TextEditingController();
    final vendorNumberCtrl = TextEditingController();
    final vendorEmailCtrl = TextEditingController();
    final vendorAddrCtrl = TextEditingController();

    //bool showonHomePage = mainCategory?.showonHomePage ?? false;
    vendorNameCtrl.text = vendor?.name.toUpperCase() ?? '';
    vendorNumberCtrl.text = vendor?.phone ?? '';
    vendorEmailCtrl.text = vendor?.email ?? '';
    vendorAddrCtrl.text = vendor?.address ?? '';
    return Column(
      children: [
        const FormHeaderTile(title: 'Vendor Details'),
        const SizedBox(height: 20),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: vendorNameCtrl,
                onFieldSubmitted: (value) async {
                  await vendorDocRef.update({
                    'name': value,
                  });
                  // getVendorData();
                },
                cursorHeight: 20,
                decoration: inpDecor().copyWith(labelText: 'Vendor Name'),
              ),
            ),
            const SizedBox(width: 15),
            Expanded(
              child: TextFormField(
                controller: vendorNumberCtrl,
                onFieldSubmitted: (value) async {
                  await vendorDocRef.update({
                    'phone': value,
                  });
                  // getVendorData();
                },
                cursorHeight: 20,
                decoration: inpDecor().copyWith(labelText: 'Number'),
              ),
            ),
          ],
        ),
        const SizedBox(height: 15),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: vendorEmailCtrl,
                onFieldSubmitted: (value) async {
                  await vendorDocRef.update({
                    'email': value,
                  });
                  // getVendorData();
                },
                cursorHeight: 20,
                decoration: inpDecor().copyWith(labelText: 'Email'),
              ),
            ),
            const SizedBox(width: 15),
            Expanded(
              child: TextFormField(
                controller: vendorAddrCtrl,
                onFieldSubmitted: (value) async {
                  await vendorDocRef.update({
                    'address': value,
                  });
                  // getVendorData();
                },
                cursorHeight: 20,
                decoration: inpDecor().copyWith(labelText: 'Address'),
              ),
            ),
          ],
        ),
        const SizedBox(height: 15),
      ],
    );
  }
}
 */
