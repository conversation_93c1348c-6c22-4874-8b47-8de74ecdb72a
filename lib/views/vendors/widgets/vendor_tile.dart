import 'package:cloud_functions/cloud_functions.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:wedding_super_admin/models/vendor.dart';
import '../../../shared/firebase.dart';
import '../../../shared/methods.dart';
import '../../../shared/theme.dart';

class VendorTile extends StatefulWidget {
  const VendorTile(
      {super.key,
      required this.index,
      this.vendorModel,
      this.onEdit,
      required this.isLast});
  final int index;
  final VendorModel? vendorModel;
  final Function()? onEdit;
  final bool isLast;
  @override
  State<VendorTile> createState() => _VendorTileState();
}

class _VendorTileState extends State<VendorTile> {
  @override
  Widget build(BuildContext context) {
    return widget.vendorModel != null
        ? Container(
            decoration: BoxDecoration(
                border: Border(
                    bottom: BorderSide(color: dividerColor),
                    left: BorderSide(color: dividerColor),
                    right: BorderSide(color: dividerColor)),
                borderRadius: widget.isLast
                    ? BorderRadius.only(
                        bottomLeft: Radius.circular(8),
                        bottomRight: Radius.circular(8))
                    : null),
            child: Row(
              children: [
                SizedBox(
                    width: 80,
                    child: Text((widget.index + 1).toString(),
                        textAlign: TextAlign.center)),
                const SizedBox(width: 5),

                Expanded(
                    child: Text(capilatlizeFirstLetter(
                        widget.vendorModel?.name ?? ""))),
                const SizedBox(width: 5),

                // Expanded(child: Text(widget.vendorModel?.vendorName ?? "")),
                // const SizedBox(width: 5),

                // Expanded(flex: 2, child: Text(property?.email ?? "")),
                Expanded(child: Text(widget.vendorModel?.phone ?? "")),
                const SizedBox(width: 5),

                Expanded(child: Text(widget.vendorModel?.email ?? "")),
                const SizedBox(width: 5),
                Expanded(
                    child: Text(widget.vendorModel?.lastUpdatedOn
                            .toDate()
                            .convertToDDMMYY() ??
                        "")),
                const SizedBox(width: 5),

                SizedBox(
                  width: 60,
                  child: IconButton(
                    highlightColor: Colors.transparent,
                    hoverColor: Colors.transparent,
                    onPressed: widget.onEdit,
                    icon: const Icon(
                      Icons.edit,
                      color: themeColor,
                      size: 22,
                    ),
                  ),
                ),

                SizedBox(
                  width: 60,
                  child: Transform.scale(
                    scale: .65,
                    child: CupertinoSwitch(
                      value: widget.vendorModel!.isActive,
                      onChanged: (value) async {
                        await FBFireStore.vendors
                            .doc(widget.vendorModel?.docId)
                            .update({'isActive': value});
                      },
                    ),
                  ),
                ),
                SizedBox(
                  width: 60,
                  child: IconButton(
                    highlightColor: Colors.transparent,
                    hoverColor: Colors.transparent,
                    onPressed: () {
                      showDialog(
                        context: context,
                        builder: (context) {
                          bool loading = false;
                          return StatefulBuilder(
                            builder: (context, setState2) {
                              return AlertDialog(
                                backgroundColor: Colors.white,
                                surfaceTintColor: Colors.white,
                                title: const Text("Alert"),
                                content: const Text(
                                    "Are you sure you want to delete"),
                                actions: loading
                                    ? [
                                        const Center(
                                          child: SizedBox(
                                            height: 25,
                                            width: 25,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2.5,
                                            ),
                                          ),
                                        )
                                      ]
                                    : [
                                        TextButton(
                                            onPressed: () async {
                                              if (loading) return;

                                              try {
                                                setState2(() {
                                                  loading = true;
                                                });
                                                HttpsCallableResult<
                                                        Map<String, bool>> res =
                                                    await FBFunctions.ff
                                                        .httpsCallable(testMode
                                                            ? 'deleteVendorTest'
                                                            : 'deleteVendor')
                                                        .call({
                                                  'uid':
                                                      widget.vendorModel?.docId,
                                                });

                                                if (res.data['success']
                                                    as bool) {
                                                  const snackBar = SnackBar(
                                                      content: Text(
                                                          "Vendor deleted successfully"));
                                                  if (context.mounted) {
                                                    Navigator.of(context).pop();
                                                    setState2(() {
                                                      loading = false;
                                                    });
                                                    ScaffoldMessenger.of(
                                                            context)
                                                        .showSnackBar(snackBar);
                                                  }
                                                } else {
                                                  const snackBar = SnackBar(
                                                      content: Text(
                                                          "Something went wrong"));
                                                  setState2(() {
                                                    loading = false;
                                                  });
                                                  ScaffoldMessenger.of(context)
                                                      .showSnackBar(snackBar);
                                                }
                                              } catch (e) {
                                                debugPrint(e.toString());

                                                if (context.mounted) {
                                                  setState2(() {
                                                    loading = false;
                                                  });
                                                  Navigator.of(context).pop();
                                                }
                                              }
                                              // await FBFireStore.vendors
                                              //     .doc(widget.vendorModel?.docId)
                                              //     .delete();
                                            },
                                            child: const Text('Yes')),
                                        TextButton(
                                            onPressed: () {
                                              Navigator.of(context).pop();
                                            },
                                            child: const Text('No')),
                                      ],
                              );
                            },
                          );
                        },
                      );

                      /*   showDialog(
                        context: context,
                        builder: (context) {
                          bool loading = false;
                          return StatefulBuilder(
                            builder: (context, setState2) {
                              return AlertDialog(
                                title: const Text("Alert"),
                                content: const Text(
                                    "Are you sure you want to delete"),
                                actions: loading
                                    ? [
                                        const Center(
                                          child: SizedBox(
                                            height: 25,
                                            width: 25,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2.5,
                                            ),
                                          ),
                                        )
                                      ]
                                    : [
                                        TextButton(
                                            onPressed: () async {
                                              try {
                                                setState2(() {
                                                  loading = true;
                                                });
                                                try {
                                            
                                                } on Exception catch (e) {
                                                  debugPrint(e.toString());
                                                }
                                                const snackBar = SnackBar(
                                                    content: Text(
                                                        "Vendor deleted successfully"));
                                                if (context.mounted) {
                                                  Navigator.of(context).pop();
                                                  setState2(() {
                                                    loading = false;
                                                  });
                                                  ScaffoldMessenger.of(context)
                                                      .showSnackBar(snackBar);
                                                }
                                              } catch (e) {
                                                debugPrint(e.toString());

                                                if (context.mounted) {
                                                  setState2(() {
                                                    loading = false;
                                                  });
                                                  Navigator.of(context).pop();
                                                }
                                              }
                                              // await FBFireStore.vendors
                                              //     .doc(widget.vendorModel?.docId)
                                              //     .delete();
                                            },
                                            child: const Text('Yes')),
                                        TextButton(
                                            onPressed: () {
                                              Navigator.of(context).pop();
                                            },
                                            child: const Text('No')),
                                      ],
                              );
                            },
                          );
                        },
                      );
                     */
                    },
                    icon: const Icon(Icons.delete,
                        // color: Colors.redAccent,
                        color: themeColor),
                  ),
                ),
              ],
            ),
          )
        : const SizedBox();
  }
}
