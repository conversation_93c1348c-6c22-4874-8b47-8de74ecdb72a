import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../shared/firebase.dart';
import '../../../shared/router.dart';

class DrawerLogout extends StatelessWidget {
  const DrawerLogout({
    super.key,
    this.tab = false,
  });
  final bool tab;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      borderRadius: BorderRadius.circular(8),
      onTap: false
          ? () async {
              await FBFireStore.subCategories
                  .doc('Ej13IWt1s32iKtbEjOFl')
                  .delete();
              final data = await FBFireStore.variants
                  .where('subCatId', isEqualTo: 'Ej13IWt1s32iKtbEjOFl')
                  .get();
              for (var element in data.docs) {
                await FBFireStore.variants.doc(element.id).delete();
              }
              final data2 = await FBFireStore.products
                  .where('subCatDocId', isEqualTo: 'Ej13IWt1s32iKtbEjOFl')
                  .get();
              for (var element in data2.docs) {
                await FBFireStore.products.doc(element.id).delete();
              }
              print("----------done");
            }
          : () {
              showDialog(
                  context: context,
                  builder: (BuildContext context) => AlertDialog(
                        backgroundColor: Colors.white,
                        surfaceTintColor: Colors.white,
                        title: const Text('Logout'),
                        content: const Text('Are you sure you want to leave?'),
                        actions: [
                          TextButton(
                              onPressed: () async {
                                await FBAuth.auth.signOut();
                                if (context.mounted) context.go(Routes.signin);
                              },
                              child: const Text('Yes')),
                          TextButton(
                              onPressed: () => context.pop(),
                              child: const Text('No')),
                        ],
                      ));
            },
      child: Container(
        decoration: BoxDecoration(
            color: Colors.transparent,
            // color: selected ? themeColor.withOpacity(0.1) : Colors.transparent,
            borderRadius: BorderRadius.circular(8)),
        padding: const EdgeInsets.all(10),
        width: 250,
        child: const Row(
          children: [
            Icon(size: 22, Icons.logout_outlined, color: null),
            SizedBox(width: 15),
            Text(
              'Logout',
              style: TextStyle(
                  fontSize: 15,
                  letterSpacing: 1.4,
                  fontWeight: FontWeight.w400,
                  color: Colors.black),
            ),
          ],
        ),
      ),
    );
  }

  // Row _row() {
  //   return Row(
  //     mainAxisSize: MainAxisSize.min,
  //     mainAxisAlignment: MainAxisAlignment.center,
  //     children: [
  //       const Icon(
  //         CupertinoIcons.square_arrow_right,
  //         size: 18,
  //       ),
  //       Padding(
  //         padding: const EdgeInsets.only(left: 8.0),
  //         child: Text(
  //           'Logout',
  //           style: GoogleFonts.livvic(fontSize: 16, letterSpacing: 1),
  //         ),
  //       ),
  //     ],
  //   );
  // }

  // Column _column() {
  //   return Column(
  //     mainAxisSize: MainAxisSize.min,
  //     mainAxisAlignment: MainAxisAlignment.center,
  //     children: [
  //       const Icon(CupertinoIcons.square_arrow_right, size: 20),
  //       Padding(
  //         padding: const EdgeInsets.only(top: 2.0),
  //         child: Text(
  //           'Logout',
  //           style: GoogleFonts.livvic(fontSize: 16),
  //         ),
  //       ),
  //     ],
  //   );
  // }
}

class NoUserLogout extends StatelessWidget {
  const NoUserLogout({
    super.key,
    this.tab = false,
  });
  final bool tab;

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: () => showDialog(
          context: context,
          builder: (BuildContext context) => AlertDialog(
                title: const Text('Logout'),
                content: const Text('Are you sure you want to logout?'),
                actions: [
                  TextButton(
                      onPressed: () async {
                        await FBAuth.auth.signOut();
                        if (context.mounted) context.go(Routes.signin);
                      },
                      child: const Text('Yes')),
                  TextButton(
                      onPressed: () => context.pop(), child: const Text('No')),
                ],
              )),
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 30),
        // shadowColor: Colors.transparent,
        overlayColor: Colors.transparent,
        // backgroundColor: Colors.grey.shade300,
        // backgroundColor: tableHeaderColor,
        // backgroundColor: tableHeaderColor.withOpacity(.4),

        foregroundColor: const Color.fromARGB(233, 0, 0, 0),
        // shadowColor: tableHeaderColor,\
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
      ),
      child: tab
          ? Padding(
              padding: const EdgeInsets.symmetric(vertical: 5.0),
              child: _column(),
            )
          : _row(),
    );
  }

  Row _row() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Icon(
          CupertinoIcons.square_arrow_right,
          size: 18,
        ),
        Padding(
            padding: const EdgeInsets.only(left: 8.0),
            child: Text(
              'Logout',
              style: GoogleFonts.livvic(fontSize: 16, letterSpacing: 1),
            ))
      ],
    );
  }

  Column _column() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Icon(CupertinoIcons.square_arrow_right, size: 20),
        Padding(
          padding: const EdgeInsets.only(top: 2.0),
          child: Text(
            'Logout',
            style: GoogleFonts.livvic(fontSize: 16),
          ),
        ),
      ],
    );
  }
}
