// Test script to verify the new embedding structure with separate collections
const admin = require('firebase-admin');

// Initialize Firebase Admin
admin.initializeApp({
  credential: admin.credential.applicationDefault(),
  projectId: 'wedding-ease-dc99a'
});

const db = admin.firestore();

async function testNewEmbeddingStructure() {
  try {
    console.log('Creating test documents to verify new embedding structure...');
    
    // Create a new test product
    const productRef = await db.collection('products').add({
      name: 'Elegant Wedding Rings',
      description: 'Beautiful gold wedding rings with diamond accents',
      category: 'Jewelry',
      image: 'https://example.com/rings.jpg',
      price: 1500,
      createdAt: admin.firestore.FieldValue.serverTimestamp()
    });
    
    console.log('New test product created with ID:', productRef.id);
    
    // Create a new test variant
    const variantRef = await db.collection('variants').add({
      name: 'Diamond Ring - 18K Gold',
      title: 'Premium Diamond Wedding Ring',
      description: 'Exquisite 18K gold ring with premium diamonds',
      image: 'https://example.com/diamond-ring.jpg',
      attributes: {
        material: '18K Gold',
        stone: 'Diamond',
        size: '7',
        style: 'Classic',
        carat: '1.5'
      },
      price: 2000,
      productId: productRef.id,
      createdAt: admin.firestore.FieldValue.serverTimestamp()
    });
    
    console.log('New test variant created with ID:', variantRef.id);
    
    // Create a new test subcategory
    const subcategoryRef = await db.collection('subcategories').add({
      name: 'Bridal Jewelry',
      category: 'Accessories',
      description: 'Elegant jewelry pieces for the bride',
      image: 'https://example.com/bridal-jewelry.jpg',
      createdAt: admin.firestore.FieldValue.serverTimestamp()
    });
    
    console.log('New test subcategory created with ID:', subcategoryRef.id);
    console.log('Waiting for embedding generation...');
    
    // Wait and then check all embedding collections
    setTimeout(async () => {
      try {
        console.log('\n=== CHECKING EMBEDDING COLLECTIONS ===');
        
        // Check products embedding collection
        const productsEmbeddingSnapshot = await db.collection('productsembedding')
          .where('productId', '==', productRef.id)
          .get();
        
        if (!productsEmbeddingSnapshot.empty) {
          const productEmbeddingDoc = productsEmbeddingSnapshot.docs[0];
          const productEmbeddingData = productEmbeddingDoc.data();
          
          console.log('\n=== PRODUCT EMBEDDING RESULTS ===');
          console.log('Embedding document ID:', productEmbeddingDoc.id);
          console.log('Product name:', productEmbeddingData.name);
          console.log('Product image:', productEmbeddingData.image);
          console.log('Original product ID:', productEmbeddingData.productId);
          console.log('Has embedding:', !!productEmbeddingData.embedding);
          console.log('Embedding length:', productEmbeddingData.embedding ? productEmbeddingData.embedding.length : 0);
          console.log('Embedding generated:', productEmbeddingData.embeddingGenerated);
        } else {
          console.log('\n❌ No product embedding found');
        }
        
        // Check variants embedding collection
        const variantsEmbeddingSnapshot = await db.collection('variantsembedding')
          .where('variantId', '==', variantRef.id)
          .get();
        
        if (!variantsEmbeddingSnapshot.empty) {
          const variantEmbeddingDoc = variantsEmbeddingSnapshot.docs[0];
          const variantEmbeddingData = variantEmbeddingDoc.data();
          
          console.log('\n=== VARIANT EMBEDDING RESULTS ===');
          console.log('Embedding document ID:', variantEmbeddingDoc.id);
          console.log('Variant name:', variantEmbeddingData.name);
          console.log('Variant image:', variantEmbeddingData.image);
          console.log('Original variant ID:', variantEmbeddingData.variantId);
          console.log('Has embedding:', !!variantEmbeddingData.embedding);
          console.log('Embedding length:', variantEmbeddingData.embedding ? variantEmbeddingData.embedding.length : 0);
          console.log('Embedding generated:', variantEmbeddingData.embeddingGenerated);
        } else {
          console.log('\n❌ No variant embedding found');
        }
        
        // Check subcategories embedding collection
        const subcategoriesEmbeddingSnapshot = await db.collection('subcategoriesembedding')
          .where('subcategoryId', '==', subcategoryRef.id)
          .get();
        
        if (!subcategoriesEmbeddingSnapshot.empty) {
          const subcategoryEmbeddingDoc = subcategoriesEmbeddingSnapshot.docs[0];
          const subcategoryEmbeddingData = subcategoryEmbeddingDoc.data();
          
          console.log('\n=== SUBCATEGORY EMBEDDING RESULTS ===');
          console.log('Embedding document ID:', subcategoryEmbeddingDoc.id);
          console.log('Subcategory name:', subcategoryEmbeddingData.name);
          console.log('Subcategory image:', subcategoryEmbeddingData.image);
          console.log('Original subcategory ID:', subcategoryEmbeddingData.subcategoryId);
          console.log('Has embedding:', !!subcategoryEmbeddingData.embedding);
          console.log('Embedding length:', subcategoryEmbeddingData.embedding ? subcategoryEmbeddingData.embedding.length : 0);
          console.log('Embedding generated:', subcategoryEmbeddingData.embeddingGenerated);
        } else {
          console.log('\n❌ No subcategory embedding found');
        }
        
        // Check original documents for status
        const [productDoc, variantDoc, subcategoryDoc] = await Promise.all([
          productRef.get(),
          variantRef.get(),
          subcategoryRef.get()
        ]);
        
        console.log('\n=== ORIGINAL DOCUMENTS STATUS ===');
        console.log('Product embedding generated:', productDoc.data().embeddingGenerated);
        console.log('Variant embedding generated:', variantDoc.data().embeddingGenerated);
        console.log('Subcategory embedding generated:', subcategoryDoc.data().embeddingGenerated);
        
      } catch (error) {
        console.error('Error checking embedding collections:', error);
      }
      process.exit(0);
    }, 15000); // Wait 15 seconds
    
  } catch (error) {
    console.error('Error creating test documents:', error);
    process.exit(1);
  }
}

testNewEmbeddingStructure();
